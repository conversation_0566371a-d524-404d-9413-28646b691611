import { ConnectionPositionPair, HorizontalConnectionPos, VerticalConnectionPos } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
    ChangeToolEventData,
    DefaultToolBar,
    FEATURE_SELECTION,
    ToolEventListener,
    VEventListener,
    ViewportContentEvent,
    ViewportManager,
} from '@viclass/editor.core';
import {
    GeoCreateDocumentTool,
    GeometryEditor,
    GeometryTool,
    GeometryToolBar,
    GeometryToolType,
    GeoToolEventData,
    RenameElementTool,
} from '@viclass/editor.geo';
import { EditorUIComponent, EditorUILoaderComponent } from '@viclass/editorui.loader';
import { BehaviorSubject, Observable, ReplaySubject, Subscription } from 'rxjs';
import { CommandtoolComponent } from './commandtool/commandtool.component';
import { ButtonData, CommunicationEvent, GeoEditorControllerEvent } from './geometrytools.models';
import { GeoToolbarBtnStateService } from './geotoolbar-btn-state.service';

@Component({
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'tb-geometrytools',
    templateUrl: './geometrytools.component.html',
    styleUrls: ['../../assets/geo.style.scss'],
})
export class GeometrytoolsComponent implements OnDestroy, EditorUIComponent {
    private notifier: ReplaySubject<CommunicationEvent<GeoEditorControllerEvent>>;
    private readonly notifySubscription: Subscription;

    protected vAlign: 'top' | 'center' | 'bottom';
    protected hAlign: 'left' | 'center' | 'right';
    protected direction: 'ltr' | 'rtl' | 'btt' | 'ttb';

    tb: GeometryToolBar;

    show$: BehaviorSubject<boolean> = new BehaviorSubject(false);

    geometryBtnsData: ButtonData<GeometryToolType>[] = [];
    geometryBtnsDataForCommandTool: ButtonData<GeometryToolType>[] = [];

    focusFillColor$: Observable<boolean>;
    focusLineWeight$: Observable<boolean>;
    focusStrokeStyle$: Observable<boolean>;
    focusSetting$: Observable<boolean>;

    contentEventListener: VEventListener<ViewportContentEvent>;

    constructor(
        public dialog: MatDialog,
        private cdr: ChangeDetectorRef,
        public btnStateSv: GeoToolbarBtnStateService
    ) {
        this.notifier = new ReplaySubject<CommunicationEvent<GeoEditorControllerEvent>>();
        this.notifySubscription = this.notifier.subscribe(event => this.handleEvents(event));

        this.geometryBtnsData = this.btnStateSv.getInitialGeometryButtonsData();
        this.geometryBtnsDataForCommandTool = this.btnStateSv.getInitialGeometryButtonsDataForCommandTool();
        this.focusLineWeight$ = this.btnStateSv.isFocusPath('LineWeight');
        this.focusStrokeStyle$ = this.btnStateSv.isFocusPath('StrokeStyle');
        this.focusSetting$ = this.btnStateSv.isFocusPath('Setting');
        this.isToolActive = this.isToolActive.bind(this);
        this.isToolAllow = this.isToolAllow.bind(this);
    }

    ngOnDestroy(): void {
        this.notifySubscription?.unsubscribe();
    }

    get createTool(): GeoCreateDocumentTool | undefined {
        return this.tb.getTool('CreateDocumentTool') as GeoCreateDocumentTool;
    }

    get renameElementTool(): RenameElementTool | undefined {
        return this.tb.getTool('RenameElementTool') as RenameElementTool;
    }

    onToolbarBtnSwitch(btnData: ButtonData<GeometryToolType>) {
        this.switchTool(btnData.name);
        this.cdr.detectChanges();
    }

    onToolbarBtnSwitchForCommandTool(btnData: ButtonData<GeometryToolType>) {
        if (btnData.key == null) return;
        this.openCmdDialog(btnData);
        this.cdr.detectChanges();
    }

    showFloatingUI() {
        const ed = this.tb.editor as GeometryEditor;
        if (ed.geoEditorConf.docViewMode === 'full-viewport') return false;
        return true;
    }

    openCmdDialog(btnData: ButtonData<GeometryToolType> = null): void {
        this.dialog.open(CommandtoolComponent, {
            panelClass: 'command-tool',
            hasBackdrop: false,
            data: { toolbar: this.tb, tool: btnData, viewportId: this.viewport },
        });
    }

    hasTool(tool: GeometryToolType) {
        return this.tb && this.tb.getTool(tool) != null;
    }

    private getTool(tool: GeometryToolType): GeometryTool<any> {
        return this.tb.getTool(tool);
    }

    private get viewport(): string {
        return this.tb.viewport.id;
    }

    get isHorizontal(): boolean {
        return this.direction === 'ltr' || this.direction === 'rtl';
    }

    get isVertical(): boolean {
        return this.direction === 'ttb' || this.direction === 'btt';
    }

    get subMenuPositions(): ConnectionPositionPair[] {
        const horizontalConnectionPos: HorizontalConnectionPos = 'center';
        const verticalConnectionPos: VerticalConnectionPos = 'center';

        let offsetX = 0;
        let offsetY = 0;

        if (this.isHorizontal) {
            if (this.vAlign === 'top') {
                offsetY = 50;
            } else {
                offsetY = -50;
            }
        } else if (this.isVertical) {
            if (this.hAlign === 'right') {
                offsetX = -50;
            } else {
                offsetX = 50;
            }
        }

        return [
            new ConnectionPositionPair(
                {
                    originX: horizontalConnectionPos,
                    originY: verticalConnectionPos,
                },
                {
                    overlayX: horizontalConnectionPos,
                    overlayY: verticalConnectionPos,
                },
                offsetX,
                offsetY
            ),
        ];
    }

    connectToolbar<T extends DefaultToolBar<any, any>>(toolbar: T) {
        if (!toolbar) return;
        this.tb = toolbar as any as GeometryToolBar;
        toolbar.registerToolListener(GeometrytoolsComponent.GeometryToolListener(this));

        if (!this.contentEventListener && toolbar.viewport) {
            // meaning the toolbar is attached to viewport before connecting to UI
            this.contentEventListener = new GeometrytoolsComponent._contentEventListener(this);
            toolbar.viewport.contentEventEmitter.registerListener(this.contentEventListener);
        }
    }

    focusAble(toolType: GeometryToolType): boolean {
        return !this.tb.isDisabled() && !this.tb.isToolDisable(toolType);
    }

    switchTool(toolType: GeometryToolType) {
        if (this.tb.isDisabled() || this.tb.isToolDisable(toolType)) return;

        this.notifier.next({
            source: this,
            eventType: 'switch-tool',
            eventData: toolType,
        });
    }

    isToolAllow(tool: GeometryToolType): boolean {
        if (!this.focusAble(tool)) return false;
        return this.getTool(tool)?.focusAble(this.viewport);
    }

    isToolActive(tool: GeometryToolType): boolean {
        return this.tb.isToolActive(tool);
    }

    disableUI() {}

    hideUI() {
        this.tb?.clearAllFocus();
        this.show$.next(false);
        this.cdr.markForCheck();
    }

    showUI() {
        this.show$.next(true);
        this.cdr.markForCheck();
    }

    isShowing(): boolean {
        return this.show$.value;
    }

    loadedBy(uiLoader: EditorUILoaderComponent) {
        this.vAlign = uiLoader.vAlign;
        this.hAlign = uiLoader.hAlign;
        this.direction = uiLoader.direction;
    }

    private handleEvents(event: CommunicationEvent<GeoEditorControllerEvent>) {
        switch (event.eventType) {
            case 'switch-tool': {
                const toolType = event.eventData as GeometryToolType;
                if (!this.tb.isToolActive(toolType)) this.tb.focus(toolType);
                else this.tb.blur(toolType);

                break;
            }
            default:
                break;
        }
    }

    /**
     * Listen for content changes on viewport and check for changes on the UI
     */
    private static _contentEventListener = class implements VEventListener<ViewportContentEvent> {
        constructor(private cmp: GeometrytoolsComponent) {}
        onEvent(eventData: ViewportContentEvent): ViewportContentEvent | Promise<ViewportContentEvent> {
            if (eventData.state && eventData.state.source === FEATURE_SELECTION)
                if (this.cmp.isShowing()) this.cmp.cdr.markForCheck();

            return eventData;
        }
        onUnregister?: () => void;
    };

    private static GeometryToolListener(
        _p: GeometrytoolsComponent
    ): ToolEventListener<GeometryToolBar, GeometryToolType> {
        return new (class implements ToolEventListener<GeometryToolBar, GeometryToolType> {
            onEvent(eventData: GeoToolEventData): GeoToolEventData {
                if (_p.tb.isDisabled()) {
                    _p.cdr.markForCheck();
                    return eventData;
                }

                const changeEvent = eventData as ChangeToolEventData<GeometryToolBar, GeometryToolType>;
                if (changeEvent.toolType === 'RenameElementTool' && changeEvent?.changes?.has('validateResult')) {
                    const toolState = _p.renameElementTool.toolState;
                    //TODO: show message error input name
                    if (!toolState.validateResult.valid) {
                        console.error('fail to rename', toolState.validateResult.message);
                    }
                }

                if (eventData.eventType === 'toolbar-viewport-attach' && !_p.contentEventListener) {
                    _p.contentEventListener = new GeometrytoolsComponent._contentEventListener(_p);
                    eventData.source.viewport.contentEventEmitter.registerListener(_p.contentEventListener);
                } else if (eventData.eventType === 'toolbar-viewport-detach') {
                    (eventData.state as ViewportManager).contentEventEmitter.unregisterListener(
                        _p.contentEventListener
                    );
                    _p.contentEventListener = undefined;
                }

                _p.cdr.markForCheck();
                return eventData;
            }
        })();
    }
}
