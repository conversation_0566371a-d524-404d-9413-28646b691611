package common.libs.codec

import org.bson.BsonReader
import org.bson.BsonType
import org.bson.BsonWriter
import org.bson.codecs.Codec
import org.bson.codecs.DecoderContext
import org.bson.codecs.EncoderContext

class TwoIntArrayCodec: Codec<Array<IntArray>> {
    override fun encode(writer: BsonWriter, value: Array<IntArray>, encoderContext: EncoderContext) {
        writer.writeStartArray()
        for (array in value) {
            writer.writeStartArray()
            for (element in array) {
                writer.writeInt32(element)
            }
            writer.writeEndArray()
        }
        writer.writeEndArray()
    }

    override fun decode(reader: BsonReader, decoderContext: DecoderContext): Array<IntArray> {
        reader.readStartArray()
        val list: MutableList<IntArray> = ArrayList()
        while (reader.readBsonType() != BsonType.END_OF_DOCUMENT) {
            reader.readStartArray()
            val sublist: MutableList<Int> = ArrayList()
            while (reader.readBsonType() != BsonType.END_OF_DOCUMENT) {
                sublist.add(reader.readInt32())
            }
            reader.readEndArray()
            list.add(sublist.stream().mapToInt { obj: Int -> obj.toInt() }.toArray())
        }
        reader.readEndArray()
        return list.toTypedArray()
    }

    override fun getEncoderClass(): Class<Array<IntArray>> {
        return Array<IntArray>::class.java
    }
}
