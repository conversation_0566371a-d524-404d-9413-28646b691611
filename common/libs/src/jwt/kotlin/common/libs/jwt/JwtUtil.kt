package common.libs.jwt

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions.JWTDecodeException
import com.auth0.jwt.exceptions.JWTVerificationException
import com.auth0.jwt.interfaces.DecodedJWT
import java.text.SimpleDateFormat
import java.util.*


data class JwtUtil(private val jwtConfig: JwtConfig) {

    private val claimName: String = this.jwtConfig.claimName
    private val algorithm = Algorithm.HMAC256(this.jwtConfig.secret)
    private val expirationTime = this.jwtConfig.expiresInMinutes * 60 * 1000

    /**
     * extract jwt token
     */
    fun verify(token: String): DecodedJWT {
        try {
            return JWT.require(algorithm).build().verify(token)
        } catch (e: JWTDecodeException) {
            throw e
        } catch (e: JWTVerificationException) {
            throw e
        }
    }

    /**
     * generate token by claimName in config file
     */
    fun create(claimValue: String): String {
        return JWT.create()
            .withClaim(claimName, claimValue)
            .withExpiresAt(Date(System.currentTimeMillis() + expirationTime))
            .sign(algorithm)
    }

    /**
     * get claim value by claimName in config file
     */
    fun getClaimValueFromToken(token: String): String {
        return verify(token).getClaim(claimName).asString()
    }

    /**
     * get expiration time formatted, example: "HH:mm dd/MM/yyyy"
     */
    fun getExpirationTimeFormat(token: String, pattern: String): String {
        // Format the Date object to a string
        val sdf = SimpleDateFormat(pattern)
        sdf.timeZone = TimeZone.getDefault()

        return sdf.format(
            JWT.decode(token).expiresAt
        )
    }
}