package common.libs.captcha

import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.request.forms.*
import io.ktor.http.*

class CaptchaUtil(private val client: HttpClient, private val captchaConf: CaptchaConf ) {
    private val captchaUrl = this.captchaConf.captchaUrl
    private val captchaSecretKey = this.captchaConf.captchaSecretKey
    private val siteKey = this.captchaConf.siteKey

    suspend fun isVerified(responseToken: String): Boolean {
        val captchaVerifyRes = client.submitForm(url = captchaUrl, formParameters = parameters {
            append("response", responseToken)
            append("secret", captchaSecretKey)
        })

        val captchaRes: CaptchaRes = captchaVerifyRes.body()
        return captchaRes.success
    }

    fun getSiteKey(): String {
        return this.siteKey
    }
}