package common.libs.cache

import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import io.lettuce.core.api.sync.RedisCommands
import java.util.concurrent.TimeUnit

/**
 * Base class for the cache services that uses Redis as the cache backend
 */
abstract class RedisBaseCacheService<K, V>(
    private val redisCommands: RedisCommands<K, V>,
) : ICacheService<K, V> {
    /**
     * Expiration time in seconds
     */
    abstract val expiration: Long

    val maximumSize: Long = 1000

    /**
     * Get the cache key to be used in Redis
     */
    abstract fun getCacheKey(key: K): K

    private val cache = lazy {
        CacheBuilder.newBuilder().expireAfterWrite(expiration, TimeUnit.SECONDS).maximumSize(maximumSize)
            .build(object : CacheLoader<K, V>() {
                override fun load(key: K): V {
                    return redisCommands.get(key) // Load from Redis when not found in memory
                }
            })
    }

    override fun get(key: K): V? {
        return try {
            cache.value.getUnchecked(getC<PERSON><PERSON><PERSON>(key))
        } catch (e: Throwable) {
            null // Returns null if failed to load from both memory and redis
        }
    }

    override fun set(key: K, value: V) {
        val cacheKey = getCacheKey(key)
        cache.value.put(cacheKey, value)
        redisCommands.setex(cacheKey, expiration, value)
    }

    override fun delete(key: K) {
        val cacheKey = getCacheKey(key)
        cache.value.invalidate(cacheKey!!)
        redisCommands.del(cacheKey)
    }

    override fun contains(key: K): Boolean {
        return cache.value.getIfPresent(getCacheKey(key)!!) != null
    }
}