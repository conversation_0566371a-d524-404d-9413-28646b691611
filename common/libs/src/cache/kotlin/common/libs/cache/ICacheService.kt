package common.libs.cache

/**
 * Cache service interface
 */
interface ICacheService<K, V> {
    /**
     * Get the value from the cache
     */
    fun get(key: K): V?
    /**
     * Set the value in the cache
     */
    fun set(key: K, value: V)
    /**
     * Delete the value from the cache
     */
    fun delete(key: K)
    /**
     * Check if the key is in the cache
     */
    fun contains(key: K): <PERSON><PERSON>an
}