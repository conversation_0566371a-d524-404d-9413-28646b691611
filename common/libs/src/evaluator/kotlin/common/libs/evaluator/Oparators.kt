package common.libs.evaluator

import kotlin.math.*

val operators: Map<String, (List<Double>) -> Double> = mapOf(
    // Algebra
    "Add" to { args -> applyToAllArgs(args) { a, b -> a + b } },
    "Subtract" to { args -> applyToAllArgs(args) { a, b -> a - b } },
    "Multiply" to { args -> applyToAllArgs(args) { a, b -> a * b } },
    "Divide" to { args -> applyToAllArgs(args) { a, b -> a / b } },
    "Negate" to { args -> onlyOneArg(args) { a -> -a } },

    // Arithmetic
    "Mod" to { args -> applyToAllArgs(args) { a, b -> a % b } },
    "Power" to { args -> applyToAllArgs(args) { a, b -> a.pow(b) } },
    "Root" to { args -> applyToAllArgs(args) { a, b -> a.pow(1.0 / b) } },
    "Sqrt" to { args -> onlyOneArg(args) { a -> sqrt(a) } },
    "Square" to { args -> onlyOneArg(args) { a -> a * a } },
    "Rational" to { args -> applyToAllArgs(args) { a, b -> a / b } },

    // Exponential and Logarithmic
    "Exp" to { args -> onlyOneArg(args) { a -> exp(a) } },
    "Ln" to { args -> onlyOneArg(args) { a -> ln(a) } },
    "Lb" to { args -> onlyOneArg(args) { a -> ln(a) / ln(2.0) } },    // Log base 2
    "Lg" to { args -> onlyOneArg(args) { a -> log10(a) } },              // Log base 10
    "Log" to { args -> applyToAllArgs(args) { a, b -> ln(a) / ln(b) } },  // Log with custom base
    "LogOnePlus" to { args -> onlyOneArg(args) { a -> ln1p(a) } },       // log(1 + x) for numerical stability

    // Comparison
    "Max" to { args -> args.maxOrNull() ?: throw IllegalArgumentException("Empty list") },
    "Min" to { args -> args.minOrNull() ?: throw IllegalArgumentException("Empty list") },

    // Utility
    "Abs" to { args -> onlyOneArg(args) { a -> abs(a) } },
    "Ceil" to { args -> onlyOneArg(args) { a -> ceil(a) } },
    "Floor" to { args -> onlyOneArg(args) { a -> floor(a) } },
    "Round" to { args -> onlyOneArg(args) { a -> Math.round(a).toDouble() } },

    // Trigonometric
    "Sin" to { args -> onlyOneArg(args) { a -> sin(a) } },
    "Cos" to { args -> onlyOneArg(args) { a -> cos(a) } },
    "Tan" to { args -> onlyOneArg(args) { a -> tan(a) } },
    "Arcsin" to { args -> onlyOneArg(args) { a -> asin(a) } },
    "Arccos" to { args -> onlyOneArg(args) { a -> acos(a) } },
    "Arctan" to { args -> onlyOneArg(args) { a -> atan(a) } },
    "Sinh" to { args -> onlyOneArg(args) { a -> sinh(a) } },
    "Cosh" to { args -> onlyOneArg(args) { a -> cosh(a) } },
    "Tanh" to { args -> onlyOneArg(args) { a -> tanh(a) } }
)

//val relations: Map<String, (Double, Double) -> Boolean> = mapOf(
//    // Comparison
//    "Equal" to { a, b -> a == b },
//    "NotEqual" to { a, b -> a != b },
//    "Greater" to { a, b -> a > b },
//    "GreaterEqual" to { a, b -> a >= b },
//    "Less" to { a, b -> a < b },
//    "LessEqual" to { a, b -> a <= b }
//)

fun applyToAllArgs(args: List<Double>, callback: (Double, Double) -> Double): Double {
    if (args.size <= 1) throw IllegalArgumentException("Must have at least 2 arguments")
    var result = args[0]

    for ((index, arg) in args.withIndex()) {
        if (index == 0) continue
        result = callback(result, arg)
    }

    return result
}

fun onlyOneArg(args: List<Double>, callback: (Double) -> Double): Double {
    if (args.size != 1) throw IllegalArgumentException("Must have only 1 arguments")
    return callback(args[0])
}