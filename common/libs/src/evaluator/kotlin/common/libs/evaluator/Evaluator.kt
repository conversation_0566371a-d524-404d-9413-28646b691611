package common.libs.evaluator

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper

data class ExtractorContext<T>(
    val extractor: (operator: List<JsonNode>) -> T,
)

data class EvalContext(
    val supportExtractorOps: Set<String> = emptySet(),
    /**
     * Extractor for handle custom operators
     */
    val extractor: ((List<JsonNode>) -> Double?)?,
    /**
     * Scope variable. Ex: { x: 0, y: 1 }
     */
    val scope: Map<String, Double> = emptyMap(),
)

class Evaluator {
    private val mapper = jacksonObjectMapper()

    fun evaluate(expression: String, context: EvalContext? = null): Double {
        val rootNode: JsonNode = mapper.readTree(expression)
        return evaluateNode(rootNode, context, 0)
    }

    fun extractRootOperator(expression: String, extractor: (List<JsonNode>) -> String?): String? {
        val rootNode: JsonNode = try {
            mapper.readTree(expression)
        } catch (ex: JsonProcessingException) {
            return expression
        }

        if (rootNode.isArray) return extractor.invoke(rootNode.toList())
        return null
    }

    private fun evaluateNode(node: JsonNode, context: EvalContext?, depth: Int = 0): Double {
        if (depth > 1000) throw RuntimeException("Evaluate over the depth limit")

        if (node.isNumber) return node.asDouble()

        if (node.isTextual) {
            val strVal = node.asText()
            if (constants.containsKey(strVal)) return constants[strVal]!!
            if (context?.scope?.containsKey(strVal) == true) return context.scope[strVal]!!

            throw IllegalArgumentException("Unknown variable '$strVal'")
        }

        if (node.isArray) {
            val arr = node.asArray()
            if (arr.size() <= 1) throw IllegalArgumentException("Array node can not have 0 or 1 element")

            val oprNode = arr.get(0)
            if (!oprNode.isTextual) throw IllegalArgumentException("Operator name must be text")
            val operator = oprNode.asText()
            if (operator === "Error") throw RuntimeException("MathJSON with Error: ${mapper.writeValueAsString(arr)}")

            if (context?.supportExtractorOps?.contains(operator) == true) {
                return context.extractor?.invoke(arr.toList())
                    ?: throw IllegalArgumentException("Extract value failed from: ${mapper.writeValueAsString(arr)}")
            }

            if (operators.containsKey(operator)) {
                val operatorFunc = operators[operator]!!

                val args = arr.drop(1).map { evaluateNode(it, context, depth + 1) }
                return operatorFunc(args)
            }
        }

        throw IllegalArgumentException("Invalid type of node ${mapper.writeValueAsString(node)}")
    }
}

fun JsonNode.asArray(): ArrayNode {
    if (!this.isArray) {
        throw IllegalArgumentException("JsonNode is not an ArrayNode")
    }
    return this as ArrayNode
}