package common.libs.thread

import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicInteger

class ThreadFactoryWithName constructor(val name: String) : ThreadFactory {
    private val poolNumber: AtomicInteger = AtomicInteger(1)
    private var group: ThreadGroup? = null
    private val threadNumber = AtomicInteger(1)
    private var namePrefix: String? = null

    init {
        group = Thread.currentThread().threadGroup
        namePrefix = name + "-pool-" +
                poolNumber.getAndIncrement() + "-thread-"
    }

    override fun newThread(r: Runnable): Thread {
        val t = Thread(
            group, r,
            namePrefix + threadNumber.getAndIncrement(),
            0
        )
        if (t.isDaemon) t.isDaemon = false
        if (t.priority != Thread.NORM_PRIORITY) t.priority = Thread.NORM_PRIORITY
        return t
    }
}