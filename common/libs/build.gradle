plugins {
    id "hnct.build"
    id "kotlin"
}

dependencies {
    internal {
    }

    // dependencies codec
    codecImplementation "org.mongodb:bson:$mongoRxDriverVs"

    langImplementation "org.mongodb:bson:$mongoRxDriverVs"

    // dependencies for logback
    loggerImplementation "org.slf4j:slf4j-api:$slf4jVs"
    loggerImplementation "ch.qos.logback:logback-classic:$logbackVs"
    loggerImplementation "ch.qos.logback:logback-core:$logbackVs"

    // dependencies for jwtUtil
    jwtImplementation "com.auth0:java-jwt:$javaJwtVs"

    // dependencies for captchaUtil
    captchaImplementation "io.ktor:ktor-client-core:$ktorVs"
    captchaImplementation "io.ktor:ktor-http:$ktorVs"
    captchaImplementation "com.fasterxml.jackson.core:jackson-databind:$jacksonVs"

    evaluatorImplementation "com.fasterxml.jackson.module:jackson-module-kotlin:$jacksonVs"
    evaluatorImplementation "com.fasterxml.jackson.core:jackson-databind:$jacksonVs"

    // dependencies for cacheUtil
    cacheImplementation "io.lettuce:lettuce-core:$lettuceVs"
    cacheImplementation "com.google.guava:guava:$guavaVs"
}

group = 'viclass'
version = '1.0.0'