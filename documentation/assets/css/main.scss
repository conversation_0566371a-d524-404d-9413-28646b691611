---

---

@import "menu";
@import "syntaxhighlight";

body *::-webkit-scrollbar-track
{
	background-color: #fff;
}

body *::-webkit-scrollbar
{
	width: 3px;
    height:1px;
	background-color: #fff;
}

body *::-webkit-scrollbar-thumb
{
	background-color: #c2cfd6;
	border: 0px solid #777;
}

.sidebar {
    position: sticky;
    top: 0rem;
    height: calc(100vh - 0rem);
    overflow: auto;
}

.sidebar::-webkit-scrollbar {
    display: none;
}

.main-content {
    order: 1;
}

.page-toc {
    order: 2;
    position: sticky;
    top: 0rem;
    height: calc(100vh - 0rem);
    overflow: auto;
    font-size:85%;
}

.section-nav {
    padding-left: 0;
    border-left: 1px solid #eee;

    ul {
        padding-left: 1rem;
    }
}

.toc-entry {
    display: block;
}

.toc-entry a {
    display: block;
    padding: .125rem 1.5rem;
    color: #77757a;
}

.title {
    margin-bottom: 30px;
}

.table {
    code {
        word-break: unset !important;
    }
}

.page-image {
    max-width: 100%;
}
