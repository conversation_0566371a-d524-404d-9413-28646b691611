// ---- configs

const regexList = [
  {
    folder: "../../viclass/packages/backend/viclass/config.server",
  },
  { folder: "../../viclass/packages/backend/viclass/eb.word" },
  {
    folder: "../../viclass/packages/editors/viclass/editor.coordinator",
  },
  { folder: "../../viclass/packages/editors/viclass/editor.core" },
  {
    folder: "../../viclass/packages/editors/viclass/editor.freedrawing",
  },
  { folder: "../../viclass/packages/editors/viclass/editor.geo" },
  { folder: "../../viclass/packages/editors/viclass/editor.word" },
  { folder: "../../viclass/packages/editors/viclass/editor.word.transform" },
  {
    folder: "../../viclass/packages/editoruis/viclass/commontools",
    packageName: "editorui.commontools",
  },
  {
    folder: "../../viclass/packages/editoruis/viclass/editorui.freedrawing",
  },
  { folder: "../../viclass/packages/editoruis/viclass/editorui.geo" },
  { folder: "../../viclass/packages/editoruis/viclass/editorui.loader" },
  { folder: "../../viclass/packages/editoruis/viclass/editorui.word" },
  { folder: "../../viclass/packages/mfe/viclass/mfe" },
  { folder: "../../viclass/packages/mfe/viclass/ww" },
  {
    folder: "../../viclass/packages/portal/viclass/portal.classrooms",
  },
  { folder: "../../viclass/packages/portal/viclass/portal.common" },
  { folder: "../../viclass/packages/portal/viclass/portal.homepage" },
  { folder: "../../viclass/packages/protobuf/viclass/proto" },
  { folder: "../../viclass/packages/themes/viclass/themes" },
];
const pattern = /(from|^@use) \'@viclass\/(.+?)(|\/.+?)\'/g;
const exts = [".ts", ".js", ".scss", "sass"];

// ---- utils

const fs = require("fs");
const path = require("path");
const colors = [
  "#FF0000", // Red
  "#00FF00", // Green
  "#0000FF", // Blue
  "#FF00FF", // Magenta
  "#00FFFF", // Cyan
  "#FFA500", // Orange
  "#800080", // Purple
  "#008000", // Dark Green
  "#000080", // Navy
  "#800000", // Maroon
  "#FFC0CB", // Pink
  "#008080", // Teal
  "#FFD700", // Gold
  "#A52A2A", // Brown
  "#FF4500", // Orange Red
  "#FF1493", // Deep Pink
  "#00FF7F", // Spring Green
  "#1E90FF", // Dodger Blue
  "#FF69B4", // Hot Pink
];

// Function to recursively traverse a directory and find files with specific extensions
function getFiles(dir, fileTypes) {
  const files = [];
  function traverse(directory) {
    const contents = fs.readdirSync(directory);
    for (const item of contents) {
      const fullPath = path.join(directory, item);
      const stats = fs.statSync(fullPath);
      if (stats.isDirectory()) {
        traverse(fullPath);
      } else if (fileTypes.includes(path.extname(fullPath))) {
        files.push(fullPath);
      }
    }
  }
  traverse(dir);
  return files;
}

function objectToMermaid(obj) {
  let code = "flowchart LR\n";
  for (const [moduleName, dependencies] of Object.entries(obj).sort(
    (a, b) => a[1].length - b[1].length
  ))
    if (dependencies.length)
      for (const dependency of dependencies)
        code += `\t${moduleName} ---> ${dependency}\n`;
    else code += `\t${moduleName}\n`;
  code += "\n";
  let lineIndex = 0;
  for (const [_, dependencies] of Object.entries(obj))
    if (dependencies?.length)
      for (const [index, _] of dependencies.entries()) {
        code += `\tlinkStyle ${lineIndex} stroke-width:2px,fill:none,stroke:${
          colors[Math.floor(Math.random() * colors.length)]
        }\n`;
        lineIndex++;
      }
  return code;
}

// --- execute

const graph = {}; // for building dependency graph

regexList.forEach(({ folder, packageName = undefined }) => {
  const files = getFiles(folder, exts);
  const matches = {};

  autoName = folder.split("/").slice(-1)[0];
  packageName = packageName ?? autoName;

  let match;
  for (const file of files) {
    match = null;
    const content = fs.readFileSync(file, "utf8");
    while ((match = pattern.exec(content)) !== null)
      if (match[2]) matches[match[2]] = true;
  }

  // add to dependency graph
  graph[packageName] = Object.keys(matches);
});

// build dependency graph
const graphPath = path.join(__dirname, "dependency_graph.mmd");
fs.writeFileSync(graphPath, objectToMermaid(graph));

// log success
console.log(`\nResults written to: ${graphPath}\n`);
