---
title: Networking
---

VINet provides the protocols and infrastructure to allow multiple peers (browsers, backend services) to connect among themselves and synchronize classrooms' document states. VINet provides following services:

- Synchronization Managers which is responsible for the apis to create, delete, maintain states of synchronization rooms. It also perform signaling to allow different peers to exchange webrtc negotiation information. Synchronization Managers are implemented in Kotlin.
- Synchronizers which is responsible for synchronize data among different peers acoording to a predefined semantics. Synchronizers are implemented in Go, which provides server side Webrtc stack, call pion/webrtc

Since the networking is used as  a communication layer it must be usable in many use cases. We list a few use cases where the networking layer must satisfy. We also describe some protocols to ensure the networking layer is used accurately. This also serves as the guideline for implementing editors so that they are usable in the described use cases.

## Use cases

### Client create document through synchronization

### Client create document through REST API

### Client join class room

### Client edit document or document object inside a class room

### Client edito document or document object on a separate website

### Client display document or document object separately in a website

## How to achieve

Whenever working with a document, we have the following components:

- A coordinator
- An editor

The coordinator stores the context for working with the document. The editor provides the interaction and process the commands that modifies the document. When a document is created through synchronization, it is created first in the client and then synchronize to the server peer and the server peer then save it to the database. When a document is created through rest api, the server peer first creates the document on the server side then asynchronously sync the data to the client peer. 

The difficulties arise when the document is created in the client before sending to the server. This is because we wants optimal speed and hence we cannot obtain a global id from the server before synchronize 

Since document might be created in the server side or client side, depending on the editor type, for each document we define a local id and a global id. The global id is the id that identifies a particular document within the global database.


