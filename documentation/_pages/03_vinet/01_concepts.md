---
title: Concepts
---

### Synchronization room

Synchronization room is created by backend services such as LSessionService once a classroom is started. Any message sent to a synchronization room will be broadcasted to all peer inside the room, if the participant don't set any filter or set filter that allow the message to go through.

### Peer

A peer can be a browser from where the student observe the class, a backend service such as service responsible for an editor or a recorder service, etc...

### Filter

A filter decide if a message can go through to a certain peer. A peer can set a particular filter at the synchronizer so that it can choose certain message to receive. Each filter is tested against the meta data attached to the message. Since each editor can have its own message format, meta data is data that is attached on top of the encoded message and have predefined structure imposed by the Vinet library. Synchronizer when receives a message can do minimal decoding for filter testing.