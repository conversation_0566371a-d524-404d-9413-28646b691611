---
title: Problems not yet handled
---

There are a few problems not yet handled when creating vinet first version:

1. Versioning of documents. Very likely, a document, layer, object will have their own version. The version of document will be increased whenever the version of a layer or an object increased. The version of layer will be increased whenever the version of its objects increased.
2. Error handling. When an user cannot join the class, what happen, how to retry joining the class. When peer disconnected what to do.
3. Batching commands. Currently, editors send command without consideration of flooding the network connection with too many commands. Testing on local network may be fine, but with a peer with slow connection, this might be very in-efficient. Furthermore, for interactions that rapidly modifies the document object, batching modification command is necessary. For example, the pen tool of the freedrawing is a good example.
    
    A naive implementation will send a command to modify the object and, therefore, increase the object version, layer version and document version, on every mouse move, to the synchronizer.

    A better implementation will be a bit more complex. The pen tool need to have few more types of command to ensure messages can be synced incrementally and losing messages doesn't degrade user experiences.

4. When syncing message, the synchronizer need to ensure that messages are sent in order they arrive, this means that each peer needs to have a queue of message
5. We intend to have commands sent over two different channel, reliable and unreliable channels. The reliable channels are for commands that must be delivered, and the unreliable channel for commands that might be skipped without degrading the user experience too much. Take for example
    - Preview commands might be lost
    - Commmands that modify the document state must be delievered reliably.