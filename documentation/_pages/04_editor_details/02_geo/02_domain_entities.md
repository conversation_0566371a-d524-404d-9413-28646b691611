---
title: Domain Entities
---

## Constraint templates

A constraint template contains information that helps user specifies constraint easier:

- Number of parameters
- Type of each parameters
- The natural sentence where parameters can be filled in. For example, `altitude of [triangle] from [point]`, in this template, `triangle` and `point` are the place of parameters and the user needs to fill these in.
- List of keyword that can be used for auto completion
- The type of shape that this constraint can be used with

Some examples of constraint template

- `From [Point]` used with `Line`, `Ray`, `Line Segment`
- `Perpendicular to [Line]` used with `Line`, `Ray`, `Line Segment`
- `With Edge [Line Segment]` used with creating `Isosceles Triangle` with a known edge
- `With Base [Line Segment]` used with creating `Isosceles Triangle` with a known base
- `With Apex [Point]` used with creating `Isoceles Triangle` with a known apex
- `With Center [Point]` used with creating `Circle` with a known center
- `With Radius [Length]` used with creating `Circle`

Note that, the parameter a specified as string and a parameter extractor to extract that parameter from name will be used. For example, `[Length]` extractor can accept a string with two charaters each define a point or it can also accept a number.

## Constraint

A constraint entity stores 

- The constraint template type
- The actual parameters used as input to shape constructors

## Shape Constructor Template

Shape Constructor Template entities describe how shape constructors can be created. It contains following information:

- The type of the shape that this constructor support
- The constraint templates that this constructor needs in order to specify enough information for the creation

## Shape creation request

An element creation request tells the back end how to create the shape. It contains following things:

- The required type of the shape
- The name of the shape
- The constraints used in creating the shape

Note: Building the shape creation request is an interactive process. The user will have a lot of auto completion support to help select the correct constraint and parameters

## Modification point

Modification points are created by shape constructors when they create the shape. These points denote what the user can drag to modify the shape interactively. A modification point has a constrained movement path and a transformation function. A modification point is often a shape vertex itself. 

## Modification points' movement paths

A movement path constrains where a modification point can move. Since shapes are created based on constraints, their vertices in many cases can't move freely. For example

- A point created as a center of a line segment is not moveable.
- A line go through a point and tangent with a circle is only moveable between two possible tangent point.
- A point created with a fix distance to other point is moveable only on the circle with radius of the specified distance.
- An isosceles triangle created with a fixed side has the remaining vertex moveable on a circle with center at the apex and radius of the fix side.
- A free triangle has its vertices move freely.

A movement path can be expressed by some code that calculates the modification point position from the mouse position. Naturally, one of the way to represent a movement path is through math function and the way to calculate is projection of the mouse position onto that curve.

## Transformation function

A transformation function tells how the shape is transformed givement the new position of the modification point. Some examples of transformation functions are:

- Parallel resizing of triangles, quadrilaterals. This function is used when these shapes are constructed with all vertices free of constraints.
- Use modification point position. This function simply uses the current modification point position for the vertex position.

A transformation function takes in the modification point current position, its old position, and recalculate the shape base on the changes calculated. For example, for parallel resizing, all vertices are scaled up / down using the same ratio, which calculated as the distance from the new point to the center over the distance from the current point to the center.

# GeoDoc

A geo document stores information needed to render and modify the document. Specifically:

- It stores a list of shapes and the dependencies among the shapes. Each shape contains geometrical information such as vertices, edges, etc.. of the shape
- It stores a creation request for each shape to know how the shape is created so that when there is a modification due to user interaction, the dependent shapes can be recreated
- It stores all the interactive modification point or the degree of freedom of the shape created with each creation request. This allows user to interactively modify the created shape, which in turn, modify the whole geo document. For each modification point, it is attached with a movement path and a transformation function.