---
title: Implementation flows
---

This page list the flows of things happens based on user actions on the editor. The flow start from user actions and go through the different stage in front end, back end to illustrate how components work together.

## Flow 1: Create new document

## Flow 2: Start editing document

## Flow 3: Open shape creation form

## Flow 4: Select constraint

## Flow 5: Enter parameter

## Flow 6: Synchronize changes

## Flow 7: Select interactive creation tool

## Flow 8: Dragging modification point