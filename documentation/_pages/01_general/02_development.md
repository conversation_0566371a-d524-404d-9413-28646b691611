---
title: Setting up development environment
---

# Build code

## JVM based projects

Our JVM projects built using Gradle build tool. Projects are all included into the `settings.gradle`. This file will give you all the projects included and their project paths (which is important for running and compiling the projects, configuring dependencies among the projects)

This structure can be setup entirely using <PERSON>rad<PERSON> from scratch, however, for convenience, a plugin called `hnct.build` was written to help specifying project dependency, applying language plugin, distributing application, and publishing artifacts easier and more consistently. More information is at [here](https://gitlab.com/hnct.build/plugin)

All JVM projects keep their configuration at a `conf` folder inside the project directory. An example of the configuration is located at `conf-sample` folder which is version-controlled. The `conf` folders are not, since it contains the machine specific configuration. The configurations for each project might include the services the service depends on, database connections, among other things. Please refer to individual project for more details.

To run a backend project, JDK 1.8 or JRE 1.8 is needed. Gradle 6.x is needed. The command is:

For pure Kotlin project
```
gradle <project path> run
```

For Play project,
```
gradle <project path>:runPlay
```

The run commands normally will build the projects which also includes compiling and testing the code, and then it start the application. You can add watch mode for auto recompilation through adding `-t` flag to the run command.

# Development

To start development, you should first understand the dependencies among services, i.e. which service will call which services, how events are produced and consumed, the responsibilities of each service. Please refer to [General section](/01_general) and [Code organization](./01_code_org) for more details.

The development process typically involves fixing bugs or implementing feature on one or typically few (2-3) projects at the same time.