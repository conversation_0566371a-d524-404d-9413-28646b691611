---
title: Viewport and Layer creation in different context
---

Editors produce different kind of documents, each has its own requirements of how to attach it to the DOM under different use cases. To satisfy all use cases while keeping the editors independent of coordinators, we need to define clear invocation flow among components, thus suitable viewports and layers can be created for usage inside editors, without having to have customized code within each editor.

We have four usage scenarios for our editors at the time of writing:

- Classroom context
- Single editor
- Embedding display context
- Document editing context

Each of this context has a different types of viewports and hence different types layers. A layer is created when an editor need to add content to a viewport.

When we first implemented the classroom, the editors would create its own layer controller and add directly to the viewport. This is not suitable when there are more kinds of contexts and more ways of laying out documents. For example, math formula documents / objects might display inline within a normal webpage and don't need the foreign object to host its content like inside the SVG viewport of a classroom. 

To allow more flexibility in laying out the content on a web page, instead of letting the editors to create their own layer controllers, we will leave this responsibility to the viewport, and the editor will simply attach their content to the created layer. This will keep the editors from having to know all the possible layouts that a coordinator might want in each specific situation.

We define the following types document render:
- *Unbounded graphic* where the document is like an image but has no boundary and will always take all available viewport space.
- *Bounded graphic* where document is like an image and has specific boundary
- *Bounded inline* where document is supposed to be both render inline with text in normal HTML flow or within a board. Within this kind of render, an object is normally a text document, which can be rendered in single line (typically) or multiple lines, the size of this document stretch depending on the content inside.

For each of the above document render types and rendering context, we will have to define clearly the flow chart where document or part of the document are rendered within a viewport. The chart should be able to answer:
- How document editors coordinate with the coordinator?
- How the content is passed to the document editor from the coordinator?
- How viewports and layers are created and when?
- How document editors attach content to the layers?

Before going into details of each context, we should be firm about the related concepts.

## Concepts

Here, we describe clearly the responsibility segregation between editors and coordinators, and the purposes of having concepts like viewports, documents, layers, objects.

**Editor**

This is the centralize concept of our system. Each editor is a processing unit that receive user input and producing content. The content of editors is represented as documents and objects and are displayed inside viewports and layers.

**Coordinator**

This component is responsible for coordinate editors in a usage scenarios, which represents different ways we can combine the functionalities of editors with users' flow so to form a meaningful use cases. Take for example, the classroom coordinator.

**Viewport**

Viewports are places on the website that users can see content produced by editors. These viewports wrap around the content so that events on contents can be collected conveniently. The viewports also responsible for providing functionalities such as content zoom or pan, and the coordinate system to layout the content.

**Document**

A document is a unit of content produced by an editor. It contains many layers and each layer contains many objects. Document has a global id that together with the editor type identify it uniquely within the viclass's ecosystem. For performance reason, to improve the smoothness of document creation in certain case, edtior can generate a local id which is unique within a unique scenario.

**Layer**

Layer is the place holder of content on the viewport. It provide the root DOM element that editors can attach or render content into. We provide a few abstract layer types and the viewports will provide these layers to the editors. Editors attach renderers (a render function) to the layers which will be called when needed to re-render the content (e.g. when pan or zoom)

Each layer should have id unique within a document. We can understand layer as provided and layout-ed by the viewport but its id is used by the editor (within a document) to group the content within a document.

**Object**

This is the smallest unit of content produced by an editor, an object must be rendered on a single layer and cannot span multiple layers.

Each object should have an id and should be unique within a document.

## Context details

**Classroom context**

The viewport is always the board viewport, and we use SVG to host the content of the board, and because of this, the layer needs to have ForeignObject to contain elements which are not SVG.