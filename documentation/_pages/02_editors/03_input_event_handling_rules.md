---
title: Rules for input events handling
---

## Types of input events

We have keyboard events, mouse events, and in future, there can be touch events.

- Keyboard events: we divide them into two types
    - Global keyboard events: These are the events registered by certain tools which will be invoked regardless of whether they are active or not. Some common tools like zoom, pan, switching editors, etc.. have the need of listening to global keyboard events.
    - Tool-based keyboard events: These are the events registered by an editor tools when the editor is activated. In general, when an editor is activated, its toolbar becomes active (e.g. in classroom, toolbar became visible) and now, keyboard events can be listened by its tools.

- Mouse events: 
    - Mouse events generated when users use mouse on certain elements within the viewports

- Focus events:
    - When a particular elements is focused (using mouse or keyboard), some action might needs to be taken. For example, when an element created by an editor, such as a text input or an editable, and put on a viewport, subsequently, when the element is focus, the editor needs to be ready to handle events emitted by this element, hence, the editor needs to be activated when the element is focus.

- Touch events
    - Touch events generated when users touch the viewports

## Event manager

### Listener attachment

In order for an event manager to route events, the manager needs to attach its event listeners to 

### Event routing

When events are generated, editors don't actually listen to those events directly. All generated events are collected by an event manager which will then route them to registered handlers. There are 3 steps for matching events with listeners:

1. Check for capture all events listeners. Some editors / tools might be interested on all events from a particular element it has created (e.g. in case of text editor's created element), it can register to capture *ALL* events generated from that *PARTICULAR* element.
2. If an event pass through step 1 (either doesn't match any event listener or the listener process it doesn't change `continue` flag to false), it will be processed by any listener that register for the `keyboard + mouse` combination of the event
3. After step 2, if no listener process the event, the event will be check to match with `global keyboard handler`.

An event manager collects events by attaching native listener to suitable source:
- All layers that are floated will supply mouse / touch events
- Viewport root element will supply keyboard events
- External elements registered by coordinators or external app will supply keyboards events

So when does an event manager attach / detach its native listeners?
- An event manager attach / detach its native mouse listeners to any layers that floating / sinking layers by listening to layer events of the viewport
- An event manager attach / detach its native keyboard listener to the viewport and external elements when the viewport containing it becomes an enabled / disabled viewport

## Listeners

Who can listen to events? There two types of destinations for events:
- Toolbars
- Tools

Both of these entities implements UserInputHandler interface which allow declaration of a set of mouse handlings and keyboard handlings. Each handling declare a matching pair of handler and trigger. The trigger tells the combination of mouse and keyboard events which trigger the handler. The handler will be added to the event manager at the right moments. The moments are decided by the coordinator, depending on the specific usage scenarios.

### Viewport attachment

A toolbar which is local can only be effective when attached to a viewport. A coordinator is the decider of creation of a local toolbar for a viewport and connect the toolbar to the corresponding UI.

### Common tools

We implement the following global common tools
- Global zoom tool: This tool listen for global mouse events on any viewport and scale the viewport
- Global pan tool: This tool listen for global mouse events on any viewport and translate the viewport
- Global history tool: This tool works with a global history repo which stores all history managers. THis tool combines the history of individual viewports into a single history queue and perform undo / redo action on this queue.

We implement the following local common tools
- Editor switching tool: This tool listen user interaction through the UI to work with the Coordinator to switch editor for a particular viewport.
- History tool: This tool listen for user interaction through the UI of individual viewport. It works with the global history repos and perform undo / redo action on the history queue of the history manager corresponding to its viewport.
- Select tool: 
- Pan tool:
- Zoom tool:

## Coordinator specific

### Classroom coordinator

The `classroom coordinator` uses a local toolbar of common tools for each viewport to manage:
- Pan and zoom on the active viewport
- History on the active viewport

The toolbars are created by the `classroom coordinator` when a new viewport as described in the following flow:

- Viewport created
- Coordinator creates a local common toolbar, attach the viewport to the local common toolbar.
- Coordinator creates a local editor toolbar for each editor. The viewport is attached to each of these toolbars.

In a `classroom coordinator`, the attachment and detachment of listeners, management of tool UIs are done as described below:

*Viewport enabled (focus in) / disabled (focus out)*

Note: In classroom coordinator, viewport disabled / endabled is the same as viewport focusout / focusin respectively. Viewport disabled always comes with viewport focus out and Viewport enabled always comes with viewport focus in.

- When viewport is enabled, the handler attachment manager will retrieve the tool stack of the enabled viewport and register the handler accordingly to the viewport's event manager.

- When viewport is disabled, the handler attachment manager will clear the handler registrations of the tool stack corresponding to the disabled viewport in the viewport's event manager.

*On an active viewport*

- When no editor or common tool is active, only the local common toolbar of the viewport is the destination
- When an editor is active, its toolbar's key and mouse handling will be registered to the currently active viewport's event manager.
- When a tool of an editor is active, its key and mouse handling will be registered to the currently active viewport's event manager.
- When a common tool is active, its key and mouse handling will be registered to the currently active viewport's event manager.

- When a viewport becomes enabled, all known global keyhandling and mousehandling (from common toolbar, editor toolbar (if any), active common tool (if any), active editor tool (if any)) will be attached to the viewport's event manager.

- When a viewport becomes disabled, all key handling and mouse handling  registered with its event manager will be reset.

The class `HandlerAttachmentManager` implements this logic by listening to various events for viewports, editors, tools and determined which are the active tools/tools bar and put them all in one stack for the viewport that the toolbar is attached to. It then scans the tools / toolbars for the mouse handling and keyboard handling and register them with the event managers. Check implementation for more details.