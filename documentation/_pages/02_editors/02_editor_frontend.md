---
title: Editor frontend
---

The editor frontend is responsible for accepting user input for editing, displaying document data. The editor front end, or more precisely, the framework for implementing editors must satisfy a few use cases:

- An editor can be used within a class room
- An editor can be used to create / edit document as part of a comment box
- An editor can be used to purely display document embedded in a standard HTML page

To achieve this we define following components

- **Coordinator**

  It is responsible for managing all editors, viewports and event managers on a particular page. For example, the coordinator on the classroom page knows all the documents being displayed in the classroom, and can store the necessary state for example, mapping of the document id to their local ids.

  The coordinator also contains the logic to initialize editors and perform necessary steps to instruct an editor to load a document into a viewport and display it.

  The coordinator also provides common tools that will be used on the page.

  We can have a few types of coordinators:
    
    - **Whiteboard coordinator**: coordinators the editors inside the classroom, it also initialize and involves in the classroom flow, for example, initialize synchronization, switch presentation flow, stores the classroom document related information, such as mapping of global id and local id.

    - **Text editor coordinator**: one important use case is that user can use our editor to perform commenting on our social networks to answer a particular educational problem. This means that we need to provide a text editor that allow embedding other editors inside it for editing document of different type inplace.

    - **Standalone coordinator**: a coordinator that manage a single editor so that user can use that to create document of a specific type. This editor is similar to whiteboard editor, without synchronization among multiple peers.

    - **Embed display coordinator**: this coordinator is similar to the text editor in that it allows displaying documents inside a HTML page but without allowing to edit them. This editor work with a special version of editor which strip down all the user interaction aspect, it also doesn't manage the event manager.

- **Viewport Manager**

    A Viewport Manager specifies a location inside 

- Event Manager
- Editor

  - Doc Controller
  - Layer Controller
  - Object Controller