---
title: UI Applications
---

The VIClass frontend contains a few applications:

* Homepage - This contains the functionalities on the homepage, where we display welcome, prominent, or new classes. It also contains UI for registration and logging into other application
* LSession - This contains the functionalities for working with the learning session, including creation of learning sessions, searching for learning sessions, session registrations, acceptance, editing, etc..
* Classroom - This contains the functionalities for interaction inside the classroom

This section describes the structure of these applications in order to help you start developing with these applications. More applications can be added or more functionalities can be added to existing applications.

