GEM
  remote: https://rubygems.org/
  specs:
    addressable (2.8.0)
      public_suffix (>= 2.0.2, < 5.0)
    colorator (1.1.0)
    concurrent-ruby (1.1.10)
    em-websocket (0.5.3)
      eventmachine (>= 0.12.9)
      http_parser.rb (~> 0)
    eventmachine (1.2.7)
    eventmachine (1.2.7-x64-mingw32)
    ffi (1.15.5)
    ffi (1.15.5-x64-mingw32)
    forwardable-extended (2.6.0)
    http_parser.rb (0.8.0)
    i18n (1.12.0)
      concurrent-ruby (~> 1.0)
    jekyll (4.2.2)
      addressable (~> 2.4)
      colorator (~> 1.0)
      em-websocket (~> 0.5)
      i18n (~> 1.0)
      jekyll-sass-converter (~> 2.0)
      jekyll-watch (~> 2.0)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.0)
      liquid (~> 4.0)
      mercenary (~> 0.4.0)
      pathutil (~> 0.9)
      rouge (~> 3.0)
      safe_yaml (~> 1.0)
      terminal-table (~> 2.0)
    jekyll-feed (0.16.0)
      jekyll (>= 3.7, < 5.0)
    jekyll-sass-converter (2.2.0)
      sassc (> 2.0.1, < 3.0)
    jekyll-swagger (1.0.2)
    jekyll-toc (0.17.1)
      jekyll (>= 3.9)
      nokogiri (~> 1.11)
    jekyll-watch (2.2.1)
      listen (~> 3.0)
    kramdown (2.4.0)
      rexml
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    liquid (4.0.3)
    liquid-md5 (0.0.3)
      liquid (>= 2.5, < 5.0)
    listen (3.7.1)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    mercenary (0.4.0)
    nokogiri (1.13.8-x64-mingw32)
      racc (~> 1.4)
    nokogiri (1.13.8-x86_64-linux)
      racc (~> 1.4)
    pathutil (0.16.2)
      forwardable-extended (~> 2.6)
    public_suffix (4.0.7)
    racc (1.6.0)
    rb-fsevent (0.11.1)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rexml (3.2.5)
    rouge (3.30.0)
    safe_yaml (1.0.5)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc (2.4.0-x64-mingw32)
      ffi (~> 1.9)
    terminal-table (2.0.0)
      unicode-display_width (~> 1.1, >= 1.1.1)
    tzinfo (2.0.5)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2022.1)
      tzinfo (>= 1.0.0)
    unicode-display_width (1.8.0)
    webrick (1.7.0)

PLATFORMS
  x64-mingw32
  x86_64-linux-musl

DEPENDENCIES
  jekyll
  jekyll-feed
  jekyll-swagger (~> 1.0.2)
  jekyll-toc
  liquid-md5 (~> 0.0.3)
  tzinfo-data
  webrick

BUNDLED WITH
   2.2.27
