$activeBackground: #CFF;

.vmenu-input {
    position: absolute;
    opacity: 0;
  }
  
.vmenu-label {
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 0;
    border-bottom: 1px solid #CCC;
}

.vmenu-label::after {
    right: 5px;
    position: absolute;
}

.vmenu-label .vmenu-link {
    color: #212529 !important;
}

.vmenu-sub {
    display: none;
}

.vmenu-input:checked ~ .vmenu-sub {
    display: block;
}

.vmenu-lvl1 {
    background: #FFF;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    border: solid 1px #CCC;
}

.vmenu-label {
    padding: 5px 10px 5px 5px;
}

.vmenu-lvl2 > .vmenu-item .vmenu-label {
    padding: 5px 10px 5px 10px;
    background-color: #EEE;
    opacity: 0.8;
}

.vmenu-lvl3 > .vmenu-item .vmenu-label {
    padding: 5px 10px 5px 15px;
    background-color: #DDD;
    opacity: 0.8;
}

.vmenu-lvl4 > .vmenu-item .vmenu-label {
    padding: 5px 10px 5px 20px;
    background-color: #DCC;
    opacity: 0.8;
}

.vmenu-label:hover {
    background-color: $activeBackground !important;
}

.vmenu-item.active > .vmenu-label {
    background-color: $activeBackground !important;
}

.vmenu-item--has-children > .vmenu-input ~ .vmenu-label::after {
    content: "+";
}

.vmenu-item--has-children > .vmenu-input:checked ~ .vmenu-label::after {
    content: "-"
}

.vmenu-link {
    display: block;
    width:100%;
}

/**
* Submenu
-----------------------------*/


.submenu {
display: none;
background: #444359;
font-size: 14px;
}

.submenu li { border-bottom: 1px solid #4b4a5e; }

.submenu a {
display: block;
text-decoration: none;
color: #d9d9d9;
padding: 12px;
padding-left: 42px;
-webkit-transition: all 0.25s ease;
-o-transition: all 0.25s ease;
transition: all 0.25s ease;
}

.submenu a:hover {
background: #b63b4d;
color: #FFF;
}