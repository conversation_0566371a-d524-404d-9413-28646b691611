/*
    Name:       Base16 Eighties Dark
    Author:     <PERSON> (http://chriskempson.com)
    Pygments template by <PERSON> (https://github.com/idleberg)
    Created with Base16 Builder by <PERSON> (https://github.com/chriske<PERSON>on/base16-builder)
*/
.highlight .hll { background-color: #515151 }
.highlight  { background: #2d2d2d; color: #f2f0ec }
.highlight .c { color: #747369 } /* Comment */
.highlight .err { color: #f2777a } /* Error */
.highlight .k { color: #cc99cc } /* Keyword */
.highlight .l { color: #f99157 } /* Literal */
.highlight .n { color: #f2f0ec } /* Name */
.highlight .o { color: #66cccc } /* Operator */
.highlight .p { color: #f2f0ec } /* Punctuation */
.highlight .cm { color: #747369 } /* Comment.Multiline */
.highlight .cp { color: #747369 } /* Comment.Preproc */
.highlight .c1 { color: #747369 } /* Co<PERSON>.Single */
.highlight .cs { color: #747369 } /* Comment.Special */
.highlight .gd { color: #f2777a } /* Generic.Deleted */
.highlight .ge { font-style: italic } /* Generic.Emph */
.highlight .gh { color: #f2f0ec; font-weight: bold } /* Generic.Heading */
.highlight .gi { color: #99cc99 } /* Generic.Inserted */
.highlight .gp { color: #747369; font-weight: bold } /* Generic.Prompt */
.highlight .gs { font-weight: bold } /* Generic.Strong */
.highlight .gu { color: #66cccc; font-weight: bold } /* Generic.Subheading */
.highlight .kc { color: #cc99cc } /* Keyword.Constant */
.highlight .kd { color: #cc99cc } /* Keyword.Declaration */
.highlight .kn { color: #66cccc } /* Keyword.Namespace */
.highlight .kp { color: #cc99cc } /* Keyword.Pseudo */
.highlight .kr { color: #cc99cc } /* Keyword.Reserved */
.highlight .kt { color: #ffcc66 } /* Keyword.Type */
.highlight .ld { color: #99cc99 } /* Literal.Date */
.highlight .m { color: #f99157 } /* Literal.Number */
.highlight .s { color: #99cc99 } /* Literal.String */
.highlight .na { color: #6699cc } /* Name.Attribute */
.highlight .nb { color: #f2f0ec } /* Name.Builtin */
.highlight .nc { color: #ffcc66 } /* Name.Class */
.highlight .no { color: #f2777a } /* Name.Constant */
.highlight .nd { color: #66cccc } /* Name.Decorator */
.highlight .ni { color: #f2f0ec } /* Name.Entity */
.highlight .ne { color: #f2777a } /* Name.Exception */
.highlight .nf { color: #6699cc } /* Name.Function */
.highlight .nl { color: #f2f0ec } /* Name.Label */
.highlight .nn { color: #ffcc66 } /* Name.Namespace */
.highlight .nx { color: #6699cc } /* Name.Other */
.highlight .py { color: #f2f0ec } /* Name.Property */
.highlight .nt { color: #66cccc } /* Name.Tag */
.highlight .nv { color: #f2777a } /* Name.Variable */
.highlight .ow { color: #66cccc } /* Operator.Word */
.highlight .w { color: #f2f0ec } /* Text.Whitespace */
.highlight .mf { color: #f99157 } /* Literal.Number.Float */
.highlight .mh { color: #f99157 } /* Literal.Number.Hex */
.highlight .mi { color: #f99157 } /* Literal.Number.Integer */
.highlight .mo { color: #f99157 } /* Literal.Number.Oct */
.highlight .sb { color: #99cc99 } /* Literal.String.Backtick */
.highlight .sc { color: #f2f0ec } /* Literal.String.Char */
.highlight .sd { color: #747369 } /* Literal.String.Doc */
.highlight .s2 { color: #99cc99 } /* Literal.String.Double */
.highlight .se { color: #f99157 } /* Literal.String.Escape */
.highlight .sh { color: #99cc99 } /* Literal.String.Heredoc */
.highlight .si { color: #f99157 } /* Literal.String.Interpol */
.highlight .sx { color: #99cc99 } /* Literal.String.Other */
.highlight .sr { color: #99cc99 } /* Literal.String.Regex */
.highlight .s1 { color: #99cc99 } /* Literal.String.Single */
.highlight .ss { color: #99cc99 } /* Literal.String.Symbol */
.highlight .bp { color: #f2f0ec } /* Name.Builtin.Pseudo */
.highlight .vc { color: #f2777a } /* Name.Variable.Class */
.highlight .vg { color: #f2777a } /* Name.Variable.Global */
.highlight .vi { color: #f2777a } /* Name.Variable.Instance */
.highlight .il { color: #f99157 } /* Literal.Number.Integer.Long */