main:
  - title: ""
    url: ""

docs:
  - title: General
    url: /01_general
    content: true
    children:
      - title: Code organization
        url: /01_general/01_code_org
      - title: Development setup
        url: /01_general/02_development
  - title: Editors
    url: /02_editors
    content: true
    children:
      - title: Editor frontend
        url: /02_editors/02_editor_frontend
      - title: Input events handling rules
        url: /02_editors/03_input_event_handling_rules  
      - title: Creation of viewports and layers in different use cases
        url: /02_editors/04_viewport_and_layer_creation
      - title: Coordinate systems
        url: /02_editors/05_coordinate_systems

  # - title: Editor core module
  #   url: /02_editor_core
  #   content: true
  #   children:
  #     - title: Editor Front End
  #       url: /02_editor_core/02_editor_frontend
  # - title: Networking
  #   url: /03_vinet
  #   content: true
  #   children:
  #     - title: Concepts
  #       url: /03_vinet/01_concepts
  #     - title: Synchronization Manager
  #       url: /03_vinet/02_sync_manager
  #     - title: Synchronizer
  #       url: /03_vinet/03_synchronizer
  #     - title: Client Command Gateway
  #       url: /03_vinet/04_client_cmd_gateway