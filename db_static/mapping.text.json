[{"_id": "tpl-Shape", "text": {"en": "<PERSON><PERSON>pe {name}", "vi": "<PERSON><PERSON><PERSON> {name}"}}, {"_id": "tpl-Point", "text": {"en": "Point {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> {name}"}}, {"_id": "tpl-Line", "text": {"en": "Line {name}", "vi": "Đường thẳng {name}"}}, {"_id": "tpl-Triangle", "text": {"en": "Triangle {name}", "vi": "<PERSON> g<PERSON> {name}"}}, {"_id": "tpl-thPoint", "text": {"en": "th point {value}", "vi": "<PERSON><PERSON><PERSON><PERSON> thứ {value}"}}, {"_id": "tpl-thLine", "text": {"en": "th line {value}", "vi": "Đ<PERSON><PERSON>ng thẳng thứ {value}"}}, {"_id": "tpl-thShape", "text": {"en": "th shape {value}", "vi": "<PERSON><PERSON><PERSON> thứ {value}"}}, {"_id": "tpl-LineStart", "text": {"en": "Line start {name}", "vi": "Đ<PERSON>ờng thẳng bắt đầu {name}"}}, {"_id": "tpl-LineEnd", "text": {"en": "Line end {name}", "vi": "Đường thẳng kết thúc {name}"}}, {"_id": "tpl-DirectionOfLine", "text": {"en": "Direction of line {value}", "vi": "Hướng của đường thẳng {value}"}}, {"_id": "tpl-DirectionOfLineStart", "text": {"en": "Direction of line start {value}", "vi": "Hướng của đường thẳng bắt đầu {value}"}}, {"_id": "tpl-DirectionOfLineEnd", "text": {"en": "Direction of line end {value}", "vi": "Hướng của đường thẳng kết thúc {value}"}}, {"_id": "tpl-StartEndPoint", "text": {"en": "Start point {name}, end point {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> đầu {name}, điểm cuối {name}"}}, {"_id": "tpl-InscribedOf", "text": {"en": "Inscribed of {name}", "vi": "<PERSON><PERSON><PERSON> t<PERSON> c<PERSON> {name}"}}, {"_id": "tpl-CircumscribedOf", "text": {"en": "Circumscribed of {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON> c<PERSON> {name}"}}, {"_id": "tpl-EscribedOf", "text": {"en": "Escribed of {name}", "vi": "<PERSON><PERSON><PERSON> ti<PERSON> c<PERSON> {name}"}}, {"_id": "tpl-OppositeOf", "text": {"en": "Opposite to the point {name}", "vi": "<PERSON><PERSON><PERSON> di<PERSON>n đỉnh {name}"}}, {"_id": "tpl-CenterPointOfPolygon", "text": {"en": "Center point of polygon {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> tâm của hình {name}"}}, {"_id": "tpl-CenterPointOfPoints", "text": {"en": "Center point of points [name]", "vi": "<PERSON><PERSON><PERSON><PERSON> cách đều các điểm [name]"}}, {"_id": "tpl-MiddlePointOfPoints", "text": {"en": "Middle point of two points {name} and {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h giữa hai điểm {name} và {name}"}}, {"_id": "tpl-MiddlePointOfLineSegment", "text": {"en": "Middle point of line segment {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h giữa đoạn thẳng {name}"}}, {"_id": "tpl-MidpointOf", "text": {"en": "Midpoint of {name}", "vi": "<PERSON>rung điểm của {name}"}}, {"_id": "tpl-FromPoint", "text": {"en": "From point {name}", "vi": "<PERSON><PERSON> điểm {name}"}}, {"_id": "tpl-AtPoint", "text": {"en": "At point {name}", "vi": "<PERSON><PERSON><PERSON> điể<PERSON> {name}"}}, {"_id": "tpl-Height", "text": {"en": "Height {name} = {expression}", "vi": "<PERSON><PERSON><PERSON><PERSON> cao {name} = {expression}"}}, {"_id": "tpl-<PERSON>ja<PERSON><PERSON>ngle", "text": {"en": "Adjacent vertex name is {name} with angle = {value}", "vi": "<PERSON><PERSON><PERSON> kề tên là {name} với số đo góc = {value}"}}, {"_id": "tpl-BaseAngle", "text": {"en": "Base vertex name is {name} with angle = {value}", "vi": "<PERSON><PERSON><PERSON> đáy tên là {name} với số đo góc = {value}"}}, {"_id": "tpl-ApexAngle", "text": {"en": "Apex vertex name is {name} with angle = {value}", "vi": "Đỉnh tên là {name} với số đo góc = {value}"}}, {"_id": "tpl-AdjacentSideLength", "text": {"en": "Adjacent side {name} = {expression}", "vi": "<PERSON><PERSON><PERSON> kề {name} = {expression}"}}, {"_id": "tpl-HypotenuseLength", "text": {"en": "Hypotenuse {name} = {expression}", "vi": "<PERSON><PERSON><PERSON> h<PERSON>n {name} = {expression}"}}, {"_id": "tpl-HeightIs", "text": {"en": "Height = {expression}", "vi": "Đường cao = {expression}"}}, {"_id": "tpl-HeightValue", "text": {"en": "Height = {value}", "vi": "Đ<PERSON>ờng cao = {value}"}}, {"_id": "tpl-ApexAngleIs", "text": {"en": "Apex angle = {value}", "vi": "Góc ở đỉnh = {value}"}}, {"_id": "tpl-BaseAngleIs", "text": {"en": "Base angle = {value}", "vi": "Góc ở đáy = {value}"}}, {"_id": "tpl-DiagonalLengthValue", "text": {"en": "Diagonal Length = {value}", "vi": "<PERSON><PERSON> dài đường chéo = {value}"}}, {"_id": "tpl-OppositeSideLength", "text": {"en": "Opposite Side Length = {value}", "vi": "<PERSON><PERSON> dài cạnh đối = {value}"}}, {"_id": "tpl-LengthValue", "text": {"en": "Length = {value}", "vi": "<PERSON><PERSON> dài = {value}"}}, {"_id": "tpl-BaseSideIs", "text": {"en": "Base side is {name}", "vi": "<PERSON><PERSON><PERSON> đ<PERSON> là {name}"}}, {"_id": "tpl-SideIs", "text": {"en": "Side is {name}", "vi": "<PERSON><PERSON><PERSON> bên là {name}"}}, {"_id": "tpl-Hypotenuse", "text": {"en": "Hypotenuse is {name}", "vi": "<PERSON><PERSON><PERSON> huyền là {name}"}}, {"_id": "tpl-AdjacentSide", "text": {"en": "Adjacent side is {name}", "vi": "<PERSON><PERSON><PERSON> kề là {name}"}}, {"_id": "tpl-ThroughPoint", "text": {"en": "Through point {name}", "vi": "<PERSON><PERSON>ua điểm {name}"}}, {"_id": "tpl-Points", "text": {"en": "Points [name]", "vi": "<PERSON><PERSON><PERSON> [name]"}}, {"_id": "tpl-TwoPoints", "text": {"en": "Two points {name} and {name}", "vi": "<PERSON> điểm {name} và {name}"}}, {"_id": "tpl-ThroughTwoPoints", "text": {"en": "Through two points {name} and {name}", "vi": "Qua hai điểm {name} và {name}"}}, {"_id": "tpl-ThreePoints", "text": {"en": "Three points {name}, {name} and {name}", "vi": "<PERSON> điểm {name}, {name} và {name}"}}, {"_id": "tpl-FromThreePoints", "text": {"en": "From three points {name}, {name} and {name}", "vi": "Từ ba điểm {name}, {name} và {name}"}}, {"_id": "tpl-FromFourPoints", "text": {"en": "From four points {name}, {name}, {name} and {name}", "vi": "Từ bốn điểm {name}, {name}, {name} và {name}"}}, {"_id": "tpl-FromLine", "text": {"en": "From line {name}", "vi": "Từ đường thẳng {name}"}}, {"_id": "tpl-LineSegment", "text": {"en": "Line segment {name}", "vi": "Đoạn thẳng {name}"}}, {"_id": "tpl-FromLineSegment", "text": {"en": "From line segment {name}", "vi": "<PERSON>ừ đoạn thẳng {name}"}}, {"_id": "tpl-OfTwoLines", "text": {"en": "Of two line {name} and {name}", "vi": "C<PERSON>a hai đường thẳng {name} và {name}"}}, {"_id": "tpl-FromTwoLineSegments", "text": {"en": "From two line segment {name} and {name}", "vi": "Từ hai đoạn thẳng {name} và {name}"}}, {"_id": "tpl-FromThreeLineSegments", "text": {"en": "From three line segment {name}, {name} and {name}", "vi": "Từ ba đoạn thẳng {name}, {name} và {name}"}}, {"_id": "tpl-OnLine", "text": {"en": "On line {name}", "vi": "<PERSON>r<PERSON><PERSON> đ<PERSON>ờng thẳng {name}"}}, {"_id": "tpl-OnCircularSector", "text": {"en": "On circular sector {name}", "vi": "Trên cung tròn {name}"}}, {"_id": "tpl-OnCircle", "text": {"en": "On circle {name}", "vi": "Trê<PERSON> đường tròn {name}"}}, {"_id": "tpl-OnEllipse", "text": {"en": "On ellipse {name}", "vi": "<PERSON>r<PERSON><PERSON> ellipse {name}"}}, {"_id": "tpl-OnLineSegment", "text": {"en": "On line segment {name}", "vi": "<PERSON>r<PERSON><PERSON> đoạn thẳng {name}"}}, {"_id": "tpl-TangentOfCircle", "text": {"en": "Tangent of circle {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> tuyến của đường tròn {name}"}}, {"_id": "tpl-With", "text": {"en": "With {name}", "vi": "<PERSON><PERSON><PERSON> {name}"}}, {"_id": "tpl-ParallelWith", "text": {"en": "<PERSON><PERSON><PERSON> with {name}", "vi": "Song song với {name}"}}, {"_id": "tpl-ParallelWithLine", "text": {"en": "Parallel with line {name}", "vi": "Song song với đường thẳng {name}"}}, {"_id": "tpl-ParallelWithLineSegment", "text": {"en": "Parallel with line segment {name}", "vi": "Song song với đoạn thẳng {name}"}}, {"_id": "tpl-ParallelWithRay", "text": {"en": "tpl-<PERSON>lle<PERSON> with ray {name}", "vi": "Song song với tia {name}"}}, {"_id": "tpl-PerpendicularWith", "text": {"en": "Perpendicular with {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> góc với {name}"}}, {"_id": "tpl-PerpendicularWithLine", "text": {"en": "Perpendicular with line {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> góc với đường thẳng {name}"}}, {"_id": "tpl-PerpendicularWithLineSegment", "text": {"en": "Perpendicular with line segment {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> góc với đoạn thẳng {name}"}}, {"_id": "tpl-PerpendicularWithRay", "text": {"en": "Perpendicular with ray {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> góc với tia {name}"}}, {"_id": "tpl-Ratio", "text": {"en": "Ratio {expression}", "vi": "Tỉ lệ {expression}"}}, {"_id": "tpl-RatioValue", "text": {"en": "Ratio {value}", "vi": "Tỉ lệ {value}"}}, {"_id": "tpl-<PERSON><PERSON><PERSON>", "text": {"en": "Number of edge {value}", "vi": "Số cạnh {value}"}}, {"_id": "tpl-Length", "text": {"en": "Length {name} = {expression}", "vi": "<PERSON><PERSON> dài {name} = {expression}"}}, {"_id": "tpl-LengthIs", "text": {"en": "Length = {expression}", "vi": "<PERSON><PERSON> dài = {expression}"}}, {"_id": "tpl-CoefficientIs", "text": {"en": "<PERSON>ệ số k = {value}", "vi": "<PERSON>ệ số k = {value}"}}, {"_id": "tpl-Distance", "text": {"en": "Distance to {name} = {expression}", "vi": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch tới {name} = {expression}"}}, {"_id": "tpl-Radius", "text": {"en": "<PERSON><PERSON> {expression}", "vi": "<PERSON><PERSON> {expression}"}}, {"_id": "tpl-CenterCircle", "text": {"en": "Center {name}", "vi": "<PERSON><PERSON><PERSON> {name}"}}, {"_id": "tpl-RadiusLineSegment", "text": {"en": "Radius is line segment {name}", "vi": "<PERSON><PERSON> k<PERSON>h là đoạn thẳng {name}"}}, {"_id": "tpl-DiameterLineSegment", "text": {"en": "Diameter is line segment {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>h là đoạn thẳng {name}"}}, {"_id": "tpl-RangeDegree", "text": {"en": "In range {value} degree and {value} degree", "vi": "Trong khoảng {value} độ và {value} độ"}}, {"_id": "tpl-AngleDegree", "text": {"en": "Angle = {value} degree", "vi": "Góc = {value} độ"}}, {"_id": "tpl-RangeRadian", "text": {"en": "In range {value} rad and {value} rad", "vi": "Trong khoảng {value} rad và {value} rad"}}, {"_id": "tpl-AngleRadian", "text": {"en": "Angle = {value} rad", "vi": "Góc = {value} rad"}}, {"_id": "tpl-3DPoint", "text": {"en": "3D Coordinates X = {value}, Y = {value}, Z = {value}", "vi": "<PERSON><PERSON><PERSON> 3D X = {value}, Y = {value}, Z = {value}"}}, {"_id": "tpl-2DPoint", "text": {"en": "2D Coordinates X = {value}, Y = {value}", "vi": "<PERSON><PERSON><PERSON> đ<PERSON> 2D X = {value}, Y = {value}"}}, {"_id": "RandomPointOnLine", "text": {"en": "random pint on line", "vi": "<PERSON>i<PERSON>m ngẫu nhiên trên đường thẳng"}}, {"_id": "RandomPointOnLineSegment", "text": {"en": "random pint on line segment", "vi": "<PERSON>i<PERSON><PERSON> ngẫu nhiên trên đoạn thẳng"}}, {"_id": "RandomPointOnCircle", "text": {"en": "random pint on circle", "vi": "Điểm ngẫu nhiên trên đường tròn"}}, {"_id": "PointOnCircularSector", "text": {"en": "random pint on circular sector", "vi": "<PERSON><PERSON><PERSON><PERSON> ngẫu nhiên trên cung tròn"}}, {"_id": "RandomPointOnCircleWithRangeDegree", "text": {"en": "random pint on circle with degree", "vi": "Điểm ngẫu nhiên trên đường tròn với độ"}}, {"_id": "RandomPointOnCircleWithRangeRadian", "text": {"en": "random point on circle with radian", "vi": "<PERSON>i<PERSON>m ngẫu nhiên trên đường tròn với radian"}}, {"_id": "CenterPointOfPolygon", "text": {"en": "Center point of a polygon", "vi": "<PERSON><PERSON><PERSON><PERSON> tâm của hình"}}, {"_id": "CenterPointOfPoints", "text": {"en": "Center point of a list of other points", "vi": "<PERSON><PERSON><PERSON><PERSON> cách đều các điểm"}}, {"_id": "MiddlePointOfPoints", "text": {"en": "Middle point of two points", "vi": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h gi<PERSON>a hai điểm"}}, {"_id": "MiddlePointOfLineSegment", "text": {"en": "Middle point of line segment", "vi": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h gi<PERSON>a đoạn thẳng"}}, {"_id": "MidpointOfLineSegment", "text": {"en": "Midpoint of line segment", "vi": "Trung điểm của đoạn thẳng"}}, {"_id": "PointOnLineWithDistance", "text": {"en": "Point on a line with distance to other point", "vi": "<PERSON>iểm trên đường thằng và cách điểm khác một khoảng"}}, {"_id": "PointOnLineSegmentWithDistance", "text": {"en": "Point on a line segment with distance to other point", "vi": "Điểm trên đoạn thằng và cách điểm khác một khoảng"}}, {"_id": "PointOnLineWithLength", "text": {"en": "Point on a line with length", "vi": "<PERSON><PERSON><PERSON><PERSON> trên đường thằng với độ dài"}}, {"_id": "PointOnLineSegmentWithLength", "text": {"en": "Point on a line segment with length", "vi": "<PERSON><PERSON><PERSON>m trên đoạn thằng với độ dài"}}, {"_id": "PointOnLineSegmentWithRatio", "text": {"en": "Point on a line segment with ratio", "vi": "<PERSON><PERSON><PERSON><PERSON> trên đoạn thằng với tỉ lệ"}}, {"_id": "3DCoordinates", "text": {"en": "Point from 3D coordinates", "vi": "<PERSON><PERSON><PERSON><PERSON> trên hệ tọa độ <PERSON>"}}, {"_id": "2DCoordinates", "text": {"en": "Point from 2D coordinates", "vi": "<PERSON><PERSON><PERSON><PERSON> hệ tọa độ <PERSON>"}}, {"_id": "LineThroughAPoint", "text": {"en": "Line through a point", "vi": "Đường thẳng đi qua một điểm"}}, {"_id": "LineThroughTwoPoints", "text": {"en": "Line through two points", "vi": "Đường thẳng đi qua hai điểm"}}, {"_id": "LineThroughAPointParallelWithLine", "text": {"en": "Line through a point and parallel with a line/line segment/ray", "vi": "Đường thẳng đi qua một điểm và song song với đường thẳng/đoạn thẳng/tia"}}, {"_id": "LineParallelWithLine", "text": {"en": "Line parallel with a line/line segment/ray", "vi": "Đường thẳng song song với đường thẳng/đoạn thẳng/tia"}}, {"_id": "LineThroughAPointPerpendicularWithLine", "text": {"en": "Line through a point and perpendicular with a line/line segment/ray", "vi": "Đường thẳng đi qua một điểm và vuông góc với đường thẳng/đoạn thẳng/tia"}}, {"_id": "LinePerpendicularWithLine", "text": {"en": "Line perpendicular with a line/line segment/ray", "vi": "Đường thẳng vuông góc với đường thẳng/đoạn thẳng/tia"}}, {"_id": "TriangleFromThreePoints", "text": {"en": "Triangle from three points", "vi": "<PERSON> gi<PERSON>c từ ba điểm"}}, {"_id": "TriangleFromTwoLineSegment", "text": {"en": "Triangle from two line segment", "vi": "<PERSON> gi<PERSON>c từ hai đoạn thẳng"}}, {"_id": "TriangleFromLineSegmentAndPoint", "text": {"en": "Triangle from a line segment and a point", "vi": "<PERSON> giác từ đoạn thẳng và điểm"}}, {"_id": "QuadrilateralFromPoints", "text": {"en": "Quadrilateral from points", "vi": "<PERSON><PERSON> gi<PERSON>c từ các điểm"}}, {"_id": "QuadrilateralFromThreeLineSegments", "text": {"en": "Quadrilateral from three line segment", "vi": "<PERSON><PERSON> gi<PERSON>c từ ba đoại thẳng"}}, {"_id": "NameOfPoint", "text": {"en": "name of point", "vi": "tên đi<PERSON>"}}, {"_id": "NameOfPoints", "text": {"en": "name of points", "vi": "tên c<PERSON>c đi<PERSON>m"}}, {"_id": "NameOfLine", "text": {"en": "name of line", "vi": "tên đường thẳng"}}, {"_id": "NameOfLineSegment", "text": {"en": "name of line segment", "vi": "tên đoạn thẳng"}}, {"_id": "NameOfCircularSector", "text": {"en": "name of circular sector", "vi": "tên cung tròn"}}, {"_id": "NameOfCircle", "text": {"en": "name of circle", "vi": "tên đường tròn"}}, {"_id": "NameOfEllipse", "text": {"en": "name of ellipse", "vi": "tên đ<PERSON><PERSON> ellipse"}}, {"_id": "NameOfTriangle", "text": {"en": "name of triangle", "vi": "tên tam gi<PERSON>c"}}, {"_id": "NameOfRectangle", "text": {"en": "name of rectangle", "vi": "tên hình chữ nhật"}}, {"_id": "NameOfSquare", "text": {"en": "name of square", "vi": "tên hình vuông"}}, {"_id": "NameOfPolygon", "text": {"en": "name of polygon", "vi": "tên đa gi<PERSON>c"}}, {"_id": "LengthAssignment", "text": {"en": "length assignment", "vi": "<PERSON><PERSON> dài"}}, {"_id": "Height", "text": {"en": "height", "vi": "<PERSON><PERSON><PERSON><PERSON> cao"}}, {"_id": "Value", "text": {"en": "value", "vi": "gi<PERSON> trị"}}, {"_id": "Expression", "text": {"en": "expression", "vi": "bi<PERSON><PERSON> th<PERSON>c"}}, {"_id": "Ra<PERSON>", "text": {"en": "radian", "vi": "radian"}}, {"_id": "Degree", "text": {"en": "degree", "vi": "degree"}}, {"_id": "<PERSON><PERSON>", "text": {"en": "ratio", "vi": "tỉ lệ"}}, {"_id": "Point", "text": {"en": "Point", "vi": "<PERSON><PERSON><PERSON><PERSON>"}}, {"_id": "Line", "text": {"en": "Line", "vi": "Đường thẳng"}}, {"_id": "LineSegment", "text": {"en": "Line Segment", "vi": "Đoạn thẳng"}}, {"_id": "<PERSON>", "text": {"en": "<PERSON>", "vi": "Tia"}}, {"_id": "Triangle", "text": {"en": "Triangle", "vi": "<PERSON> g<PERSON>"}}, {"_id": "AcuteTriangle", "text": {"en": "AcuteTriangle", "vi": "<PERSON> g<PERSON>"}}, {"_id": "ObtuseTriangle", "text": {"en": "Obtuse Triangle", "vi": "<PERSON> gi<PERSON>c tù"}}, {"_id": "RightTriangle", "text": {"en": "Right Triangle", "vi": "<PERSON> gi<PERSON>c vuông"}}, {"_id": "IsoscelesTriangle", "text": {"en": "Isosceles Triangle", "vi": "<PERSON> g<PERSON> cân"}}, {"_id": "IsoscelesRightTriangle", "text": {"en": "Isosceles Right Triangle", "vi": "Tam gi<PERSON>c vuông cân"}}, {"_id": "EquilateralTriangle", "text": {"en": "Equilateral Triangle", "vi": "<PERSON> gi<PERSON> đều"}}, {"_id": "Circle", "text": {"en": "Circle", "vi": "<PERSON><PERSON><PERSON><PERSON> tròn"}}, {"_id": "Ellipse", "text": {"en": "Ellipse", "vi": "<PERSON><PERSON><PERSON><PERSON>"}}, {"_id": "Polygon", "text": {"en": "Polygon", "vi": "<PERSON><PERSON>"}}, {"_id": "CircumscribedCircle", "text": {"en": "CircumscribedCircle", "vi": "<PERSON><PERSON><PERSON><PERSON> tròn ngoại tiếp"}}, {"_id": "Escribed", "text": {"en": "Escribed", "vi": "<PERSON><PERSON><PERSON><PERSON> tròn bàng tiếp"}}, {"_id": "InscribedCircle", "text": {"en": "InscribedCircle", "vi": "<PERSON><PERSON><PERSON><PERSON> tròn nội tiếp"}}, {"_id": "Quadrilateral", "text": {"en": "Quadrilateral", "vi": "<PERSON><PERSON> g<PERSON>c"}}, {"_id": "Trapezoid", "text": {"en": "Trapezoid", "vi": "<PERSON><PERSON><PERSON>g"}}, {"_id": "Rhombus", "text": {"en": "Rhombus", "vi": "<PERSON><PERSON><PERSON> thoi"}}, {"_id": "Parallelogram", "text": {"en": "Parallelogram", "vi": "<PERSON><PERSON><PERSON> bình hành"}}, {"_id": "Rectangle", "text": {"en": "Rectangle", "vi": "<PERSON><PERSON><PERSON> chữ nhật"}}, {"_id": "Square", "text": {"en": "Square", "vi": "<PERSON><PERSON><PERSON> vu<PERSON>ng"}}, {"_id": "<PERSON><PERSON>", "text": {"en": "radius", "vi": "<PERSON><PERSON>"}}, {"_id": "tpl-AltitudeOfTriangle", "text": {"en": "Altitude of triangle {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> cao của tam gi<PERSON> {name}"}}, {"_id": "tpl-BisectorOfTriangle", "text": {"en": "Bisector of triangle {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> phân giác của tam giác {name}"}}, {"_id": "tpl-MedianOfTriangle", "text": {"en": "Median of triangle {name}", "vi": "<PERSON><PERSON><PERSON><PERSON> trung tuyến của tam gi<PERSON>c {name}"}}, {"_id": "tpl-IntersectionWithLine", "text": {"en": "Intersection with line {name}", "vi": "<PERSON><PERSON><PERSON> với đường thẳng {name}"}}, {"_id": "tpl-IntersectionWithCircle", "text": {"en": "Intersection with circle {name}", "vi": "<PERSON><PERSON>o với đường tròn {name}"}}, {"_id": "tpl-IntersectionWithEllipse", "text": {"en": "Intersection with ellipse {name}", "vi": "<PERSON><PERSON><PERSON> với elip {name}"}}, {"_id": "tpl-IntersectionWithSector", "text": {"en": "Intersection with sector {name}", "vi": "<PERSON>iao với cung tròn {name}"}}, {"_id": "tpl-thIntersection", "text": {"en": "th intersection {value}", "vi": "<PERSON><PERSON><PERSON> điểm thứ {value}"}}, {"_id": "tpl-IntersectionOfLine", "text": {"en": "Intersection of line {name}", "vi": "<PERSON><PERSON><PERSON> điểm của đường thẳng {name}"}}, {"_id": "tpl-IntersectionOfCircle", "text": {"en": "Intersection of circle {name}", "vi": "<PERSON><PERSON><PERSON> điểm của đường tròn {name}"}}, {"_id": "tpl-IntersectionOfSector", "text": {"en": "Intersection of sector {name}", "vi": "<PERSON><PERSON><PERSON> điểm của cung tròn {name}"}}, {"_id": "tpl-IntersectionOfEllipse", "text": {"en": "Intersection of ellipse {name}", "vi": "<PERSON><PERSON><PERSON> điểm của elip {name}"}}, {"_id": "IntersectionOfLineLine", "text": {"en": "Intersection of two lines", "vi": "Giao của hai đường thẳng"}}, {"_id": "IntersectionOfLineCircle", "text": {"en": "Intersection of line and circle", "vi": "<PERSON><PERSON><PERSON> điểm của đường thẳng và đường tròn"}}, {"_id": "IntersectionOfLineSector", "text": {"en": "Intersection of a line and a sector", "vi": "Giao của đường thẳng và cung tròn"}}, {"_id": "IntersectionOfLineEllipse", "text": {"en": "Intersection of a line and an ellipse", "vi": "<PERSON>iao của đường thẳng và elip"}}, {"_id": "IntersectionOfCircleLine", "text": {"en": "Intersection of a circle and a line", "vi": "Giao của đường tròn và đường thẳng"}}, {"_id": "IntersectionOfCircleCircle", "text": {"en": "Intersection of two circles", "vi": "<PERSON><PERSON>o của hai đường tròn"}}, {"_id": "IntersectionOfCircleSector", "text": {"en": "Intersection of a circle and a sector", "vi": "<PERSON><PERSON>o của đường tròn và cung tròn"}}, {"_id": "IntersectionOfCircleEllipse", "text": {"en": "Intersection of a circle and an ellipse", "vi": "G<PERSON>o của đường tròn và elip"}}, {"_id": "IntersectionOfSectorLine", "text": {"en": "Intersection of a sector and a line", "vi": "Giao của cung tròn và đường thẳng"}}, {"_id": "IntersectionOfSectorCircle", "text": {"en": "Intersection of a sector and a circle", "vi": "Giao của cung tròn và đường tròn"}}, {"_id": "IntersectionOfSectorSector", "text": {"en": "Intersection of two sectors", "vi": "Giao của hai cung tròn"}}, {"_id": "IntersectionOfSectorEllipse", "text": {"en": "Intersection of a sector and an ellipse", "vi": "Giao của cung tròn và elip"}}, {"_id": "IntersectionOfEllipseLine", "text": {"en": "Intersection of an ellipse and a line", "vi": "Giao của elip và đường thẳng"}}, {"_id": "IntersectionOfEllipseCircle", "text": {"en": "Intersection of an ellipse and a circle", "vi": "<PERSON><PERSON>o của elip và đường tròn"}}, {"_id": "IntersectionOfEllipseSector", "text": {"en": "Intersection of an ellipse and a sector", "vi": "Giao của elip và cung tròn"}}, {"_id": "IntersectionOfEllipse<PERSON>llipse", "text": {"en": "Intersection of two ellipses", "vi": "<PERSON><PERSON>o c<PERSON>a hai elip"}}]