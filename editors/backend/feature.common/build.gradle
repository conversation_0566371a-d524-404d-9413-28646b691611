plugins {
    id "hnct.build"
    id "kotlin"
    id "com.google.protobuf"
    id "com.google.devtools.ksp"
}

version = "1.0.0"

dependencies {
    implementation "com.google.protobuf:protobuf-java:$protobufVs"
}

tasks.withType(JavaCompile) {
    doFirst {
        options.compilerArgs = [
                '--module-path', classpath.asPath,
        ]
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVs}"
    }

    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:1.41.0${platform}"
        }
    }
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                java {
//                    option '--experimental_allow_proto3_optional'
                }
            }
            task.plugins {
                grpc {}
            }
        }
    }
}
