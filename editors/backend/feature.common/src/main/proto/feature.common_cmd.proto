syntax = "proto3";

package viclass.proto.feature_common;

message BoundaryProto {
  PositionProto start = 1;
  PositionProto end = 2;
}

message PositionProto {
  double x = 1;
  double y = 2;
}

// NO OTHER COMMAND IN ALL VICLASS CAN HAVE THESE VALUE BECAUSE THESE VALUES
enum FCCmdTypeProto {
    DUMMY = 0;  // THIS SHOULD NOT BE USED!
    INSERT_DOC = 1000001;
    INSERT_LAYER = 1000002;
    REMOVE_DOC = 1000003;
    PREVIEW_BOUNDARY = 1000004;
    RELOAD_DOC = 1000005;
    UPDATE_BOUNDARY = 1000006;
    UPDATE_LOCAL_CONTENT = 1000007;
}

message InsertDocCmdProto {
    string global_id = 1;
    // initialization data specific to each editor
    optional bytes initData = 2;
    // true if this is a local content document
    optional bool local_doc = 3;
}

message ReloadDocCmdProto {
  int32 localId = 1;
}

message InsertLayerCmdProto {
    BoundaryProto boundary = 1;
    int32 z_index = 2;
}

message RemoveDocCmdProto {
  repeated DocInfo ids = 1;
  string vmId = 2;
  // when the local command is processed, additional data can be added
  // if editor needs to synchronize custom data
  optional bytes deleteData = 3;
}

message PreviewBoundaryProto {
    optional BoundaryProto boundary = 1;
    bool stopPreview = 2;
}

message DocInfo {
  int32 local_id = 1;
  optional string global_id = 2;
}

message UpdateBoundaryProto {
  repeated DocInfo docs = 1;
  repeated BoundaryProto newBoundaries = 2;
  optional bool isResetRatio = 3;
}

message LocalContentProto {
  int32 editor_id = 1;

  int32 local_id = 2;
  optional string global_id = 3;

  int32 version = 4;
  string content = 5; // local content in JSON format
}

message UpdateLocalContentProto {
  repeated LocalContentProto local_content = 1;
}