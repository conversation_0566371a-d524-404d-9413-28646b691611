package viclass.editor.geo.entity

import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.elements.Element
import kotlin.reflect.KClass

class ConstructorTemplate constructor(
    /**
     * How to create the constructor
     */
    var constructor: ElementConstructor<*>,

    var id: String,

    /**
     * The element types that this constructor template is usable for
     */
    var elTypes: List<KClass<out Element>>,

    /**
     * The groups of constraint templates that can be used with this template
     *
     * To use this constructor template, user just need to specify enough constraint
     * for at least one group. Each group represents a way of adding parameters to run the constructor.
     *
     */
    var cgs: List<ConstraintGroup>

)
