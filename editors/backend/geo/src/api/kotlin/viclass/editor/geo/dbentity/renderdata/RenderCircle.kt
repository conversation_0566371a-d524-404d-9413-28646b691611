package viclass.editor.geo.dbentity.renderdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.buildCircleRenderProp
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Element

@BsonDiscriminator(key = "renderElementType")
data class RenderCircle @BsonCreator constructor(
    @BsonProperty("renderProp") override var renderProp: RenderProp,
    @BsonProperty("elType") override val elType: String = Circle::class.simpleName!!
) : RenderElement {

    @BsonProperty("elIndexes")
    override var elIndexes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("relIndex")
    override var relIndex: Int = -1

    @BsonProperty("name")
    override var name: String = ""

    @BsonProperty("length")
    var length: Double = 0.0

    @BsonProperty("usable")
    override var usable: Boolean = true

    @BsonProperty("deleted")
    override var deleted: Boolean? = null

    @BsonProperty("valid")
    override var valid: Boolean = true

    @BsonProperty("centerPointIdx")
    var centerPointIdx: Int = -1

    @BsonProperty("radius")
    var radius: Double = 0.0

    @BsonProperty("vertexRelIdxes")
    var vertexRelIdxes: MutableSet<Int> = mutableSetOf()

    override fun <T : Element> isMatchElement(el: T) = false

    override fun clone(): RenderElement {
        return copy().also {
            cloneBase(it)
            it.length = length
            it.centerPointIdx = centerPointIdx
            it.radius = radius
            it.vertexRelIdxes = vertexRelIdxes.toMutableSet()
        }
    }

    override fun setDefaultProp(): RenderProp {
        renderProp = buildCircleRenderProp(DocDefaultElRenderProp())
        return renderProp
    }
}