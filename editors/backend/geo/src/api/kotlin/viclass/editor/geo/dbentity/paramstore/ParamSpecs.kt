package viclass.editor.geo.dbentity.paramstore

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.ElementMappingType
import viclass.editor.geo.exceptions.ExtractionFailedException
import kotlin.reflect.KClass


/**
 * Represent a specification of parameters for a construction constraint. It contains the real
 * parameters for the construction param.
 *
 * Take for example the constructor OnLineWithLength which construct a point on a line and with
 * a distance to another point equal to some length. It needs two constraints:
 * - The line on which the point lies
 * - The lengthAssignment constraint
 *
 * For each constraint, we need a param specs.
 *
 * For the line constraint, we need a param spec where the 'name' parameter is mapped to the name of the line
 * For the lengthAssignment constraint, we need a param spec where
 * - `name` parameter is mapped to the name of line segment between the constructed point and the reference point
 * - `expression` parameter is mapped to an a length expression which will calculate the actual length needed.
 * - Example: AO = AB + 1/2*AC, here, O is the name of the constructed point and AO is the name of the length, its actual length is calculated by the expression AB + 1/2*AC
 *
 * It contains following information:
 * - The param template being used (so that types of each parameter name is known)
 * - The parameters - mapping from param name to the string that represent a value
 * - The template string being used (retrieved from the constraint group)
 */
data class ParamSpecs @BsonCreator constructor(
    /**
     * The id of the param template being used
     */
    @BsonProperty("paramDefId")
    var paramDefId: String,

    /**
     * Get this from the constraint template. We need to know exactly
     * the parameters specified here is for which constraint template
     * inside the constraint group.
     */
    @BsonProperty("indexInCG")
    var indexInCG: Int,

    /**
     * Flag to mark this param specs is optional
     */
    @BsonProperty("optional")
    var optional: Boolean,

    /**
     * The actual template used by this constraint. This is
     * the chosen template among equivalent templates as defined in the
     * chosen constraint group of the constructor template
     *
     * This is not the actual string content but the id of the string
     * to serve the multi-language translation purpose
     */
    @BsonProperty("tplStrLangId")
    var tplStrLangId: String,

    /**
     * The parameters. This is a map from the param name (as defined in the template) to
     * parameter value (a name of previously constructed elements or a constant value or an expression)
     *
     * The type of the parameters are inferred from the constraint template being used
     */
    @BsonProperty("params")
    var params: Map<String, ParamStore>,

    /**
     * The parameters. This is a map from the param name (as defined in the template) to
     * parameter value (a name of previously constructed elements or a constant value or an expression)
     *
     * The type of the parameters are inferred from the constraint template being used
     */
    @BsonProperty("dataTypes")
    var dataTypes: Map<String, String>? = mutableMapOf(),

    /**
     * The extractor id used for a particular parameter.
     * This map is empty when sent from the frontend, i.e. the extractor for a parameter is not yet
     * determined.
     *
     * This map must be set before ElConstruction is saved to database, so that when we load the ElConstruction from
     * the database, we don't have to re-determine the extractor to be used for the parameters inside this param specs.
     */
    @BsonProperty("extractorIds")
    var extractorIds: Map<String, String> = mutableMapOf()

) {

    fun getParam(paramName: String): ParamStore? {
        return params[paramName]
    }

    fun getParamDataType(paramName: String): KClass<out Element>? {
        val type = dataTypes?.get(paramName) ?: return null
        return ElementMappingType.elementMapping[type] ?: throw ExtractionFailedException("not found element type $type")
    }

    fun extractorForParam(paramName: String, extractorId: String) {
        (extractorIds as MutableMap<String, String>)[paramName] = extractorId
    }

    fun clone(): ParamSpecs {
        return ParamSpecs(
            paramDefId = paramDefId,
            indexInCG = indexInCG,
            optional = optional,
            tplStrLangId = tplStrLangId,
            params = params.mapValues { it.value.clone() },
            dataTypes = dataTypes?.toMutableMap(),
            extractorIds = extractorIds.toMutableMap()
        )
    }
}