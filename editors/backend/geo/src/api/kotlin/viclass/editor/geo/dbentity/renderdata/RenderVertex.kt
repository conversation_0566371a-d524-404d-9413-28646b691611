package viclass.editor.geo.dbentity.renderdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.buildPointRenderProp
import viclass.editor.geo.dbentity.movement.path.MovementPath
import viclass.editor.geo.elements.Point

@BsonDiscriminator(key = "renderElementType")
data class RenderVertex @BsonCreator constructor(
    @BsonProperty("renderProp") override var renderProp: RenderProp,
    @BsonProperty("elType") override val elType: String = Point::class.simpleName!!,
    @BsonProperty("movementPath") var movementPath: MovementPath? = null // null meaning unable to move
) : RenderElement {

    @BsonProperty("elIndexes")
    override var elIndexes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("relIndex")
    override var relIndex: Int = -1

    @BsonProperty("name")
    override var name: String = ""

    @BsonProperty("usable")
    override var usable: Boolean = true

    @BsonProperty("deleted")
    override var deleted: Boolean? = null

    @BsonProperty("valid")
    override var valid: Boolean = true

    @BsonProperty("coords")
    var coords: DoubleArray = doubleArrayOf(0.0, 0.0, 0.0)

    override fun clone(): RenderElement {
        return copy().also {
            cloneBase(it)
            it.coords = coords.clone()
            it.movementPath = movementPath?.clone()
        }
    }

    override fun setDefaultProp(): RenderProp {
        renderProp = buildPointRenderProp(DocDefaultElRenderProp())
        return renderProp
    }
}
