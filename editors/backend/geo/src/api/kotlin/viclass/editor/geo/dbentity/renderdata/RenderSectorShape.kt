package viclass.editor.geo.dbentity.renderdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.NamePattern
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.buildSectorRenderProp
import viclass.editor.geo.elements.CircularSector

/**
 *
 * <AUTHOR>
 */
@BsonDiscriminator(key = "renderElementType")
data class RenderSectorShape
@BsonCreator
constructor(
    @BsonProperty("renderProp") override var renderProp: RenderProp,
    @BsonProperty("elType") override val elType: String = CircularSector::class.simpleName!!
) : RenderElement {

    @BsonProperty("elIndexes") override var elIndexes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("relIndex") override var relIndex: Int = -1

    @BsonProperty("name") override var name: String = ""

    @BsonProperty("area") var area: Double = 0.0

    @BsonProperty("length") var length: Double = 0.0

    @BsonProperty("usable") override var usable: Boolean = true

    @BsonProperty("deleted") override var deleted: Boolean? = null

    @BsonProperty("valid") override var valid: Boolean = true

    @BsonProperty("centerPointIdx") var centerPointIdx: Int = -1

    @BsonProperty("startPointIdx") var startPointIdx: Int = -1

    @BsonProperty("endPointIdx") var endPointIdx: Int = -1

    @BsonProperty("radius") var radius: Double = 0.0

    @BsonProperty("vertexRelIdxes") var vertexRelIdxes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("arcRelIdx") var arcRelIdx: Int = -1

    @BsonProperty("lineRelIdxes") var lineRelIdxes: MutableSet<Int> = mutableSetOf()

    override fun clone(): RenderElement {
        return copy().also {
            cloneBase(it)
            it.area = area
            it.length = length
            it.centerPointIdx = centerPointIdx
            it.startPointIdx = startPointIdx
            it.endPointIdx = endPointIdx
            it.radius = radius
            it.vertexRelIdxes = vertexRelIdxes.toMutableSet()
            it.lineRelIdxes = lineRelIdxes.toMutableSet()
            it.arcRelIdx = arcRelIdx
        }
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(CircularSector::class, name) || this.name == null) return false
        return NamePattern.extractPointName(CircularSector::class, this.name).toSet() ==
            NamePattern.extractPointName(CircularSector::class, name).toSet()
    }

    override fun setDefaultProp(): RenderProp {
        renderProp = buildSectorRenderProp(DocDefaultElRenderProp())
        return renderProp
    }
}
