package viclass.editor.geo.extractable

import common.libs.logger.Logging
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.Extractable
import viclass.editor.geo.elements.Element

class ValueExpression constructor(
    val value: Double,
    private val constructions: List<ConstructionResult<out Element>>
) : Extractable, Logging {

    override fun constructions(): List<ConstructionResult<out Element>> {
        return constructions
    }
}
