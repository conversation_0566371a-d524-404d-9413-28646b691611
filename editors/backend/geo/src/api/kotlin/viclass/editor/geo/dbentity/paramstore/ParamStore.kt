package viclass.editor.geo.dbentity.paramstore

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import org.bson.codecs.pojo.annotations.BsonDiscriminator

/**
 * This interface describes the way of storing parameter values of a constraint
 *
 * For example:
 *
 * A constraint `On {AB}` which has the parameter value AB will be represented by
 * the ParamStoreValue which tells the system that it only has 1 value which is `AB`
 *
 * A constraint `Points [A, B, C]` which has the parameter value A, B, C will be represented
 * by the ParamStoreArray which tells the system that this constraint has an array
 * of values
 */
@BsonDiscriminator(key = "paramStoreType")
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = ParamStoreValue::class, name = "singleValue"),
    JsonSubTypes.Type(value = ParamStoreArray::class, name = "array")
)
interface ParamStore {
    fun clone(): ParamStore
}
