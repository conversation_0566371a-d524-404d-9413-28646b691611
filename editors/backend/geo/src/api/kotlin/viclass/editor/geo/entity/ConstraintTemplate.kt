package viclass.editor.geo.entity

/**
 * A constraint template specifies the shape of a constraint used
 * in a constructor.
 *
 * A constraint template contains:
 * - A constraint param definition => this specifies the number of variables, the types of the variable
 * - A set of template strings that can be displayed for this template
 */
class ConstraintTemplate {
    /**
     * The id of the param template being used
     */
    var paramDefId: String? = null

    /**
     * The index of this constraint template inside a constraint group that define this template.
     * There might be constructor that might have multiple constraint that have similar shape, and might
     * use the same tplStrLangIds, hence this index clearly tells which one it is inside the CG.
     *
     * When the user specify param specs for the constraint, the param specs will need to record down
     * which constraint template exactly, the specification for.
     */
    var indexInCG = 0

    /**
     * A list of equivalent templates that specifies what to show to users.
     * Each template needs to contain all the parameter names as specified in the
     * param template.
     *
     * This list doesn't store the actual template string but store the language id
     * of the template string
     */
    var tplStrLangIds: List<String> = listOf()

    /**
     * A list of equivalent templates description that specifies what to show to users.
     * Each template description needs to contain all the parameter names as specified in the
     * param template.
     *
     * This list doesn't store the actual template string but store the language id
     * of the template string
     */
    var tplDescription: List<String> = listOf()

    /**
     * A list of equivalent index of templates in constraint group.
     * This constraint template only be affected if all dependencies are filled.
     */
    var dependencies: List<Int> = listOf()

    /**
     * A flag to mark this is an optional constraint template
     */
    var optional: Boolean = false

    /**
     * A flag to mark this is a hidden constraint template, it's used for submitting constraints by mouse,
     * or in the internal service to store constraints.
     * This constraint will not be included in the response when fetching the constraint template.
     */
    var hidden: Boolean = false
}
