package viclass.editor.geo.constructor

import viclass.editor.geo.elements.Element

interface ConstructionResult<R> {
    var ctIdx: Int

    var newly: Boolean

    /**
     * When construction is done, the main element is the element that the user
     * requested to create
     * @return the main element
     */
    fun result(): R?

    /**
     * The list of all elements involve in the construction, both inferred elements and existing elements, and the result.
     * The result is the last element in this list.
     * @return
     */
    fun elements(): List<Element?>


    /**
     * The edges of the full dependency graph. A dependency graph is a directed graph, with edge specifies the
     * direct dependency relationship between two element.
     *
     * deps[i] is the set of indexes (into the elements() array) of the elements which are the direct
     * dependencies of element at index i.
     *
     * @return the (full graph) dependencies of elements in the inferredElements array
     */
    fun deps(): List<Set<Int>>

    fun resultDeps(): List<Int>

    fun edges(): List<Set<Int>>

    fun nodes(): List<Element>

    /**
     * An element is often constructed from inferred elements, which are extracted from parameter names.
     * The extraction itself is a form of construction. The final construction therefore needs to contain
     * the dependencies of the inferred elements as its own inferred elements. This method merges the
     * dependencies element of an inferred element into the dependency graph.
     * @param c the construction result to be merged
     */
    fun mergeAsDependency(c: ConstructionResult<out Element>)

    fun addDependency(e: Element, eDeps: List<Element>, isResultDep : Boolean)
}
