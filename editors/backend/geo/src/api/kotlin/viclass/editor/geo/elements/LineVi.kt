package viclass.editor.geo.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.geometry.euclidean.threed.line.Line3D

interface LineVi : Element, Parameterized, LengthAble {
    /**
     * @return the unit vector that is parallel with this line
     */
    val parallelVector: Vector3D

    /**
     *
     * @return the point P0 through which this line go through
     */
    val p1: Point

    val p2: Point?

    /**
     * @return apache line
     */
    fun line(): Line3D
}
