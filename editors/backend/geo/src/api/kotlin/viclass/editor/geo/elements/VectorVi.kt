package viclass.editor.geo.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D


/**
 * A class represent Vector in geometry editor context, it is not meant
 * to be a mathematical object, e.g. it should not be used
 * to represent normal or parallel vector for mathematical calculation. Use
 * vector defined in 3rd library for that
 */
interface VectorVi : LineSegment {
    fun root(): Point
    fun end(): Point
    fun parallelVector(): Vector3D
}
