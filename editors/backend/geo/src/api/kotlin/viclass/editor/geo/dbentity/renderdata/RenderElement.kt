package viclass.editor.geo.dbentity.renderdata

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import viclass.editor.geo.NamePattern
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.ElementMappingType

/**
 * RenderElement contains the information to render the elements:
 * 1. Contains the color and opacity information
 * 2. Contains the list of faces or other geometric information needed
 */
@BsonDiscriminator(key = "renderElementType")
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = RenderVertex::class),
    JsonSubTypes.Type(value = RenderPolygon::class),
    JsonSubTypes.Type(value = RenderLineSegment::class),
    JsonSubTypes.Type(value = RenderVector::class),
    JsonSubTypes.Type(value = RenderRay::class),
    JsonSubTypes.Type(value = RenderLine::class),
    JsonSubTypes.Type(value = RenderSector::class),
    JsonSubTypes.Type(value = RenderEllipse::class),
    JsonSubTypes.Type(value = RenderCircle::class),
    JsonSubTypes.Type(value = RenderCircleShape::class),
    JsonSubTypes.Type(value = RenderEllipseShape::class),
    JsonSubTypes.Type(value = RenderSectorShape::class),
    JsonSubTypes.Type(value = RenderAngle::class),
)
interface RenderElement {

    /**
     * The index of the element inside the geo document Chỉ được gán giá trị khi Render Element được tạo bởi một element
     * thực sự (được construct), những Render Element ảo thì không được gán. VD: với hình tam giác (triangle element),
     * sẽ tạo ra một render element (polygon render element), có 3 cạnh ảo (line render element) được tạo ra. Thì chỉ có
     * polygon render element mới có elIndex, còn line render element sẽ ko có elIndex, vì 3 cảnh ảo này được tạo ra chỉ
     * nhằm mục đích render, chưa hề được construct.
     */
    val elIndexes: MutableSet<Int>

    /** The index of the render element inside the render geo document */
    var relIndex: Int

    var name: String

    var renderProp: RenderProp

    /** mark element is usable or not, if unusable then this element will be removed from doc as soon as possible */
    var usable: Boolean

    /** mark element is deleted or not, if deleted then this element will not be used anymore */
    var deleted: Boolean?

    /**
     * mark element is valid or not, if invalid meaning element is out of construction, the construction cannot
     * construct this element
     */
    var valid: Boolean

    val elType: String

    fun <T : Element> isMatchElement(el: T): Boolean {
        val elClass = ElementMappingType.elementMapping[elType] ?: return false
        return elClass.isInstance(el) && elIndexes.contains(el.doc.getIndex(el))
    }

    fun isNameMatch(name: String): Boolean {
        val elClass = ElementMappingType.elementMapping[elType] ?: return false
        if (!NamePattern.isNameValid(elClass, name)) return false
        if (this.name == name) return true
        return false
    }

    fun clone(): RenderElement

    fun cloneBase(it: RenderElement): RenderElement {
        it.relIndex = relIndex
        it.name = name
        it.usable = usable
        it.deleted = deleted
        it.valid = valid
        it.elIndexes.addAll(elIndexes)
        return it
    }

    fun setDefaultProp(): RenderProp
}
