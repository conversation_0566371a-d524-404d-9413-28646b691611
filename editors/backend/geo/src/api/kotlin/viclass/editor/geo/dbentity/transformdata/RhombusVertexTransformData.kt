package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class RhombusVertexTransformData @BsonCreator constructor(
    @BsonProperty("center")
    val center: DoubleArray,
    @BsonProperty("vertexIdx")
    val vertexIdx: Int,
    @BsonProperty("lengthParamIdx")
    val lengthParamIdx: Int? = null,
): TransformData {
    override fun clone(): TransformData {
        return copy()
    }
}