package viclass.editor.geo.dbentity.movement.path

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class MovementSectorPath @BsonCreator constructor(
    @BsonProperty("pc") val pc: DoubleArray,
    @BsonProperty("ps") val ps: DoubleArray,
    @BsonProperty("pe") val pe: DoubleArray,
): MovementPath {
    override fun clone(): MovementPath {
        return copy()
    }
}
