package viclass.editor.geo.constructor

import viclass.editor.geo.dbentity.ElConstruction
import viclass.editor.geo.entity.ConstructorTemplate

/**
 * A construction contains the materialized constructor and the materialized parameters
 * needed to run the constructor. A construction is created from ElConstruction entity.
 *
 * The construction process is done as following:
 *
 * - From Element Construction, we know which constructor template is used, from there we know the constructor
 * class.
 * - From Element Construction, we can get the element constraint. From Element Constraint, we know
 * the constraint template being used and the actual parameter. Base on the parameter types, we create
 * the parameter extractors
 * - The parameters and constructors are stored inside the construction object and can be run whenever
 * they are needed
 *
 * When used, the constructor descriptor allows initialization of the correct constructor and then
 * apply the correct parameters.
 *
 */
interface Construction {
    /**
     * The entity corresponding to this construction
     * @return
     */
    var entity : ElConstruction

    /**
     * The constructor template being used
     * @return
     */
    var template : ConstructorTemplate

    /**
     * The element constructor used for creation of elements
     * @return the constructor
     */
    val constructor : ElementConstructor<*> get() = template.constructor

    /**
     * @return the list of params that actually used for this construction process
     */
    var params : List<ConstructionParams>

    /**
     * @return The constraint group that is actually used
     */
    val cgName: String get() = entity.cgName

    /**
     * @return name of the element that is constructed from this
     * construction
     */
    val elName : String? get() = entity.name

    /**
     * mark construction is usable, if unusable then this construction will be removed later on
     */
    val usable: Boolean get() = entity.usable

    val ctIdx: Int get() = entity.ctIdx

    fun mergeFrom(other: Construction) {
        other.entity.result = entity.result
        other.entity.ctIdx = entity.ctIdx
        entity = other.entity
        template = other.template
        params = other.params
    }
}
