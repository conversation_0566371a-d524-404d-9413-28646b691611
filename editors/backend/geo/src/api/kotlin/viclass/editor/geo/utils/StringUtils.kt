package viclass.editor.geo.utils

import java.text.Normalizer


/**
 *
 * <AUTHOR>
 */

/**
 * Removing Unicode accents and diacritics
 */
fun String.removeAccent(): String {
    return Normalizer.normalize(this, Normalizer.Form.NFKD)
        .replace("\\p{InCombiningDiacriticalMarks}+".toRegex(), "")
        .replace("đ", "d").replace("Đ", "D")
}

/**
 * generates all permutations of a string
 */
fun String.permute(result: String = ""): List<String> {
    return if (isEmpty())
        listOf(result)
    else flatMapIndexed { i, c ->
        removeRange(i, i + 1).permute(result + c)
    }
}
