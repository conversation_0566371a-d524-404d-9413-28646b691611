package viclass.editor.geo.constructor

import common.libs.logger.Logging
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.entity.ConstraintParamDef
import viclass.editor.geo.exceptions.ExtractionFailedException

/**
 * Construction Params is created from an ParamSpecs entity while
 * Construction is created from an ElConstruction.
 *
 * Normally, when a document is loaded from the database, it is cached inside
 * the memory as GeoDocImpl (GeoDocument is converted to GeoDocImpl inside memory)
 *
 * The in memory version contains the information to work with the document directly
 * (e.g. dependencies among elements, the actual elements in actual coordinate system,
 * the construction of each element) and hence it contains the Construction object for
 * each element. The construction object in turn, contains the actual ConstructionParams, which contains the actual
 * parameter extractors (which was created during the process of parameter extractor determination).
 *
 * When the construction of an element is re-run, we don't need to re-determine the parameter extractors
 * from the ElConstruction entity, because we already have them inside the Construction object, in memory.
 */
interface ConstructionParams : Logging {
    /**
     * @return The entity from which contains the raw data of the parameters
     */
    val specs : ParamSpecs

    /**
     * @return the param definition used for the constraint
     */
    val paramDef : ConstraintParamDef

    /**
     * Parameters are elements extracted from the document, or they can be
     * constant. Some examples are, a point or a line, or length of a line
     * segment, angle, etc...
     * @return the map of parameter extractor. The key is the name of the parameter
     * that the constructor can use to distinguish the parameters from each other
     * in case their type is the same
     */
    var paramExtractors : Map<String, ParameterExtractor>

    /**
     * Candidate extractors are extractors that are POTENTIALLY
     * usable to extract needed parameters
     */
    var candidateExtractors : Map<String, List<ParameterExtractor>>

    fun extractor(paramKind: String): List<ParameterExtractor> {
        return candidateExtractors[paramKind] ?: kotlin.run {
            logger.error("candidate extraction failed for {}", paramKind);
            throw ExtractionFailedException("candidate extraction failed for $paramKind")
        }
    }

    /**
     * When the constructor actually constructs the element, it would choose
     * among the candidate the exact parameter extractor needed.
     *
     * When chosen, constructor would call this method to set the actually used
     * parameter into the construction param.
     */
    fun useExtractor(paramKind : String, extractor : ParameterExtractor)
}
