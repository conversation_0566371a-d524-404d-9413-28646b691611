package viclass.editor.geo.dbentity

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.dbentity.elementdata.GeometryData
import viclass.editor.geo.dbentity.movement.path.MovementPath
import viclass.editor.geo.dbentity.transformdata.TransformData

/**
 * An element data is created by serializing a element in geodoc.
 * Each kind of element will store the necessary information so that it can deserialize
 * this data into its actual form.
 *
 * To give an example of  serialization process, a line segment which is the first element created
 * within a document will serialize as following
 *
 * - name: AB
 * - elType: LineSegment
 * - isPrimitive: false
 * - cindex: 0
 * - elIndex: 2 (0 and 1 is the index of the two element which are the start point and end point of this line segment)
 * - refElements: [0, 1] - it depends on the two end points
 * - geometry: this will be a byte array that store information such as [1, 0]. This array is interpreted
 * by the deserialization of the line segment as the start point of line segment is the element (of type point) at 1st index
 * of the element array of the geo document, and the end point of the line segment is the element (of type point) at 0 index
 */
@BsonDiscriminator
class ElementData @BsonCreator constructor(
    @BsonProperty("name")
    var name: String?,

    @BsonProperty("elType")
    var elType: String,

    /**
     * Construction index. The index of the constructor in the GeoDocEntity
     * that constructed this element, a constructor might create multiple
     * element.
     */
    @BsonProperty("cIdx")
    var cIdx: Int,

    /**
     * The index of this element in the GeoDocEntity
     */
    @BsonProperty("elIdx")
    var elIdx: Int,

    /**
     * The indexes of the elements in the GeoDocEntity that this element
     * depends on for construction.
     *
     * A standard validation is that the numbers in this array must be smaller than
     * elIndex
     */
    @BsonProperty("deps")
    var deps: List<Int>,

    @BsonProperty("inferred")
    var inferred: Boolean,

    @BsonProperty("transformer")
    var transformer: String? = null,

    @BsonProperty("transformData")
    var transformData: TransformData? = null,

    @BsonProperty("movementPath")
    var movementPath: MovementPath? = null,

    /**
     * The actual geometry information of the element, serialized into
     * a byte array. For example,
     * - Points have their coordinates serialized into byte array
     * - Line Segments have the coordinates of the two end points serialized
     * - Triangle have the 3 vertices coordinates serialized
     * - Line / Ray has the pass through point / root point and the vector serialized
     * - Circle has the center and its radius serialized
     * - etc...
     */
    @BsonProperty("geometry")
    var geometry: GeometryData? = null,

    /**
     * mark element is usable or not, if unusable then this element will be deleted from doc as soon as possible
     */
    @BsonProperty("usable")
    var usable: Boolean = true,

    /**
     * mark element is usable or not, if unusable then this element will be deleted from doc as soon as possible
     */
    @BsonProperty("deleted")
    var deleted: Boolean? = null,

    /**
     * mark element is usable or not, if unusable then this element will be deleted from doc as soon as possible
     */
    @BsonProperty("valid")
    var valid: Boolean = true,
)
