package viclass.editor.geo.dbentity

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.dbentity.paramstore.ParamSpecs

data class ElConstructionResult @BsonCreator constructor(
    @BsonProperty("result")
    var result: Int,

    @BsonProperty("resultDeps")
    var resultDeps: List<Int>,

    @BsonProperty("nodes")
    var nodes: List<Int>,

    @BsonProperty("edges")
    var edges: Array<IntArray>
) {
    fun clone(): ElConstructionResult {
        return copy()
    }
}

/**
 * Contains the information for construction of elements. It basically contains
 *
 * - What constructor template is used
 * - What are the constraint specified for that constructor template
 *
 */
data class ElConstruction @BsonCreator constructor(
    /**
     * Constructor template id
     */
    @BsonProperty("ctId")
    val ctId: String,

    /**
     * Actual element type being created
     * A constructor template can create multiple element types
     */
    @BsonProperty("elType")
    val elType: String,

    /**
     * The name that user set for the element
     */
    @BsonProperty("name")
    var name: String?,

    /**
     * The constraint group index of the constructor template being fulfilled by
     * list of element constraint
     */
    @BsonProperty("cgName")
    val cgName: String,

    /**
     * List of constraint specified for the element construction
     */
    @BsonProperty("paramSpecs")
    var paramSpecs: List<ParamSpecs>? = null,

    @BsonProperty("result")
    var result: ElConstructionResult? = null,

    /**
     * Construction index
     */
    @BsonProperty("ctIdx")
    var ctIdx: Int = -1,

    @BsonProperty("usable")
    var usable: Boolean = true
) {
    fun clone(): ElConstruction {
        return copy().also {
            it.paramSpecs = it.paramSpecs?.map { it.clone() }
            it.result = it.result?.clone()
        }
    }
}