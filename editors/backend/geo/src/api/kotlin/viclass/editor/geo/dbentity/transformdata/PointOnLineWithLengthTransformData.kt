package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class PointOnLineWithLengthTransformData @BsonCreator constructor(
    @BsonProperty("lengthParamIdx") val lengthParamIdx: Int,
    @BsonProperty("lengthParamKind") val lengthParamKind: String,
    @BsonProperty("nthParamIdx") val nthParamIdx: Int,
    @BsonProperty("root") val root: DoubleArray,
    @BsonProperty("unitVector") val unitVector: DoubleArray,
): TransformData {
    override fun clone(): TransformData {
        return copy()
    }
}