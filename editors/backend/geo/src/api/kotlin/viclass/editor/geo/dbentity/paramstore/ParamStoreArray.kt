package viclass.editor.geo.dbentity.paramstore

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "ParamStoreArray", key = "paramStoreType")
data class ParamStoreArray @BsonCreator constructor(
    @BsonProperty("values")
    var values: List<String>
) : ParamStore {
    override fun clone(): ParamStore {
        return ParamStoreArray(values.toList())
    }
}
