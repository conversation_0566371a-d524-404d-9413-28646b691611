package viclass.editor.geo.elements

import viclass.editor.geo.dbentity.movement.path.MovementPath
import viclass.editor.geo.dbentity.transformdata.TransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.transformer.Transformer
import kotlin.reflect.KClass

interface Element {
    val clazz: KClass<out Element>
        get() = this::class

    /**
     * @return document that contains this element
     */
    val doc: GeoDoc

    var transformData: TransformData?
        get() = null
        set(_) {}

    var movementPath: MovementPath?
        get() = null
        set(_) {}

    var transformer: Transformer<TransformData>?
        get() = null
        set(_) {}

    /**
     * @return list of vertices of this element as point within the doc
     */
    fun vertices() : List<Point>

    /**
     * Contains a specific logic to check if certain name match this element.
     *
     * For point, it is pretty simple, but for line, triangle, rectangle, etc... it gets more complicated
     * For example, a triangle ABC and BCA are the same triangle.
     * A line can have name x which is single character.
     * @param name
     * @return whether a name match with this element.
     */
    fun isNameMatch(name: String): Boolean

    /**
     * Merges the properties of a newly constructed `Element` into an existing one.
     * This method is called when merging construction elements, transferring values
     * from a newly created element to a previously constructed element.
     *
     * @param other The newly constructed `Element` whose properties will be copied.
     */
    fun mergeFrom(other: Element) {
        this.usable = other.usable
        this.valid = other.valid
        this.transformData = other.transformData?.clone()
        this.transformer = other.transformer
        this.movementPath = other.movementPath?.clone()
    }

    /**
     * Checks whether the element is valid.
     * This method is called externally and is automatically triggered when the constructor sets the result.
     *
     * @throws InvalidElementException If the element is invalid.
     */
    fun validate() {}

    /**
     * @return name of the element
     */
    var name: String?

    /**
     * mark element is usable or not, if unusable then this element will be removed from doc as soon as possible
     */
    var usable: Boolean

    /**
     * mark element is deleted or not, if deleted then this element will not be used anymore
     */
    var deleted: Boolean?

    /**
     * mark element is valid or not, if invalid meaning element is out of construction,
     * the construction cannot construct this element
     */
    var valid: Boolean
}
