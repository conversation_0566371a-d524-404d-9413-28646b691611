package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class PointOnPolygonTransformData @BsonCreator constructor(
    @BsonProperty("idxInPointParam") val idxInPointParam: Int,
) : TransformData {
    override fun clone(): TransformData {
        return copy()
    }
}
