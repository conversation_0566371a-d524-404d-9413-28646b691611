package viclass.editor.geo.models.response

import viclass.editor.geo.dbentity.ElConstruction
import viclass.editor.geo.dbentity.renderdata.RenderElement
import viclass.editor.geo.models.request.RenameElementModel

data class ApplyConstructionResponse(
    val render : Set<RenderElement>,
    val elIdxes: Set<Int>,
    val ctIndexes: Set<Int>,
    val renameEls: Set<RenameElementModel> = emptySet()
)

data class ReconstructionResponse(
    val render : List<RenderElement>,
    val oldConstruction: ElConstruction,
    val newConstruction: ElConstruction,
)
