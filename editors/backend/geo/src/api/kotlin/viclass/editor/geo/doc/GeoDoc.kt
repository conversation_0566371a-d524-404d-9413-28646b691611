package viclass.editor.geo.doc

import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.elements.Element
import viclass.editor.geo.render.RenderDoc
import kotlin.reflect.KClass

/**
 * A geo doc provides access to the elements within the
 * document. This is the place where transformation, and
 * geometry calculation takes place.
 */
interface GeoDoc {
    /**
     * Each document will have a unique id
     * @return
     */
    val id: String

    /**
     * A document can work in 2D or 3D mode.
     * @return The number of dimension that this document is in.
     */
    val numDim: Int

    /**
     * List of constructions, each of this is correspondent
     * to one ElementConstruction from GeoDocument entity. However,
     * it also contains the constructors and param extractors
     * @return
     */
    var constructions: List<Construction>

    /**
     * @return list of elements
     */
    var elements: List<Element>

    var docDefaultElRenderProp: DocDefaultElRenderProp

    /**
     * A RenderDoc can be returned from the GeoDoc. If the render doc is
     * not yet created, it will be created through a rendering process.
     * @return
     */
    var renderDoc: RenderDoc

    fun getIndex(el: Element): Int?

    /**
     * @param index
     * @return the list of element created by the construction at a particular index
     */
    fun elementsFromConstruction(index: Int): List<Element>

    /**
     * Check if an element already added into this document
     * @param e check if Element e exists within the document
     * @return true if the element exists
     * false if the element doesn't exist
     */
    fun hasElement(e: Element): Boolean

    /**
     * Return the list of elements that an element
     * depends on DIRECTLY
     * @param e
     * @return
     */
    fun dependencies(e: Element): List<Element>

    /**
     * An element can be directly created by users OR
     * it can be created as a results of creating another element. The
     * second case is called an inferred element. Inferred element is useful
     * because we can use it to create additional element.
     * @param e
     * @return
     */
    fun isInferred(e: Element): Boolean

    /**
     * Find an element of a specific type with certain name. Element of subsclass of <T>
     * will be returned if name matched too.
     * @param name string, name of the element to be retrieve
     * @param elType type of the element
     * @return Optional containing the element.
     * @param <T> Type of the element to be found
    </T> */
    fun <T : Element> findElementByName(name: String?, elType: KClass<T>, ctIdx: Int? = null, includeSameCtIdx: Boolean? = null): T?

    /**
     * Merge the construction result into an existing document.
     * This method add the element into the list of elements together with the
     * construction that used to create the element as well as all the dependencies.
     *
     * @return the construction index after merged
     */
    fun mergeConstructionResult(cr: ConstructionResult<out Element>, construction: Construction)

    fun reMergeConstructionResult(cr: ConstructionResult<out Element>, construction: Construction): Set<Element>

    fun getConstructionIndex(el: Element): Int?

    fun updateElsValid(idxes: Set<Int>, valid: Boolean)

    fun updateElsDeleted(idxes: Set<Int>, deleted: Boolean?)

    fun updateElsUsable(idxes: Set<Int>, usable: Boolean)

    fun updateConstructionsUsable(idxes: Set<Int>, usable: Boolean)

    fun clearUnusableElements()

    fun clearUnusableConstruction()
}
