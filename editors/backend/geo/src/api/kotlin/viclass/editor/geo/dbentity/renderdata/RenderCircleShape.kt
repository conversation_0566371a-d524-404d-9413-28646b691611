package viclass.editor.geo.dbentity.renderdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.NamePattern
import viclass.editor.geo.dbentity.*
import viclass.editor.geo.elements.Circle

@BsonDiscriminator(key = "renderElementType")
data class RenderCircleShape @BsonCreator constructor(
    @BsonProperty("renderProp") override var renderProp: RenderProp,
    @BsonProperty("elType") override val elType: String = Circle::class.simpleName!!
) : RenderElement {

    @BsonProperty("elIndexes")
    override var elIndexes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("relIndex")
    override var relIndex: Int = -1

    @BsonProperty("name")
    override var name: String = ""

    @BsonProperty("area")
    var area: Double = 0.0

    @BsonProperty("perimeter")
    var perimeter: Double = 0.0

    @BsonProperty("usable")
    override var usable: Boolean = true

    @BsonProperty("deleted")
    override var deleted: Boolean? = null

    @BsonProperty("valid")
    override var valid: Boolean = true

    @BsonProperty("radius")
    var radius: Double = 0.0

    @BsonProperty("centerPointIdx")
    var centerPointIdx: Int = -1   // index of the vertex that is the center

    @BsonProperty("vertexRelIdxes")
    var vertexRelIdxes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("arcRelIdx")
    var arcRelIdx: Int = -1

    override fun isNameMatch(_name: String): Boolean {
        if (!NamePattern.isNameValid(Circle::class, _name)) return false

        if (name == _name) return true

        NamePattern[Circle::class]!![0].find(_name)?.let {
            if (it.groupValues.size == 2) {
                val p1Name = it.groupValues[1]
                if ("($p1Name)" == name) return true
            }
            if (it.groupValues.size == 3) {
                val p1Name = it.groupValues[1]
                val p2Name = it.groupValues[2]

                if (p1Name == p2Name) // not a valid name
                    return false

                if ("($p1Name;$p2Name)" == name) return true
            }
        }

        return false
    }

    override fun clone(): RenderElement {
        return copy().also {
            cloneBase(it)
            it.area = area
            it.perimeter = perimeter
            it.radius = radius
            it.centerPointIdx = centerPointIdx
            it.vertexRelIdxes = vertexRelIdxes.toMutableSet()
            it.arcRelIdx = arcRelIdx
        }
    }

    override fun setDefaultProp(): RenderProp {
        renderProp = buildCircleShapeRenderProp(DocDefaultElRenderProp())
        return renderProp
    }
}
