package viclass.editor.geo.dbentity.elementdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "CircleGeometry", key = "geoDataType")
data class CircleGeometry @BsonCreator constructor(
    @BsonProperty("version")
    override var version: Int,

    @BsonProperty("centerIdx")
    var centerIdx: Int,

    @BsonProperty("radius")
    var radius: Double,

    ) : GeometryData