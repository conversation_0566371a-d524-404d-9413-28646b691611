package viclass.editor.geo.dbentity

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.*
import viclass.editor.geo.dbentity.renderdata.GeoStrokeStyle
import viclass.editor.geo.dbentity.renderdata.RenderElement
import viclass.editor.geo.render.RenderDocState
import java.util.*

/**
 * This entity contains the actual information of the
 */
data class GeoDocument @BsonCreator constructor(
    @BsonId
    @BsonRepresentation(BsonType.OBJECT_ID)
    var id: String,     // Hex string represent the document

    @BsonProperty("numDim")
    val numDim: Int,

    /**
     * List of element construction. Order matters.
     */
    @BsonProperty("elConstructions")
    val elConstructions: List<ElConstruction>,

    /**
     * List of constructed element
     */
    @BsonProperty("elData")
    val elData: List<ElementData>,

    /**
     * List of rendered element
     */
    @BsonProperty("renderEl")
    val renderEl: List<RenderElement>,

    @BsonProperty("docDefaultElRenderProp")
    val docDefaultElRenderProp: DocDefaultElRenderProp,

    @BsonProperty("docRenderProp")
    val docRenderProp: DocRenderProp,

    @BsonProperty("version")
    var version: Int = 1,

    @BsonProperty("createdAt")
    var createdAt: Date = Date(),

    @BsonProperty("updatedAt")
    var updatedAt: Date = Date(),
)

data class DocRenderProp @BsonCreator constructor(
    @BsonProperty("screenUnit")
    var screenUnit: Int = 10,
    @BsonProperty("canvasWidth")
    var canvasWidth: Double,
    @BsonProperty("canvasHeight")
    var canvasHeight: Double,
    @BsonProperty("scale")
    var scale: Double = 1.0,
    @BsonProperty("translation")
    var translation: DoubleArray = doubleArrayOf(0.0, 0.0, 0.0),
    @BsonProperty("rotation")
    var rotation: DoubleArray = doubleArrayOf(0.0, 0.0, 0.0),

    // Grid
    @BsonProperty("grid")
    var grid: Boolean = true,
    @BsonProperty("axis")
    var axis: Boolean = true,
    @BsonProperty("detailGrid")
    var detailGrid: Boolean = false,

    // Snap
    @BsonProperty("snapMode")
    var snapMode: Boolean = false,
    @BsonProperty("snapToExistingPoints")
    var snapToExistingPoints: Boolean = false,
    @BsonProperty("snapToGrid")
    var snapToGrid: Boolean = false,

    @BsonProperty("namingMode")
    var namingMode: Boolean = false,
) {
    fun toRenderDocState(docId: String, docNumDim: Int): RenderDocState {
        return RenderDocState(
            docId,
            docNumDim,
            screenUnit,
            canvasWidth,
            canvasHeight,
            scale,
            translation,
            rotation,
            grid,
            axis,
            detailGrid,
            snapMode,
            snapToExistingPoints,
            snapToGrid,
            namingMode
        )
    }

    fun clone(): DocRenderProp {
        return copy()
    }
}

@BsonDiscriminator(key = "docDefaultElRenderProp")
open class DocDefaultElRenderProp @BsonCreator constructor() {
    open fun clone(): DocDefaultElRenderProp {
        val prop = DocDefaultElRenderProp()
        prop.color = color
        prop.lineColor = lineColor
        prop.pointColor = pointColor
        prop.opacity = opacity
        prop.lineWeight = lineWeight
        prop.strokeStyle = strokeStyle
        prop.spaceFromArcToCorner = spaceFromArcToCorner
        return prop
    }

    @BsonProperty("color")
    var color: String? = null
    @BsonProperty("lineColor")
    var lineColor: String? = null
    @BsonProperty("pointColor")
    var pointColor: String? = null
    @BsonProperty("opacity")
    var opacity: Byte? = null
    @BsonProperty("lineWeight")
    var lineWeight: Byte? = null
    @BsonProperty("strokeStyle")
    var strokeStyle: GeoStrokeStyle? = null
    @BsonProperty("spaceFromArcToCorner")
    var spaceFromArcToCorner: Float? = null
}