package viclass.editor.geo.dbentity

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.dbentity.renderdata.GeoStrokeStyle
import viclass.editor.geo.dbentity.renderdata.GeoStrokeStyle.Solid
import viclass.editor.geo.elements.Angle

enum class AngleType {
    as1, as2
}

/**
 * This class represents the rendering properties
 * that needs to be applied to an element. For example, line color, fill color
 * opacity, etc..
 */
@BsonDiscriminator(key = "renderPropType")
open class RenderProp @BsonCreator constructor() {
    open fun clone(): RenderProp {
        val prop = RenderProp()

        prop.color = color
        prop.lineColor = lineColor
        prop.pointColor = pointColor
        prop.opacity = opacity
        prop.lineWeight = lineWeight
        prop.hidden = hidden
        prop.strokeStyle = strokeStyle
        prop.showLabel = showLabel
        prop.label = label
        prop.swapLabelPosition = swapLabelPosition
        prop.labelType = labelType
        prop.spaceFromArcToCorner = spaceFromArcToCorner
        prop.showAngleTypes = showAngleTypes
        prop.angleArc = angleArc
        prop.enableEqualSegmentSign = enableEqualSegmentSign
        prop.equalSegmentSign = equalSegmentSign
        prop.size = size
        prop.showArcLabel = showArcLabel
        prop.arcLabelType = arcLabelType
        prop.arcLabelContent = arcLabelContent
        prop.pointLabelType = pointLabelType
        prop.pointLabelFreeContent = pointLabelFreeContent
        prop.showPointLabel = showPointLabel

        return prop
    }

    @BsonProperty("color")
    var color: String? = "#00aeef"

    @BsonProperty("lineColor")
    var lineColor: String? = "#00aeef"

    @BsonProperty("pointColor")
    var pointColor: String? = "#121414"

    @BsonProperty("opacity")
    var opacity: Byte? = 100.toByte()

    @BsonProperty("lineWeight")
    var lineWeight: Byte? = 1.toByte()

    /**
     * Whether an element should be shown. By default,
     * an element that is inferred will be hidden, and
     * an element that is created by user will be shown.
     */
    @BsonProperty("hidden")
    var hidden: Boolean? = false

    @BsonProperty("strokeStyle")
    var strokeStyle: GeoStrokeStyle? = Solid

    @BsonProperty("showLabel")
    var showLabel: Boolean? = false

    @BsonProperty("label")
    var label: String? = ""

    @BsonProperty("swapLabelPosition")
    var swapLabelPosition: Boolean? = false

    @BsonProperty("labelType")
    var labelType: String? = ""

    @BsonProperty("spaceFromArcToCorner")
    var spaceFromArcToCorner: Float? = 30f

    @BsonProperty("degree")
    var degree: Double? = 0.0

    @BsonProperty("showAngleTypes")
    var showAngleTypes: AngleType? = AngleType.as2

    @BsonProperty("angleArc")
    var angleArc: Int? = 1

    @BsonProperty("enableEqualSegmentSign")
    var enableEqualSegmentSign: Boolean? = false

    @BsonProperty("equalSegmentSign")
    var equalSegmentSign: String? = ""

    @BsonProperty("size")
    var size: Float? = 0f

    @BsonProperty("showArcLabel")
    var showArcLabel: Boolean? = false

    @BsonProperty("arcLabelType")
    var arcLabelType: String? = ""

    @BsonProperty("arcLabelContent")
    var arcLabelContent: String? = ""

    @BsonProperty("pointLabelType")
    var pointLabelType: String? = "name"

    @BsonProperty("pointLabelFreeContent")
    var pointLabelFreeContent: String? = ""

    @BsonProperty("showPointLabel")
    var showPointLabel: Boolean? = true

    @BsonProperty("isShowAngleSize")
    var isShowAngleSize: Boolean? = false

    fun setColor(color: String): RenderProp {
        this.color = color
        return this
    }

    fun setLineColor(lineColor: String): RenderProp {
        this.lineColor = lineColor
        return this
    }

    fun setPointColor(pointColor: String): RenderProp {
        this.pointColor = pointColor
        return this
    }

    fun setOpacity(opacity: Byte): RenderProp {
        this.opacity = opacity
        return this
    }

    fun setLineWeight(lineWeight: Byte): RenderProp {
        this.lineWeight = lineWeight
        return this
    }

    fun setHidden(hidden: Boolean): RenderProp {
        this.hidden = hidden
        return this
    }

    fun setStrokeStyle(strokeStyle: GeoStrokeStyle): RenderProp {
        this.strokeStyle = strokeStyle
        return this
    }

    fun setSpaceFromArcToCorner(spaceFromArcToCorner: Float): RenderProp {
        this.spaceFromArcToCorner = spaceFromArcToCorner
        return this
    }
}

fun buildPointRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setPointColor(defaultElRenderProp.pointColor ?: "#121414").setLineWeight(5)
}

fun buildLineRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setLineColor(defaultElRenderProp.lineColor ?: "#00AEEF")
        .setLineWeight(defaultElRenderProp.lineWeight ?: 1).setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}

fun buildSectorRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setLineColor(defaultElRenderProp.lineColor ?: "#31E37C")
        .setOpacity(defaultElRenderProp.opacity ?: 5).setLineWeight(defaultElRenderProp.lineWeight ?: 1)
        .setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}

fun buildSectorShapeRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setColor(defaultElRenderProp.color ?: "#31E37C")
        .setOpacity(defaultElRenderProp.opacity ?: 5).setLineWeight(defaultElRenderProp.lineWeight ?: 1)
        .setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}

fun buildCircleRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setLineColor(defaultElRenderProp.lineColor ?: "#31E37C")
        .setOpacity(defaultElRenderProp.opacity ?: 5).setLineWeight(defaultElRenderProp.lineWeight ?: 1)
        .setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}

fun buildCircleShapeRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setColor(defaultElRenderProp.color ?: "#31E37C")
        .setOpacity(defaultElRenderProp.opacity ?: 5).setLineWeight(defaultElRenderProp.lineWeight ?: 1)
        .setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}

fun buildAngleRenderProp(defaultElRenderProp: DocDefaultElRenderProp, element: Angle? = null): RenderProp {
    val props =
        RenderProp().setColor(defaultElRenderProp.color ?: "#31E37C").setOpacity(defaultElRenderProp.opacity ?: 5)
            .setLineWeight(defaultElRenderProp.lineWeight ?: 1)
            .setSpaceFromArcToCorner(defaultElRenderProp.spaceFromArcToCorner ?: 30f)

    return props
}

fun buildEllipseRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setLineColor(defaultElRenderProp.lineColor ?: "#31E37C")
        .setOpacity(defaultElRenderProp.opacity ?: 5).setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}

fun buildEllipseShapeRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setColor(defaultElRenderProp.color ?: "#31E37C")
        .setOpacity(defaultElRenderProp.opacity ?: 5).setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}

fun buildSectorLineRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setLineColor(defaultElRenderProp.lineColor ?: "#31E37C")
        .setLineWeight(defaultElRenderProp.lineWeight ?: 1).setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}

fun buildPolygonRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setColor(defaultElRenderProp.color ?: "#00AEEF").setOpacity(defaultElRenderProp.opacity ?: 5)
        .setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}

fun buildPolygonEdgeRenderProp(defaultElRenderProp: DocDefaultElRenderProp): RenderProp {
    return RenderProp().setColor(defaultElRenderProp.color ?: "#00AEEF")
        .setLineColor(defaultElRenderProp.lineColor ?: "#00AEEF").setLineWeight(defaultElRenderProp.lineWeight ?: 1)
        .setStrokeStyle(defaultElRenderProp.strokeStyle ?: Solid)
}