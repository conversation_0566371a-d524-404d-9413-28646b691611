package viclass.editor.geo.doc

import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.DocRenderProp
import viclass.editor.geo.dbentity.GeoDocument
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.renderdata.RenderElement
import viclass.editor.geo.elements.Element
import viclass.editor.geo.models.request.ElConstructionRequest
import viclass.editor.geo.models.request.RenameElementModel
import viclass.editor.geo.models.response.MoveElementResponse
import viclass.editor.geo.models.response.ReconstructionResponse

/**
 * This is the entry point to apply an user's construction
 * specification (ELConstruction) into an existing GeoDoc.
 *
 * It also is responsible for creating new document, apply transformation
 */
interface GeoDocCommander {

    /**
     * Find the document. If doesn't exist inside memory, retrieve it from DB
     */
    suspend fun retrieveDoc(id: String): GeoDoc?
    
    /**
     * Save a whole document into database
     */
    suspend fun saveDocToDB(geoDoc: GeoDoc)

    suspend fun replaceDocToDB(geoDoc: GeoDoc)

    suspend fun updateDocSize(docId: String, width: Double, height: Double)

    suspend fun updateElsProp(
        docId: String,
        elIndexes: List<Int>,
        elementRenderProps: RenderProp
    )

    suspend fun renamePointElement(docId: String, relIndex: Int, newName: String): List<RenameElementModel>

    suspend fun renameLineElement(docId: String, relIndex: Int, newName: String): List<RenameElementModel>

    suspend fun updateDocState(docId: String, docRenderProp: DocRenderProp): DocRenderProp?

    suspend fun updateDocDefaultElRenderProps(
        docId: String,
        docDefaultElRenderProp: DocDefaultElRenderProp
    ): DocDefaultElRenderProp?

    suspend fun deleteRenderElements(docId: String, relIndexes: List<Int>): Pair<List<Int>, List<Int>>

    suspend fun updateDeletedElements(docId: String, deleted: Boolean?, elIndexes: List<Int>, relIndexes: List<Int>)

    suspend fun updateUsableElements(docId: String, usable: Boolean, ctIdxes: List<Int>, elIndexes: List<Int>, relIndexes: List<Int>)

    suspend fun clearUnusableElements(docId: String)

    suspend fun deleteDocument(docId: String)

    suspend fun duplicateDocument(docIds: List<String>): Map<String, String?>

    fun convertDocToEntity(geoDoc: GeoDoc): GeoDocument

    fun replaceDoc(entity: GeoDocument): GeoDoc

    fun convertEntityToDoc(entity: GeoDocument): GeoDoc

    suspend fun newDoc(numDimension: Int, canvasWidth: Double, canvasHeight: Double, unit: Int): GeoDoc

    /**
     * Apply an el construction to the in memory document. The rendering of the construction
     * result must follow immediately to ensure the rendered content and the document are in sync
     *
     * TODO this method needs to be sequentially executed
     */
    suspend fun applyConstruction(
        doc: GeoDoc,
        cmd: ElConstructionRequest
    ): Pair<Construction, ConstructionResult<out Element>>

    /**
     * Apply an el re-construction to the in memory document. The rendering of the construction
     * result must follow immediately to ensure the rendered content and the document are in sync
     *
     * TODO this method needs to be sequentially executed
     */
    suspend fun reconstruct(doc: GeoDoc, cmd: ElConstructionRequest, ctIdx: Int): ReconstructionResponse


    /**
     * Apply a transformation. This method accept the element that is being transformed
     * (normally, a point) and the transformation it is performing (normally, a translation
     * to a new position) and calculate the new position of the elements inside the GeoDoc
     * because of this transformation.
     */
    suspend fun moveElement(doc: GeoDoc, reIdx: Int, pos: DoubleArray): MoveElementResponse

    /**
     * Render all elements within a construction result.
     * This basically create the render information within the render doc
     * for these elements. For the inferred elements, they are by default hidden
     * and has the hidden flag is true.
     *
     * The prop is the render properties of the construction result.
     * For inferred element, they are rendered using default prop, they are hidden anyway
     *
     * @return The list of render element. This is the information we should return to client
     */
    suspend fun renderConstructionResult(
        result: ConstructionResult<out Element>, prop: RenderProp? = null
    ): List<RenderElement>

    fun <T: GeoDoc>cloneDoc(doc: T): T
}
