package viclass.editor.geo.dbentity.elementdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "LineGeometry", key = "geoDataType")
data class LineGeometry @BsonCreator constructor(
    @BsonProperty("version")
    override var version: Int,

    @BsonProperty("spIdx")
    var spIdx: Int,

    @BsonProperty("epIdx")
    var epIdx: Int?,

    @BsonProperty("pVX")
    var pVX: Double,

    @BsonProperty("pVY")
    var pVY: Double,

    @BsonProperty("pvZ")
    var pvZ: Double,

) : GeometryData