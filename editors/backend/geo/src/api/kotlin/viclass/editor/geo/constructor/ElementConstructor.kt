package viclass.editor.geo.constructor

import common.libs.logger.Logging
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.entity.ConstructorTemplate
import kotlin.reflect.KClass

/**
 * An element constructor produce a shape from the properties provided.
 * The dependencies among elements will be added to the document.
 * @param <R>
</R> */
interface ElementConstructor<R : Element>: Logging {
    /**
     * Do final construction and return the result
     * @return the element
     */
    fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<R>

    /**
     * Generate the template object of this constructor
     * @return
     */
    fun template(): ConstructorTemplate
    fun outputType(): KClass<R>
}
