package viclass.editor.geo.render

import viclass.editor.geo.dbentity.DocRenderProp


/**
 *
 * <AUTHOR>
 */
data class RenderDocState constructor(
    /**
     * Id of the geo doc that generates this render doc
     * When a render doc is created and the geodoc itself has not been persisted
     * then this id might be null.
     */
    val docId: String,

    /**
     * A document can work in 2D or 3D mode.
     */
    val numDim: Int = 2,

    /**
     * Number of pixel per 1 unit coordinate
     */
    var screenUnit: Int = 10,

    /**
     * Number of width unit
     */
    var canvasWidth: Double = 200.0,

    /**
     * Number of height unit
     */
    var canvasHeight: Double = 200.0,

    /**
     * Current zoom of the doc
     * @return
     */
    var scale: Double = 1.0,

    /**
     * Current translation of the doc, in coordinate unit
     * @return the translation matrix
     */
    var translation: DoubleArray = doubleArrayOf(0.0, 0.0, 0.0),

    /**
     * Current rotation of the doc, in coordinate unit
     * @return the rotation matrix
     */
    var rotation: DoubleArray = doubleArrayOf(0.0, 0.0, 0.0),

    // Grid
    var grid: Boolean = true,
    var axis: Boolean = true,
    var detailGrid: Boolean = false,

    // Snap
    var snapMode: Boolean = false,
    var snapToExistingPoints: Boolean = false,
    var snapToGrid: Boolean = false,

    var namingMode: Boolean = true,
) {
    fun clone(): RenderDocState {
        return RenderDocState(
            docId = docId,
            numDim = numDim,
            screenUnit = screenUnit,
            canvasWidth = canvasWidth,
            canvasHeight = canvasHeight,
            scale = scale,
            translation = translation,
            rotation = rotation,
            axis = axis,
            grid = grid,
            detailGrid = detailGrid,
            snapMode = snapMode,
            snapToExistingPoints = snapToExistingPoints,
            snapToGrid = snapToGrid,
            namingMode = namingMode
        )
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as RenderDocState

        if (docId != other.docId) return false
        if (numDim != other.numDim) return false
        if (screenUnit != other.screenUnit) return false
        if (canvasWidth != other.canvasWidth) return false
        if (canvasHeight != other.canvasHeight) return false
        if (scale != other.scale) return false
        if (grid != other.grid) return false
        if (!translation.contentEquals(other.translation)) return false
        if (!rotation.contentEquals(other.rotation)) return false
        return true
    }

    override fun hashCode(): Int {
        var result = docId.hashCode()
        result = 31 * result + numDim
        result = 31 * result + screenUnit.hashCode()
        result = 31 * result + canvasWidth.hashCode()
        result = 31 * result + canvasHeight.hashCode()
        result = 31 * result + scale.hashCode()
        result = 31 * result + grid.hashCode()
        result = 31 * result + translation.contentHashCode()
        result = 31 * result + rotation.contentHashCode()
        return result
    }

    fun toDocRenderProp(): DocRenderProp {
        return DocRenderProp(
            screenUnit = screenUnit,
            canvasWidth = canvasWidth,
            canvasHeight = canvasHeight,
            scale = scale,
            translation = translation,
            rotation = rotation,
            grid = grid,
            axis = axis,
            detailGrid = detailGrid,
            snapMode = snapMode,
            snapToExistingPoints = snapToExistingPoints,
            snapToGrid = snapToGrid,
            namingMode = namingMode
        )
    }
}
