package viclass.editor.geo.models.request

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

data class DeleteRenderElementsRequest(
    val relIdxes: List<Int>
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ElementIndexesModel(
    val elIdxes: List<Int>?,
    val relIdxes: List<Int>?,
    val ctIdxes: List<Int>?,
)

enum class RelType { Vertex, Line }

@JsonIgnoreProperties(ignoreUnknown = true)
data class RenameElementModel(
    val relIdx: Int,
    val relType: RelType,
    val oldName: String,
    val newName: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class UpdateElementStateRequest(
    val deletedEls: ElementIndexesModel?,
    val undeletedEls: ElementIndexesModel?,
    val usableEls: ElementIndexesModel?,
    val unusableEls: ElementIndexesModel?,
    val renameEls: List<RenameElementModel>?,
)

data class MoveElementRequest constructor(
    val relIdx: Int,
    val pos: DoubleArray
)