package viclass.editor.geo.solving

import org.apache.commons.math3.complex.Complex
import kotlin.math.abs
import kotlin.math.sign

object Complex {
    /**
     * If the original array contains more than 1 element the a new array is
     * returned with duplicates removed leaving just unique vales. The
     * original array is returnred if it has less than 2 elements..
     *
     * In all cases the original array is unchanged.
     *
     * @param z the original array
     * @param epsilon max diffference allowed (optional)
     * @returns an array with duplicates removed
     */
    fun removeDuplicates(z: List<Complex>, epsilon: Double): List<Complex> {
        if (z.size <= 1) return z // can't have duplicates so exit
        val zs = z.drop(0).sortedWith { a, b ->
            a.compareTo(b)
        }
        val nz = mutableListOf(zs[0])
        for (i in 1 until zs.size) {
            if (!zs[i].equals(nz[nz.size - 1], epsilon)) nz.add(zs[i])
        }
        return nz
    }

    /**
     * Given an array of complex numbers return a new array containing those
     * that represent real numbers. The original array is unchanged.
     *
     * @param z the original array
     * @param epsilon max diffference allowed (optional)
     * @returns an array containing complex numbers representing real numbers
     */
    fun filterRealRoots(z: List<Complex>, epsilon: Double): List<Complex> {
        val nz = mutableListOf<Complex>()
        for (cn in z)
            if (cn.isReal(epsilon)) nz.add(Complex(cn.real, .0))
        return nz
    }
}

fun Complex.isZero(): Boolean {
    return Complex.ZERO.equals(this)
}

fun Complex.squared(): Complex {
    val re = this.real * this.real - this.imaginary * this.imaginary
    val im = 2 * this.real * this.imaginary
    return Complex(re, im)
}

fun Complex.cubed(): Complex {
    val re = this.real * this.real * this.real - 3 * this.real * this.imaginary * this.imaginary
    val im = 3 * this.real * this.real * this.imaginary - this.imaginary * this.imaginary * this.imaginary
    return Complex(re, im)
}

/**
 * This method compares this complex number with another. The main purpose
 * of this is to define an appropriate sort order.
 * Returns
 *  0 if the numbers are the same when using the equals method
 *  -1 if considered less than z
 *  +1 if considered larger than z
 * @param z the complex number to compare with
 * @returns -1, 0 or +1
 */
fun Complex.compareTo(z: Complex): Int {
    return if (this == z) 0
    else if (this.real == z.real) sign(this.imaginary - z.imaginary).toInt()
    else sign(this.real - z.real).toInt()
}

fun Complex.equals(z: Complex, epsilon: Double): Boolean {
    return abs(this.real - z.real) < epsilon && abs(this.imaginary - z.imaginary) < epsilon
}

fun Complex.isReal(epsilon: Double): Boolean {
    return abs(this.imaginary) <= epsilon
}