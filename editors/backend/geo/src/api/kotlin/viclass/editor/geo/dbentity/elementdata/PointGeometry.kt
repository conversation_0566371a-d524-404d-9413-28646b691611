package viclass.editor.geo.dbentity.elementdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "PointGeometry", key = "geoDataType")
data class PointGeometry @BsonCreator constructor(
    @BsonProperty("version")
    override var version: Int,

    @BsonProperty("x")
    var x: Double,

    @BsonProperty("y")
    var y: Double,

    @BsonProperty("z")
    var z: Double,

) : GeometryData