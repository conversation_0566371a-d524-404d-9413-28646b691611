package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class MoveOrderTransformData @BsonCreator constructor(
    @BsonProperty("center")
    val center: DoubleArray,
    @BsonProperty("source")
    val source: DoubleArray,
    @BsonProperty("targetParamIdx")
    val targetParamIdx: List<Int>,
): TransformData {
    override fun clone(): TransformData {
        return copy()
    }
}