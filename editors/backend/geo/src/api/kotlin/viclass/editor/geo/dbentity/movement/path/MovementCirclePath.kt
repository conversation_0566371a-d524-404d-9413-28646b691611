package viclass.editor.geo.dbentity.movement.path

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class MovementCirclePath @BsonCreator constructor(
    @BsonProperty("pc") val pc: DoubleArray,
    @BsonProperty("radius") val radius: Double,
): MovementPath {
    override fun clone(): MovementPath {
        return copy()
    }
}
