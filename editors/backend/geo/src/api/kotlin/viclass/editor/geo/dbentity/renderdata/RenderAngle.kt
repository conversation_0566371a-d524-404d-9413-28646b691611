package viclass.editor.geo.dbentity.renderdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.buildAngleRenderProp
import viclass.editor.geo.elements.Angle

@BsonDiscriminator(key = "renderElementType")
data class RenderAngle @BsonCreator constructor(
    @BsonProperty("renderProp") override var renderProp: RenderProp,
    @BsonProperty("elType") override val elType: String = Angle::class.simpleName!!
) : RenderElement {

    @BsonProperty("elIndexes")
    override var elIndexes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("relIndex")
    override var relIndex: Int = -1

    @BsonProperty("name")
    override var name: String = ""

    @BsonProperty("usable")
    override var usable: Boolean = true

    @BsonProperty("deleted")
    override var deleted: Boolean? = null

    @BsonProperty("valid")
    override var valid: Boolean = true

    @BsonProperty("anglePointIdx")
    var anglePointIdx: Int = -1

    @BsonProperty("degree")
    var degree: Double = .0

    @BsonProperty("vectorStart")
    var vectorStart: DoubleArray = doubleArrayOf(.0, .0, .0)

    @BsonProperty("vectorEnd")
    var vectorEnd: DoubleArray = doubleArrayOf(.0, .0, .0)

    @BsonProperty("vertexRelIdxes")
    var vertexRelIdxes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("lineRelIdxes")
    var lineRelIdxes: MutableSet<Int> = mutableSetOf()

    override fun clone(): RenderElement {
        return copy().also {
            cloneBase(it)
            it.anglePointIdx = anglePointIdx
            it.vectorStart = vectorStart.clone()
            it.vectorEnd = vectorEnd.clone()
            it.vertexRelIdxes = vertexRelIdxes.toMutableSet()
            it.lineRelIdxes = lineRelIdxes.toMutableSet()
            it.degree = degree
        }
    }

    override fun setDefaultProp(): RenderProp {
        renderProp = buildAngleRenderProp(DocDefaultElRenderProp())
        return renderProp
    }
}
