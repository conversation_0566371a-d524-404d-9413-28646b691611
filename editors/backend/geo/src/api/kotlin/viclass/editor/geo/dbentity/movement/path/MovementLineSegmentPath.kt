package viclass.editor.geo.dbentity.movement.path

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class MovementLineSegmentPath @BsonCreator constructor(
    @BsonProperty("p1") val p1: DoubleArray,
    @BsonProperty("p2") val p2: DoubleArray,
): MovementPath {
    override fun clone(): MovementPath {
        return copy()
    }
}
