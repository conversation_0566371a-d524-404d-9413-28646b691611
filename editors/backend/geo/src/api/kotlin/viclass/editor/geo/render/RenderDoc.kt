package viclass.editor.geo.render

import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.DocRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.renderdata.RenderElement
import viclass.editor.geo.elements.Element
import kotlin.reflect.KClass

/**
 * A RenderDoc contains the rendering geometry information which means
 * the vertices and the triangular faces used for rendering the final
 * image, together with the displaying information, such as names of vertices,
 * color of lines, faces, opacity, radius of each vertex etc..
 *
 */
interface RenderDoc {

    /**
     * The render state info
     */
    val state: RenderDocState

    val elements: List<RenderElement>

    /**
     * Render an element into the render document and return the render element
     */
    fun render(element: Element, renderProp: RenderProp? = null, rerender: Boolean? = null): List<RenderElement>

    fun updateRelsValid(idxes: Set<Int>, valid: Boolean)

    fun updateRelsUsable(idxes: Set<Int>, usable: Boolean)

    fun updateRelsDeleted(idxes: Set<Int>, deleted: Boolean?)

    fun updateDocSize(width: Double, height: Double)

    fun updateDocState(docRenderProp: DocRenderProp)

    fun updateDocDefaultElRenderProps(docDefaultElRenderProp: DocDefaultElRenderProp)

    fun addRenderEls(renderEls: List<RenderElement>)

    fun clearUnusableElements()

    fun deleteRenderElements(relIdx: Int, elDepIdxes: List<Int>): List<Int>

    fun <T : RenderElement> findRenderElByName(name: String?, relType: KClass<out T>): T?

    fun clone(): RenderDoc
}
