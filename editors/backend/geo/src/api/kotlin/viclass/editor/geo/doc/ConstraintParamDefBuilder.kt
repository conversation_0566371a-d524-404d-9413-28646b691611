package viclass.editor.geo.doc

import viclass.editor.geo.entity.ConstraintParamDef
import kotlin.reflect.KClass

class ConstraintParamDefBuilder private constructor() {
    lateinit var id : String
    var paramTypes : MutableMap<String, KClass<*>> = HashMap()
    lateinit var defaultTpl : String
    lateinit var description : String

    fun defId(id: String): ConstraintParamDefBuilder {
        this.id = id
        return this
    }

    fun pTypes(paramNames: List<String>, paramTypes: List<KClass<*>>): ConstraintParamDefBuilder {
        for (i in paramNames.indices) {
            this.paramTypes[paramNames[i]] = paramTypes[i]
        }
        return this
    }

    fun pType(name: String, type: KClass<*>): ConstraintParamDefBuilder {
        this.paramTypes[name] = type

        return this
    }

    fun defaultTpl(tpl: String): ConstraintParamDefBuilder {
        this.defaultTpl = tpl
        return this
    }

    fun description(desc: String): ConstraintParamDefBuilder {
        this.description = desc
        return this
    }

    fun build(): ConstraintParamDef {
        val def = ConstraintParamDef(id, paramTypes)
        def.description = description
        def.defaultTpl = defaultTpl

        return def
    }

    companion object {
        fun create(): ConstraintParamDefBuilder {
            return ConstraintParamDefBuilder()
        }
    }
}
