package viclass.editor.geo.elements

import org.reflections.Reflections
import kotlin.reflect.KClass

object ElementMappingType {
    val elementMapping: Map<String, KClass<out Element>>
    init {
        val reflections = Reflections(Element::class.java.`package`.name)
        val subTypes: List<KClass<out Element>> = reflections.getSubTypesOf(Element::class.java).map { it.kotlin }
        elementMapping = subTypes.associateBy { it.simpleName!! }
    }
}