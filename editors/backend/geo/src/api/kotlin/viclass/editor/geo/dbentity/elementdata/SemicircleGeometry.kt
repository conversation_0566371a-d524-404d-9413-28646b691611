package viclass.editor.geo.dbentity.elementdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "SemicircleGeometry", key = "geoDataType")
data class SemicircleGeometry @BsonCreator constructor(
    @BsonProperty("version")
    override var version: Int,

    @BsonProperty("centerPointIdx")
    var centerPointIdx: Int,

    @BsonProperty("startPointIdx")
    var startPointIdx: Int,

    @BsonProperty("endPointIdx")
    var endPointIdx: Int,

    ) : GeometryData