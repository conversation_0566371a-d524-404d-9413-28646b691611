package viclass.editor.geo.entity

class ConstraintGroup(var name : String) {


    /**
     * When deciding potential constraint group that can be used, this is
     * matched against the document dimension
     */
    var numDim: Int? = null

    /**
     * List of constraints that user needs to fill for this constraint group.
     *
     * A constraint is expressed as a mapping from a way of expressing parameter (Params)
     * to the list of equivalent template for the constraint.
     */
    val params: MutableList<ConstraintTemplate> = mutableListOf()

    /**
     * It's likely params, but it's having a list of dependencies is the group index of template
     * that user have to fill first, or it's empty, then this optional params will be shown to user
     */
    var optionalParams: MutableList<ConstraintTemplate> = mutableListOf()

    /**
     * The list of hint to select this constraint group
     * For example
     * - a hint for middle point constraint group is `middle of points`
     * - If the user select this then the parameters of this constructor
     * will be the two points
     *
     * In some case, the user can directly select constraint, for example
     * construct line from two point, the user can directly select constraint
     *
     * - from [startPoint] to [endPoint]
     *
     * or two constraints
     * - from [Point]
     * - to [Point]
     *
     * This list doesn't contain the actual string but the id of the string to serve the
     * multi-language translation
     */
    val hintStrLangIds : MutableList<String> = mutableListOf()

    /**
     * Mark this constraint group as invisible, user cannot see this constraint group to user.
     * Use this for creating construction by mouse or default
     */
    var invisible: Boolean = false
}
