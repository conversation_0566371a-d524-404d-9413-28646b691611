package viclass.editor.geo.utils

import java.util.Collections.swap


/**
 *
 * <AUTHOR>
 */

/**
 * Generate All Permutations of a list
 */
fun <V> List<V>.permute(): List<List<V>> {
    val retVal: MutableList<List<V>> = mutableListOf()

    fun generate(k: Int, list: List<V>) {
        // If only 1 element, just output the array
        if (k == 1) retVal.add(list.toList())
        else for (i in 0 until k) {
            generate(k - 1, list)
            if (k % 2 == 0) swap(list, i, k - 1)
            else swap(list, 0, k - 1)
        }
    }

    generate(this.count(), this.toList())

    return retVal
}
