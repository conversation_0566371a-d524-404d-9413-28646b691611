package viclass.editor.geo.dbentity.elementdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "AngleGeometry", key = "geoDataType")
data class AngleGeometry @BsonCreator constructor(
    @BsonProperty("version")
    override var version: Int,

    @BsonProperty("anglePointIdx")
    var anglePointIdx: Int,

    @BsonProperty("lineStartIdx")
    var lineStartIdx: Int,

    @BsonProperty("directionStart")
    var directionStart: Int,

    @BsonProperty("lineEndIdx")
    var lineEndIdx: Int,

    @BsonProperty("directionEnd")
    var directionEnd: Int,

    ) : GeometryData
