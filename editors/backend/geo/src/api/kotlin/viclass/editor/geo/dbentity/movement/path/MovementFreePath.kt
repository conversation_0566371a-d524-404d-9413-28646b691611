package viclass.editor.geo.dbentity.movement.path

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class MovementFreePath @BsonCreator constructor(
    @BsonProperty("freePath") val freePath: Boolean,
): MovementPath {
    constructor(): this(true) {

    }
    override fun clone(): MovementPath {
        return MovementFreePath(freePath)
    }
}
