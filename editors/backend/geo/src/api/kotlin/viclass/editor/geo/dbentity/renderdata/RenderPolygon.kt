package viclass.editor.geo.dbentity.renderdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.NamePattern
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.buildPolygonRenderProp
import viclass.editor.geo.elements.Polygon

@BsonDiscriminator(key = "renderElementType")
data class RenderPolygon
@BsonCreator
constructor(
    @BsonProperty("renderProp") override var renderProp: RenderProp,
    @BsonProperty("elType") override val elType: String = Polygon::class.simpleName!!
) : RenderElement {

    @BsonProperty("elIndexes") override var elIndexes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("relIndex") override var relIndex: Int = -1

    @BsonProperty("name") override var name: String = ""

    @BsonProperty("area") var area: Double = 0.0

    @BsonProperty("perimeter") var perimeter: Double = 0.0

    @BsonProperty("usable") override var usable: Boolean = true

    @BsonProperty("deleted") override var deleted: Boolean? = null

    @BsonProperty("valid") override var valid: Boolean = true

    @BsonProperty("faces") var faces: List<Int> = listOf()

    @BsonProperty("vertexRelIdxes") var vertexRelIdxes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("lineRelIdxes") var lineRelIdxes: MutableSet<Int> = mutableSetOf()

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Polygon::class, name)) return false
        val a = NamePattern.extractPointName(Polygon::class, this.name!!)
        val b = NamePattern.extractPointName(Polygon::class, name)
        if (a.size != b.size || a.isEmpty()) return false
        fun match(x: List<String>, y: List<String>) =
            (x + x).windowed(x.size).any { it == y } || (x.reversed() + x.reversed()).windowed(x.size).any { it == y }
        return match(a, b)
    }

    override fun clone(): RenderElement {
        return copy().also {
            cloneBase(it)
            it.area = area
            it.perimeter = perimeter
            it.faces = faces.toMutableList()
            it.vertexRelIdxes = vertexRelIdxes.toMutableSet()
            it.lineRelIdxes = lineRelIdxes.toMutableSet()
            it.vertexRelIdxes = vertexRelIdxes.toMutableSet()
        }
    }

    override fun setDefaultProp(): RenderProp {
        renderProp = buildPolygonRenderProp(DocDefaultElRenderProp())
        return renderProp
    }
}
