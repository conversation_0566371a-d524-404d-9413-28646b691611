package viclass.editor.geo.elements;

/**
 * Define all elements needed for working with a geometry document object
 *
 * A geometry document object element (short: element) contains geometrical
 * information to render it in the document display. The information for each
 * element is different. For example, a Point will need coordinate, a line
 * will need the parametric equations, the circle need the parametric equation
 * and the center, a triangle need the three points, etc..
 */
