package viclass.editor.geo.dbentity.elementdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "LineSegmentGeometry", key = "geoDataType")
data class LineSegmentGeometry @BsonCreator constructor(
    @BsonProperty("version")
    override var version: Int,

    @BsonProperty("spIdx")
    var spIdx: Int,

    @BsonProperty("epIdx")
    var epIdx: Int,

) : GeometryData