package viclass.editor.geo.dbentity.movement.path

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class MovementLinePath @BsonCreator constructor(
    @BsonProperty("root") val root: DoubleArray,
    @BsonProperty("parallelVector") val parallelVector: DoubleArray,
): MovementPath {
    override fun clone(): MovementPath {
        return copy()
    }
}
