package viclass.editor.geo.models.response

import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.DocRenderProp
import viclass.editor.geo.dbentity.renderdata.RenderElement


/**
 *
 * <AUTHOR>
 */
data class FetchDocResponse(
    val docId: String,
    val numDim: Int,
    val docRenderProp: DocRenderProp,
    val docDefaultElRenderProp: DocDefaultElRenderProp,
    var render: List<RenderElement>,
)
