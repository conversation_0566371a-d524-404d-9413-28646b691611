package viclass.editor.geo.models.response

/**
 * Include infos to identify a constraint template
 *
 * <AUTHOR>
 */
class ConstraintTemplateModel(val constructorId: String) {
    var cgId: String = ""
    var numConstraint: Int = 0
    var paramDefId: String? = null
    var indexInCG: Int = -1
    var tplString: String = ""
    var constraintString: String = ""
    var tplStringId: String = ""
    var tplDescription: List<String> = listOf()
    var noMatch: Int = -1
    var optional: Boolean = false

    fun clone(): ConstraintTemplateModel {
        val c = ConstraintTemplateModel(constructorId)
        c.cgId = cgId
        c.numConstraint = numConstraint
        c.paramDefId = paramDefId
        c.indexInCG = indexInCG
        c.tplString = tplString
        c.constraintString = constraintString
        c.tplStringId = tplStringId
        c.tplDescription = tplDescription.toList()
        c.noMatch = noMatch
        c.optional = optional
        return c
    }
}
