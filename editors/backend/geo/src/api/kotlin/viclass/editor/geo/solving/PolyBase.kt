package viclass.editor.geo.solving

import org.apache.commons.math3.complex.Complex
import kotlin.math.abs
import kotlin.math.cbrt
import kotlin.math.sqrt


/**
 * Base class for polynomials of degree 2, 3, or 4. The polynomial equation
 * is of the form:
 * f(x) = a4 * x^4 + a3 * x^3 + a2 * x^2 + a1 * x + a0
 * where the coefficients a4, a3, a2, a1, and a0 are real numbers.
 *
 * Each degree has a specific child class:
 * - Quadratic (degree 2): a4 = 0, a3 = 0, a2 != 0
 * - Cubic (degree 3): a4 = 0, a3 != 0
 * - Quartic (degree 4): a4 != 0
 *
 * Solving for f(x) = 0 returns an array of complex roots. To filter real roots,
 * use `Complex.filterRealRoots(allRoots)`.
 *
 * Real roots are represented as complex numbers with zero imaginary part.
 *
 * @author: <PERSON> (2023)
 * <AUTHOR>
 */
open class PolyBase {
    protected var cf: List<Double> = emptyList()
    protected lateinit var allRoots: List<Complex>

    companion object {
        fun getPoly(coefs: List<Double>): PolyBase {
            val coefsNonZero = coefs.dropWhile { it == 0.0 }
            return when (coefsNonZero.size) {
                5 -> Quartic(coefsNonZero)
                4 -> Cubic(coefsNonZero)
                3 -> Quadratic(coefsNonZero)
                2 -> Linear(coefsNonZero)
                else -> throw IllegalArgumentException("Invalid coefficients for a valid polynomial")
            }
        }
    }

    open fun roots(): List<Complex> = allRoots

    fun eval(x: Complex): Complex {
        var R = Complex(cf[0], 0.0)
        var V = Complex(x.real, x.imaginary)
        for (i in 1 until cf.size) {
            R = R.add(Complex(cf[i], 0.0).multiply(V))
            V = V.multiply(x)
        }
        return R

    }

    override fun toString(): String {
        val degree = cf.size - 1
        val sb = StringBuilder("x^$degree")
        for (i in degree - 1 downTo 0) {
            val n = cf[i]
            if (n != 0.0) {
                sb.append(" ${if (n < 0) "-" else "+"} ${abs(n)}")
                if (i > 0) sb.append(".x")
                if (i > 1) sb.append("^$i")
            }
        }
        return sb.toString()
    }
}

class Linear(coefs: List<Double>) : PolyBase() {
    init {
        require(coefs.size == 2 && coefs[0] != 0.0) { "Invalid coefficients for a linear equation" }
        val a1 = coefs[0]
        // Calculate coefficients so that a1 = 1
        cf = coefs.map { it / a1 }.reversed()
        allRoots = solve()
    }

    private fun solve(): List<Complex> {
        return listOf(Complex(-this.cf[0], .0))
    }
}

/**
 * Quadratic equation solver.
 *
 * Solves the equation
 * a2.x^2 + a1.x + a0
 * Where all coefficients are real and a2 != 0
 *
 * Pass an array of the coefficients to the constructor e.g.
 * [a2, a1, a0] an error will be thrown if there are not
 * exactly 3 elements or a2 == 0
 *
 * <AUTHOR> Lager (2023)
 */
class Quadratic(coefs: List<Double>) : PolyBase() {
    init {
        require(coefs.size == 3 && coefs[0] != 0.0) { "Invalid coefficients for a quadratic equation" }
        val a2 = coefs[0]
        cf = coefs.map { it / a2 }.reversed()
        allRoots = solve()
    }

    private fun solve(): List<Complex> {
        val a1 = this.cf[1]
        val a0 = this.cf[0]
        val t0 = a1 * a1 - 4 * a0
        val t1 = sqrt(abs(t0))
        val D = if (t0 >= 0) Complex(t1, 0.0) else Complex(0.0, t1)
        val roots = mutableListOf<Complex>()
        roots.add(D.subtract(a1).divide(2.0))
        roots.add(D.negate().subtract(a1).divide(2.0))
        return roots
    }
}
/**
 * Cubic equation solver.
 *
 * Solves the equation
 * a3.x^3 + a2.x^2 + a1.x + a0
 * Where all coefficients are real and a3 != 0
 *
 * Pass an array of the coefficients to the constructor e.g.
 * [a3, a2, a1, a0] an error will be thrown if there are not
 * exactly 4 elements or a3 == 0
 *
 * <AUTHOR> Lager (2023)
 */
class Cubic(coefs: List<Double>) : PolyBase() {
    private lateinit var d: List<Double>
    private lateinit var SHIFT: Complex

    /**
     *  Array of coefficients in term-power decending order i.e.
     *  [a3, a2, a1, a0]
     */
    init {
        require(coefs.size == 4 && coefs[0] != 0.0) { "Invalid coefficients for a cubic equation" }
        val a3 = coefs[0]
        cf = coefs.map { it / a3 }.reversed()
        allRoots = solve()
    }

    private fun solve(): List<Complex> {
        calculateDepressedCubic()
        val roots = mutableListOf<Complex>()
        val shift = SHIFT
        if (d[1] == .0) { // solve x^3 + d0 = 0
            roots.add(Complex(cbrt(d[0]), 0.0).subtract(shift))
        } else {
            val R = Complex(-this.d[0] / 2, .0)
            val Q = Complex(this.d[1] / 3, .0)
            val W3 = R.subtract(R.squared().add(Q.cubed()).sqrt())
            val Wroots = W3.nthRoot(3)
            for (root in Wroots) roots.add(root.subtract(Q.multiply(root.pow(-1.0))).subtract(shift))
        }
        // Complex root calculation
        return roots
    }

    // y^3 + d2.y^2 + d1.y + d0 where d2 == 0
    private fun calculateDepressedCubic() {
        SHIFT = Complex(cf[2] / 3, 0.0)
        d = listOf(-(9 * cf[1] * cf[2] - 27 * cf[0] - 2 * cf[2] * cf[2] * cf[2]) / 27, (3 * cf[1] - cf[2] * cf[2]) / 3, .0, 1.0)
    }
}

/**
 * Quartic equation solver.
 *
 * Solves the equation
 * a4.x^4 + a3.x^3 + a2.x^2 + a1.x + a0
 * Where all coefficients are real and a4 != 0
 *
 * Pass an array of the coefficients to the constructor e.g.
 * [a4, a3, a2, a1, a0] an error will be thrown if there are not
 * exactly 5 elements or a4 == 0
 *
 * <AUTHOR> Lager (2023)
 */
class Quartic(coefs: List<Double>) : PolyBase() {
    private var d: MutableList<Double> = mutableListOf(.0, .0, .0, .0, .0)
    private lateinit var SHIFT: Complex

    /**
     * Array of coefficients in term-power decending order i.e.
     * [a4, a3, a2, a1, a0]
     */
    init {
        require(coefs.size == 5 && coefs[0] != 0.0) { "Invalid coefficients for a quartic equation" }
        val a4 = coefs[0]
        cf = coefs.map { it / a4 }.reversed()
        allRoots = solve()
    }

    private fun solve(): List<Complex> {
        calculateDepressedQuartic()
        return if (d[1] == 0.0) solveBiquadratic() else solveGeneral()
    }

    /**
     * y^4 + d3.y^3 + d2.y^2 + d1.y + d0 where d3 == 0
     */
    private fun calculateDepressedQuartic() {
        SHIFT = Complex(cf[3] / 4, 0.0)
        val b = cf[3]
        val c = cf[2]
        val d = cf[1]
        val e = cf[0]
        this.d[4] = 1.0
        this.d[3] = 0.0
        this.d[2] = (-3 * b * b / 8) + c
        this.d[1] = (b * b * b / 8) - (b * c / 2) + d
        this.d[0] = -(3 * b * b * b * b / 256) + (b * b * c / 16) - (b * d / 4) + e
    }

    /**
     *  solve x^4 + d2.x^2 + d0 = 0
     */
    private fun solveBiquadratic(): List<Complex> {
        val d2 = this.d[2]
        val d0 = this.d[0]
        val roots = mutableListOf<Complex>()
        if (d2 == .0) { // x^4 + d0 = 0
            // console.log('Biquadratic solution to x^4 + d0 = 0')
            val R = Complex(-d0, .0).sqrt().sqrt()
            roots.add(R)
            roots.add(R.negate())
        }
        else { // x^4 + b.x^2 + c = 0
            // console.log('Biquadratic solution to x^4 + b.x^2 + c = 0')
            val quad = Quadratic(listOf(this.d[0], this.d[2], this.d[4]).reversed())
            for (root in quad.roots()) {
                val r = root.sqrt()
                roots.add(r.subtract(this.SHIFT))
                roots.add(r.negate().subtract(this.SHIFT))
            }
        }
        return roots
    }

    /**
     * solve x^4 + d2.x^2 + d1.x + d0 = 0
     */
    private fun solveGeneral(): List<Complex> {
        // console.log('General solution')
        // Depressed quartic coefficients
        val d3 = this.d[3]
        val d2 = this.d[2]
        val d1 = this.d[1]
        val d0 = this.d[0]
        // Resolvant cubic of depressed quartic coefficients
        val r3 = 1.0
        val r2 = -d2
        val r1 = d1 * d3 - 4 * d0
        val r0 = 4 * d2 * d0 - d1 * d1 - d3 * d3 * d0
        // Solve resolvant cubiic and find a real root
        val cubic = Cubic(listOf(r3, r2, r1, r0))
        val c_roots = cubic.roots()
        // There is always at least one real root to a cubic equation so
        // find the root with the smallest imaginary part i.e. min|imag|
        var y1 = 1E100
        var _im_ = 1E100
        for (root in c_roots)
        if (abs(root.imaginary) < _im_) {
            y1 = root.real
            _im_ = abs(root.imaginary)
        }
        // Get solutions to the depressed quatic equation then shift
        // these to get the solutions to the original quartic equation
        val t0 = y1 - d2
        val t1 = Math.sqrt(Math.abs(t0))
        val R = if (t0 >= 0) Complex(t1, .0) else Complex(.0, t1)
        val D: Complex
        val E: Complex
        if (R.isZero()) {
            val t2 = y1 * y1 - 4 * d0
            val t3 = sqrt(Math.abs(t2))
            val T = if (t2 >= 0)Complex(t3, .0) else Complex(.0, t3)
            D = T.sqrt().multiply(2.0).subtract(2 * d2)
            E = T.sqrt().multiply(2.0).negate().subtract(2 * d2)
        }
        else {
            D = R.squared().add(2 * d2).negate().add(R.pow(-1.0).multiply(-2 * d1)).sqrt()
            E = R.squared().add(2 * d2).negate().subtract(R.pow(-1.0).multiply(-2 * d1)).sqrt()
        }
        val roots = mutableListOf<Complex>()
        roots.add(R.add(D).divide(2.0).subtract(this.SHIFT))
        roots.add(R.subtract(D).divide(2.0).subtract(this.SHIFT))
        roots.add(R.negate().add(E).divide(2.0).subtract(this.SHIFT))
        roots.add(R.negate().subtract(E).divide(2.0).subtract(this.SHIFT))
        return roots
    }
}