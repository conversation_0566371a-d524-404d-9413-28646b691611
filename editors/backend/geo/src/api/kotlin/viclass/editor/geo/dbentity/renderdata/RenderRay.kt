package viclass.editor.geo.dbentity.renderdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.buildLineRenderProp
import viclass.editor.geo.elements.Ray

@BsonDiscriminator(key = "renderElementType")
data class RenderRay @BsonCreator constructor(
    @BsonProperty("renderProp") override var renderProp: RenderProp,
    @BsonProperty("elType") override val elType: String = Ray::class.simpleName!!
) : RenderElement {

    @BsonProperty("elIndexes")
    override var elIndexes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("relIndex")
    override var relIndex: Int = -1

    @BsonProperty("name")
    override var name: String = ""

    @BsonProperty("length")
    var length: Double = 0.0

    @BsonProperty("usable")
    override var usable: Boolean = true

    @BsonProperty("deleted")
    override var deleted: Boolean? = null

    @BsonProperty("valid")
    override var valid: Boolean = true

    @BsonProperty("startPointIdx")
    var startPointIdx: Int = -1

    @BsonProperty("endPointIdx")
    var endPointIdx: Int? = null

    @BsonProperty("vector")
    var vector: DoubleArray = doubleArrayOf(0.0, 0.0, 0.0)

    @BsonProperty("vertexRelIdxes")
    var vertexRelIdxes: MutableSet<Int> = mutableSetOf()

    override fun clone(): RenderElement {
        return copy().also {
            cloneBase(it)
            it.length = length
            it.startPointIdx = startPointIdx
            it.endPointIdx = endPointIdx
            it.vector = vector.clone()
            it.vertexRelIdxes = vertexRelIdxes.toMutableSet()
        }
    }

    override fun setDefaultProp(): RenderProp {
        renderProp = buildLineRenderProp(DocDefaultElRenderProp())
        return renderProp
    }
}
