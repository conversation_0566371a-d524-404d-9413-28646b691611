package viclass.editor.geo.entity

/**
 * The kind of parameter used. For example, a `name` kind means the parameter is
 * the name of an object within the document. A `value` kind means the parameter
 * is a value specified by the user. An `expression` kind means, the parameter
 * is an expression specified by the user.
 *
 * The param kind is used so that our template use a fixed naming so the retrieval
 * of parameters from construction params is less chaotic. Instead of
 *
 * constructionParam.getParam("point") in one place and
 * constructionParam.getParam("line") in another place
 *
 * which cause refactoring difficult, we can define all possible name
 * to be used in our code base here.
 *
 */
object ParamKind {
    const val PK_Name = "name"
    const val PK_Value = "value"
    const val PK_Expr = "expression"
}
