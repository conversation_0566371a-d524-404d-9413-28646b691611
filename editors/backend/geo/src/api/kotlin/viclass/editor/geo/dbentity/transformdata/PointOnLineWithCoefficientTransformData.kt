package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.entity.ParamKind

@BsonDiscriminator(key = "type")
data class PointOnLineWithCoefficientTransformData @BsonCreator constructor(
    @BsonProperty("targetParamIdx") val targetParamIdx: Int,
    @BsonProperty("paramKind") val paramKind: String,
    @BsonProperty("rootPoint") val rootPoint: DoubleArray,
    @BsonProperty("unitVector") val unitVector: DoubleArray,
): TransformData {
    override fun clone(): TransformData {
        return copy()
    }
}