package viclass.editor.geo.constructor

import viclass.editor.geo.elements.Element

/**
 * Extractable is the interface that represents extractable objects
 * that a parameter name within a param template can be mapped to. For example
 *
 * AB is a name that can be mapped to a Line between point A and B. The line is then extractable.
 *
 * Length AB = 1/2 * BC
 *
 * Here the name AB and BC are two names. If AB is used in a lengthAssignment param template, it
 * denotes the distance of an unknown point A to B is equal to to some expression. These two names
 * are mapped to NameForLength and Expression object. The corresponding extractor will have to implement
 * this.
 */
interface Extractable {
    fun constructions(): List<ConstructionResult<out Element>> = listOf()
}
