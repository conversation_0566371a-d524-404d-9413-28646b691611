package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class PointOnArcWithAngleTransformData @BsonCreator constructor(
    @BsonProperty("targetParamIdx") val targetParamIdx: Int,
    @BsonProperty("paramKind") val paramKind: String,
    @BsonProperty("center") val center: DoubleArray,
    @BsonProperty("pS") val pS: DoubleArray? = null,
    @BsonProperty("degree") val degree: Boolean? = null,
): TransformData {
    override fun clone(): TransformData {
        return copy()
    }
}