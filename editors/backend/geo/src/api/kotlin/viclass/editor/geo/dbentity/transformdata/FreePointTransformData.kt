package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(key = "type")
data class FreePointTransformData @BsonCreator constructor(
    @BsonProperty("targetParamIdx")
    val targetParamIdx: Int,
): TransformData {
    override fun clone(): TransformData {
        return copy()
    }
}