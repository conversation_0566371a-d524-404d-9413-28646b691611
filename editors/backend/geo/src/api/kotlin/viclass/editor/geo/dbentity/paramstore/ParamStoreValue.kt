package viclass.editor.geo.dbentity.paramstore

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "ParamStoreValue", key = "paramStoreType")
data class ParamStoreValue @BsonCreator constructor(
    @BsonProperty("value")
    var value: String
) : ParamStore {
    override fun clone(): ParamStoreValue {
        return ParamStoreValue(value)
    }
}
