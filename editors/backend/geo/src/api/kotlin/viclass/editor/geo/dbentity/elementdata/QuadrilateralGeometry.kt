package viclass.editor.geo.dbentity.elementdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "QuadrilateralGeometry", key = "geoDataType")
data class QuadrilateralGeometry @BsonCreator constructor(
    @BsonProperty("version")
    override var version: Int,

    @BsonProperty("p1Idx")
    var p1Idx: Int,

    @BsonProperty("p2Idx")
    var p2Idx: Int,

    @BsonProperty("p3Idx")
    var p3Idx: Int,

    @BsonProperty("p4Idx")
    var p4Idx: Int,

) : GeometryData