package viclass.editor.geo.entity

import kotlin.reflect.KClass

/**
 * A param template is a way of expressing parameters for a constraint.
 *
 * It tells the system what parameter values to be expected from the constraints
 *
 * For example
 * - aPoint: it tells the system that, from the constraint template (specified in the constraint group),
 * a point is expected for each {name} parameter
 */
class ConstraintParamDef {
    /**
     * An id that uniquely identify this definition
     * so that we can retrieve the definition for the constraint
     * template
     */
    var id: String
    var description: String = ""

    /**
     * Map from parameter names to the type of the parameters
     */
    var paramTypes: MutableMap<String, KClass<*>>
    var defaultTpl: String = ""

    constructor(id: String, paramTypes: Map<String, KClass<*>>?) {
        this.id = id
        this.paramTypes = HashMap(paramTypes)
    }

    fun getKClassType(type: String): KClass<*>? {
        return paramTypes[type]
    }
}
