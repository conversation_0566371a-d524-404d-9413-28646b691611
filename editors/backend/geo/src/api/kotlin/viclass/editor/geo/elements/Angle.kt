package viclass.editor.geo.elements

interface Angle : Element {
    val anglePoint: Point

    val lineStart: LineVi
    /**
     * = 1 nếu cùng hướng với vector của lineStart, = -1 nếu ngượ<PERSON> hướng với vector của lineStart
     */
    val directionStart: Int

    val lineEnd: LineVi
    /**
     * = 1 nếu cùng hướng với vector của lineEnd, = -1 nếu ngược hướng với vector của lineEnd
     */
    val directionEnd: Int

    fun angle(): Float?
}
