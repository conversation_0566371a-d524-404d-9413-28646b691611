package viclass.editor.geo.models.request

import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.PlaneVi
import viclass.editor.geo.elements.VectorVi
import viclass.editor.geo.render.RenderDocState

data class ElConstructionRequest constructor(
    val ctId: String,
    val name: String?,
    val cgName: String,
    val elType: String = "",
    var paramSpecs: List<ParamSpecs>? = null,
)

data class ConstructionRequest constructor(
    val construction: ElConstructionRequest,
    val renderProp: RenderProp?,
)

data class ApplyConstructionRequest constructor(
    val constructions: List<ConstructionRequest>,
    val preview: Boolean? = false
)

data class ReconstructionRequest constructor(
    val ctIdx: Int,
    val construction: ElConstructionRequest,
)