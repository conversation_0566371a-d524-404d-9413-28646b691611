package viclass.editor.geo.extractable

import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.Extractable
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point

/**
 * Represent a notion of the length between an unknown point and a reference point
 */
class NameForLength
/**
 * @param reference The reference point
 * @param unknown The unknown character
 */
constructor(val name: String, val reference: Point?, val unknown: String?) : Extractable {

    override fun constructions(): List<ConstructionResult<out Element>> {
        return emptyList()
    }
}
