package viclass.editor.geo.solving

import kotlin.reflect.KProperty1

class Coefficients(
    val a: Double,
    val b: Double,
    val c: Double,
    val d: Double,
    val e: Double,
    val f: Double
) {
    fun getCoef(coef: KProperty1<Coefficients, Double>): Double {
        return coef.get(this)
    }

    fun mult(n: Double): Coefficients {
        return Coefficients(a * n, b * n, c * n, d * n, e * n, f * n)
    }

    fun divide(n: Double): Coefficients {
        return Coefficients(a / n, b / n, c / n, d / n, e / n, f / n)
    }

    fun subtract(coef: Coefficients): Coefficients {
        return Coefficients(a - coef.a, b - coef.b, c - coef.c, d - coef.d, e - coef.e, f - coef.f)
    }

    fun add(coef: Coefficients): Coefficients {
        return Coefficients(a + coef.a, b + coef.b, c + coef.c, d + coef.d, e + coef.e, f + coef.f)
    }

    companion object {
        fun elliminateTerm(s: Coefficients, t: Coefficients, coef: KProperty1<Coefficients, Double>): Coefficients {
            val _s = s.getCoef(coef)
            val _t = t.getCoef(coef)
            val s1 = s.mult(_t)
            val t1 = t.mult(_s)
            return s1.subtract(t1)
        }
    }
}