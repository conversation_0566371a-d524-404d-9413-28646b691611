package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.entity.ParamKind

@BsonDiscriminator(key = "type")
data class PointOnLineSegmentWithRatioTransformData @BsonCreator constructor(
    @BsonProperty("targetParamIdx") val targetParamIdx: Int,
    @BsonProperty("paramKind") val paramKind: String,
    @BsonProperty("sPoint") val sPoint: DoubleArray,
    @BsonProperty("ePoint") val ePoint: DoubleArray,
): TransformData {
    override fun clone(): TransformData {
        return copy()
    }
}