package viclass.editor.geo.impl.elements

import org.apache.commons.math3.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Parallelogram
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import kotlin.math.abs
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
open class ParallelogramImpl constructor(
    doc: GeoDoc, name: String?,
    point1: Point, point2: Point, point3: Point, point4: Point
) : Parallelogram, QuadrilateralImpl(doc, name, point1, point2, point3, point4) {
    override val clazz: KClass<out Parallelogram> = Parallelogram::class


    /**
     * Validates that the polygon defined by 'point1', 'point2', 'point3', and 'point4' is a valid Parallelogram.
     *
     * Checks for equal lengths of opposite sides.
     *
     * @throws InvalidElementException if the polygon is not a valid Parallelogram.
     */
    override fun validate() {
        // Calculate side vectors.
        val vector12 = Vector3D(point2.coordinates().x - point1.coordinates().x,
            point2.coordinates().y - point1.coordinates().y,
            point2.coordinates().z - point1.coordinates().z)

        val vector23 = Vector3D(point3.coordinates().x - point2.coordinates().x,
            point3.coordinates().y - point2.coordinates().y,
            point3.coordinates().z - point2.coordinates().z)

        val vector34 = Vector3D(point4.coordinates().x - point3.coordinates().x,
            point4.coordinates().y - point3.coordinates().y,
            point4.coordinates().z - point3.coordinates().z)

        val vector41 = Vector3D(point1.coordinates().x - point4.coordinates().x,
            point1.coordinates().y - point4.coordinates().y,
            point1.coordinates().z - point4.coordinates().z)

        // Check for equal lengths.
        if (abs(vector12.norm - vector34.norm) > DEFAULT_TOLERANCE ||
            abs(vector23.norm - vector41.norm) > DEFAULT_TOLERANCE
        ) {
            throw InvalidElementException("$name is not a parallelogram: opposite sides are not equal in length.")
        }
    }

    override fun mergeFrom(other: Element) {
        if (other !is Parallelogram) return
        super<QuadrilateralImpl>.mergeFrom(other)
    }
}
