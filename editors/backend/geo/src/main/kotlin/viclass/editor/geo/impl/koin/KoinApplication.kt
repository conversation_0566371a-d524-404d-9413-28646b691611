package viclass.editor.geo.impl.koin

import org.koin.dsl.module
import org.koin.ksp.generated.module
import org.koin.logger.slf4jLogger
import viclass.editor.geo.impl.cache.MappingTextCache
import org.koin.dsl.koinApplication as _koinApplication

val koinApplication = _koinApplication {
    slf4jLogger()
    modules(
        DatabaseModule().module,
        ExtractorModule().module,
        ConstructorModule().module,
        GeoDocModule().module,
        ControllerModule().module,
        module {
            single<MappingTextCache> { MappingTextCache(get()) }
        }
    )
}