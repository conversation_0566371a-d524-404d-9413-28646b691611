package viclass.editor.geo.impl.constructor.quadrilateral

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.dbentity.movement.path.MovementFreePath
import viclass.editor.geo.dbentity.transformdata.PointOnPolygonTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aBoolean
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPointWithCoords
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Square
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.constructor.Points.isNewPoint
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.SquareImpl
import viclass.editor.geo.impl.transformer.PointOnSquareTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass

@Singleton
class SquareEC : ElementConstructor<Square> {
    override fun outputType(): KClass<Square> = Square::class

    private enum class CGS { FromPoints }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this).cgs(
            ConstraintGroupBuilder.create().name(CGS.FromPoints.name)
                // points
                .constraint(
                    0, ConstraintParamDefManager.instance()[aPointWithCoords]!!, emptyList(), ""
                )
                // isClockwise
                .constraint(
                    1, ConstraintParamDefManager.instance()[aBoolean]!!, emptyList(), ""
                ).build()
        ).elTypes(Square::class).build()
    }

    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<Square> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 2)
                constructFromPoints(doc, inputName, c)
            }
        }
    }

    private fun constructFromPoints(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<Square> {
        val (points, isClockwise) = extractSquareFromPointsParams(doc, c)

        // Get or generate input name and validate
        var generatedName = inputName ?: points.joinToString("") { it.name!! }
        val inputNameSet = NamePattern.extractPointName(Square::class, generatedName).toSet()

        require(inputNameSet.size == 4) { "Tên đầu vào phải chứa chính xác 4 tên điểm" }
        generatedName = inputNameSet.joinToString("")

        if (!isValidSquare(points)) points.adjustSquarePoints(doc, isClockwise)

        if (!isValidSquare(points))
            throw ConstructionException("4 điểm không thỏa mãn hình vuông")

        points.forEachIndexed { idx, pt ->
            if (isNewPoint(doc, pt)) {
                pt.transformData = PointOnPolygonTransformData(idx)
                pt.transformer = TransformMapping.fromClazz(PointOnSquareTransformer::class)
                pt.movementPath = MovementFreePath()
                pt.usable = true
            }
        }

        val square = SquareImpl(doc, generatedName, points[0], points[1], points[2], points[3])
        return ConstructionResultImpl<Square>().apply {
            addDependencies(points, true)
            setResult(square)
        }
    }
}
