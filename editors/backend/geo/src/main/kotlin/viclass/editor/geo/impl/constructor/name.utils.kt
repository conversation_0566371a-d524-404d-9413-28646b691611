package viclass.editor.geo.impl.constructor

import viclass.editor.geo.dbentity.renderdata.*
import viclass.editor.geo.doc.GeoDoc

/**
 *
 * <AUTHOR>
 */
fun generatePointName(doc: GeoDoc): String {
    return generatePointName(doc, arrayListOf())
}

fun generatePointName(doc: GeoDoc, vararg excluded: String): String {
    return generatePointName(doc, arrayListOf(*excluded))
}

fun generatePointName(doc: GeoDoc, excluded: ArrayList<String>): String {
    val template = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    var i = 0

    val used =
        doc.renderDoc.elements
            .filter {
                it.deleted != true &&
                        it.usable &&
                        (it is RenderVertex || it is RenderCircleShape || it is RenderEllipseShape)
            }
            .map { it.name }

    while (true) {
        val end = if (i == 0) "" else i

        template.forEach {
            val name = "$it$end"
            if (!used.contains(name) && !excluded.contains(name)) {
                excluded.add(name)
                return name
            }
        }

        i++
    }
}

fun generateCircleName(doc: GeoDoc): String {
    return generateCircleName(doc, arrayListOf())
}

fun generateCircleName(doc: GeoDoc, vararg excluded: String): String {
    return generateCircleName(doc, arrayListOf(*excluded))
}

fun generateCircleName(doc: GeoDoc, excluded: ArrayList<String>): String {
    val usedNames =
        doc.renderDoc
            .elements
            .filter {
                it.deleted != true &&
                        it.usable &&
                        (it is RenderVertex || it is RenderCircleShape || it is RenderEllipseShape)
            }
            .map { it.name }
            .toSet()

    var index = 0
    while (true) {
        val suffix = if (index == 0) "" else index.toString()
        val name = "O$suffix"
        if (name !in usedNames && name !in excluded) {
            excluded.add(name)
            return name
        }
        index++
    }
}

/**
 * Generates a new unique name starting with a lowercase letter a-z,
 * followed by a1, a2, ..., b1, b2, ..., a11, a12, ...
 *
 * @param doc       The current geometry document.
 * @param excluded  A list of externally reserved names, which will be updated.
 * @return          A unique name that does not conflict with existing ones.
 */
fun generateLowercaseName(
    doc: GeoDoc,
    excluded: MutableList<String>
): String {
    val usedNames = doc.renderDoc.elements
        .filter {
            it.deleted != true &&
                    it.usable &&
                    (it is RenderVertex || it is RenderCircleShape || it is RenderEllipseShape || it is RenderSectorShape || it is RenderLine)
        }
        .map { it.name }
        .toSet()

    for (ch in 'a'..'z') {
        val name = ch.toString()
        if (name !in usedNames && name !in excluded) {
            excluded.add(name)
            return name
        }
    }

    val letters = ('a'..'z').toList()
    var number = 1
    while (true) {
        if ('0' !in number.toString()) {
            for (ch in letters) {
                val name = "$ch$number"
                if (name !in usedNames && name !in excluded) {
                    excluded.add(name)
                    return name
                }
            }
        }
        if (number == Int.MAX_VALUE)
            throw IllegalStateException("Out of unique names!")
        number++
    }
}

fun generateLineName(doc: GeoDoc): String {
    return generateLineName(doc, arrayListOf())
}

fun generateLineName(doc: GeoDoc, vararg excluded: String): String {
    return generateLineName(doc, arrayListOf(*excluded))
}

fun generateLineName(doc: GeoDoc, excluded: ArrayList<String>): String {
    var i = 0

    val used =
        doc.renderDoc.elements.filter { it.deleted != true && it.usable }.filterIsInstance<RenderLine>().map { it.name }

    while (true) {
        val end = if (i == 0) "" else i

        val name = "d$end"
        if (!used.contains(name) && !excluded.contains(name)) {
            excluded.add(name)
            return name
        }

        i++
    }
}

fun extractAngleCenter(input: String): String {
    // angle name can contain 3 parts (ABC; xAy) or 1 part (A; B)
    val threePartsRegex = Regex("^([A-Za-z]\\d*'?)([A-Z]\\d*'?)([A-Za-z]\\d*'?)$")
    val onePartRegex = Regex("^([A-Z]\\d*'?)$")

    if (threePartsRegex.matches(input)) {
        val matchResult = threePartsRegex.find(input)
        val parts = matchResult?.groupValues
        if (parts != null && parts.size == 4) {
            return parts[2] // The center point is the second group
        }
    }
    if (onePartRegex.matches(input)) return input
    throw IllegalArgumentException("Invalid angle name: $input")
}

fun generateRayName(doc: GeoDoc, startObjName: String): String {
    return generateRayName(doc, startObjName, arrayListOf())
}

fun generateRayName(doc: GeoDoc, startObjName: String, vararg excluded: String): String {
    return generateRayName(doc, startObjName, *excluded)
}

fun generateRayName(doc: GeoDoc, startObjName: String, excluded: ArrayList<String>): String {
    val used =
        doc.renderDoc
            .elements
            .filter {
                it.deleted != true &&
                        it.usable &&
                        (it is RenderRay || it is RenderLine || it is RenderLineSegment || it is RenderVector)
            }
            .map { it.name }
            .toSet()

    val postFixTemplate = "xyzabcdefghijklmnopqrstuvw"
    var i = 0

    while (true) {
        val end = if (i == 0) "" else i

        for (char in postFixTemplate) {
            val name = "$startObjName$char$end"
            if (!used.contains(name) && !excluded.contains(name)) {
                excluded.add(name)
                return name
            }
        }

        i++
    }
}
