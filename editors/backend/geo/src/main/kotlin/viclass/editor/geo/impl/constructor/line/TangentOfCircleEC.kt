package viclass.editor.geo.impl.constructor.line

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import kotlin.reflect.KClass

@Singleton
class TangentOfCircleEC : ElementConstructor<LineVi> {
    override fun outputType(): KClass<LineVi> = LineVi::class

    private enum class ConstraintGroup { ThroughAPoint }

    override fun template(): ConstructorTemplate {
        val cm = ConstraintParamDefManager.instance()
        return ConstructorTemplateBuilder.create(this).cgs(
            ConstraintGroupBuilder.create().name(ConstraintGroup.ThroughAPoint.name).constraint(
                0, cm[ConstraintParamDefManager.aCircle]!!, listOf("NameOfCircle"), "tpl-TangentOfCircle"
            ).constraint(
                1, cm[ConstraintParamDefManager.aPoint]!!, listOf("NameOfPoint"), "tpl-ThroughPoint"
            ).constraintOptional(
                2, cm[ConstraintParamDefManager.aValue]!!, listOf(0, 1), listOf("Value"), "tpl-thLine"
            ).build(),
        ).elTypes(LineVi::class).build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<LineVi> {
        return when (ConstraintGroup.valueOf(c.cgName)) {
            ConstraintGroup.ThroughAPoint -> {
                Validations.validateNumConstraints(c, 2, "min")
                constructThroughPoint(doc, inputName, c)
            }
        }
    }

    private fun constructThroughPoint(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<LineVi> {
        val result = ConstructionResultImpl<LineVi>()
        var circle: Circle? = null
        var point: Point? = null
        var resIdx = 0

        for (p in c.params) {
            when (p.paramDef.id) {
                ConstraintParamDefManager.aCircle -> {
                    val extracted = extractFirstPossible<ElementExtraction<Circle>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    circle = extracted.result.result() ?: throw ConstructionException("Thiếu đường tròn")
                    result.mergeAsDependency(extracted.result)
                }

                ConstraintParamDefManager.aPoint -> {
                    val extracted = extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    point = extracted.result.result() ?: throw ConstructionException("Thiếu điểm")
                    result.mergeAsDependency(extracted.result)
                }

                ConstraintParamDefManager.aValue -> if (p.specs.indexInCG == 2) {
                    val extracted = extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    resIdx = (extracted.result - 1).coerceAtLeast(0)
                }
            }
        }

        val tangents = Circles.tangentThroughPoint(circle!!, point!!)
            ?: throw ConstructionException("Không thể tạo đường tiếp tuyến")

        val line = if (resIdx >= tangents.size) throw ConstructionException("Không thể tạo được tiếp tuyến")
        else tangents[resIdx]

        line.name = inputName ?: generateLineName(doc)

        result.setResult(line)
        return result
    }
}