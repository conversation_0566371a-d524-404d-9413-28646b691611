package viclass.editor.geo.impl.db

import com.mongodb.reactivestreams.client.MongoCollection
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.reactive.asFlow
import viclass.editor.geo.impl.pojo.TextMapping


/**
 *
 * <AUTHOR>
 */
class TextMappingDS constructor(
    private val collection: MongoCollection<TextMapping>
) {
    suspend fun fetch(): List<TextMapping> {
        return collection.find().asFlow().toList()
    }
}
