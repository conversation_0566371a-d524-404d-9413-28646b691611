package viclass.editor.geo.impl.doc

import viclass.editor.geo.entity.ConstraintGroup
import viclass.editor.geo.entity.ConstraintParamDef
import viclass.editor.geo.entity.ConstraintTemplate

class ConstraintGroupBuilder private constructor() {
    private var cg: ConstraintGroup = ConstraintGroup("")

    fun name(grp: String): ConstraintGroupBuilder {
        cg.name = grp
        return this
    }

    fun numDim(num: Int?): ConstraintGroupBuilder {
        cg.numDim = num
        return this
    }

    /**
     * This constraint template is not required and only be shown after all the dependencies are filled, or it's empty.
     * @param tplDes List of description of template params
     * @param dependencies List of group index of dependency constraint template
     */
    fun constraintOptional(
        idxInCG: Int, c: ConstraintParamDef, dependencies: List<Int>, tplDes: List<String>, vararg tplStrLangId: String
    ): ConstraintGroupBuilder {
        val ct = ConstraintTemplate()
        ct.paramDefId = c.id
        ct.tplStrLangIds = listOf(*tplStrLangId)
        ct.tplDescription = tplDes
        ct.indexInCG = idxInCG
        ct.dependencies = dependencies
        ct.optional = true

        cg.optionalParams.add(ct)

        return this
    }

    fun constraintDepends(
        idxInCG: Int, c: ConstraintParamDef, dependencies: List<Int>, tplDes: List<String>, vararg tplStrLangId: String
    ): ConstraintGroupBuilder {
        val ct = ConstraintTemplate()
        ct.paramDefId = c.id
        ct.tplStrLangIds = listOf(*tplStrLangId)
        ct.tplDescription = tplDes
        ct.indexInCG = idxInCG
        ct.dependencies = dependencies

        cg.params.add(ct)

        return this
    }

    /**
     * This constraint template is not required and only use by internal service or by submitting constraint by mouse.
     */
    fun constraintHidden(
        idxInCG: Int, c: ConstraintParamDef, tplDes: List<String>, vararg tplStrLangId: String
    ): ConstraintGroupBuilder {
        val ct = ConstraintTemplate()
        ct.paramDefId = c.id
        ct.tplStrLangIds = listOf(*tplStrLangId)
        ct.tplDescription = tplDes
        ct.indexInCG = idxInCG
        ct.optional = true
        ct.hidden = true

        cg.optionalParams.add(ct)

        return this
    }

    /**
     * @param tplDes List of description of template params
     */
    fun constraint(
        idxInCG: Int, c: ConstraintParamDef, tplDes: List<String>, vararg tplStrLangId: String
    ): ConstraintGroupBuilder {
        val ct = ConstraintTemplate()
        ct.paramDefId = c.id
        ct.tplStrLangIds = listOf(*tplStrLangId)
        ct.tplDescription = tplDes
        ct.indexInCG = idxInCG

        cg.params.add(ct)

        return this
    }

    /**
     * add constraint without template and constraint params
     */
    fun constraintDefault(): ConstraintGroupBuilder {
        val ct = ConstraintTemplate()
        ct.indexInCG = 0
        cg.invisible = true
        cg.params.add(ct)
        return this
    }

    fun invisible(): ConstraintGroupBuilder {
        cg.invisible = true
        return this
    }

    fun hints(vararg hint: String): ConstraintGroupBuilder {
        cg.hintStrLangIds.addAll(listOf(*hint))
        return this
    }

    fun build(): ConstraintGroup {
        return cg
    }

    companion object {
        fun create(): ConstraintGroupBuilder {
            return ConstraintGroupBuilder()
        }
    }
}
