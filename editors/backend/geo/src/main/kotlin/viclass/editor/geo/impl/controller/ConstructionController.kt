package viclass.editor.geo.impl.controller

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.logging.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import org.koin.core.annotation.Singleton
import viclass.editor.geo.dbentity.renderdata.RenderElement
import viclass.editor.geo.dbentity.renderdata.RenderLine
import viclass.editor.geo.dbentity.renderdata.RenderVertex
import viclass.editor.geo.doc.GeoDocCommander
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementExistInDocumentException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.exceptions.ViException
import viclass.editor.geo.impl.constructor.generateLineName
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.models.request.ApplyConstructionRequest
import viclass.editor.geo.models.request.MoveElementRequest
import viclass.editor.geo.models.request.ReconstructionRequest
import viclass.editor.geo.models.request.RenameElementModel
import viclass.editor.geo.models.response.ApplyConstructionResponse

/**
 * Controller for handling geometry construction operations in the backend. Provides endpoints for applying
 * constructions, reconstructing elements, and moving elements within a geometry document. Uses GeoDocCommander for
 * business logic and database operations.
 */
@Singleton(binds = [ConstructionController::class])
class ConstructionController(val commander: GeoDocCommander) : Logging {
    /**
     * Applies a list of constructions to a geometry document.
     *
     * Steps:
     * 1. Parses the ApplyConstructionRequest from the client.
     * 2. Retrieves the document by docId from the request parameters.
     * 3. For each construction:
     * ```
     *    - Applies the construction using commander.
     *    - Handles element renaming if necessary.
     *    - Renders the construction result.
     *    - Collects indices and construction IDs for response.
     * ```
     * 4. Handles preview mode by restoring the original document.
     * 5. Responds with the result or error as appropriate.
     *
     * Error Handling:
     * - Returns appropriate HTTP status and logs for invalid requests, missing documents, or construction errors.
     */
    suspend fun applyConstruction(call: ApplicationCall) {
        logger.debug("Request to string {}", call.request.toLogString())
        val req =
            try {
                call.receive<ApplyConstructionRequest>()
            } catch (ex: Exception) {
                logger.error("Failed to parse ApplyConstructionRequest", ex)
                call.respond(HttpStatusCode.BadRequest, "Invalid request body")
                return
            }

        val docId = call.parameters["docId"]
        if (docId == null) {
            call.respond(HttpStatusCode.BadRequest, "Must specify document id")
            return
        }

        val doc = commander.retrieveDoc(docId)
        if (doc == null) {
            call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
            return
        }
        val entity = commander.convertDocToEntity(doc)

        try {
            commander.clearUnusableElements(docId)
            val renamedEls = mutableSetOf<RenameElementModel>()
            val elementIdxes = mutableSetOf<Int>()
            val renders = mutableSetOf<RenderElement>()
            val constructionIdxes = mutableSetOf<Int>()

            for (cstReq in req.constructions) {
                val (construction, cr) =
                    try {
                        commander.applyConstruction(doc, cstReq.construction)
                    } catch (ex: ElementNotExistInDocumentException) {
                        logger.warn("doc {} construct element not exist {} ... ", docId, req)
                        call.respond(HttpStatusCode.BadRequest, ex.message.toString())
                        return
                    } catch (ex: ElementExistInDocumentException) {
                        logger.warn("doc {} construct element exist {} ... ", docId, req)
                        call.respond(HttpStatusCode.BadRequest, ex.message.toString())
                        return
                    } catch (ex: ConstructionException) {
                        logger.warn("doc {} construction failed {} ... ", docId, req, ex)
                        call.respond(HttpStatusCode.BadRequest, ex.message.toString())
                        return
                    } catch (ex: ViException) {
                        logger.error("doc {} construct failed {} ... ", docId, req, ex)
                        commander.replaceDoc(entity)
                        call.respond(
                            HttpStatusCode.InternalServerError,
                            ApplyConstructionResponse(emptySet(), emptySet(), emptySet())
                        )
                        return
                    } catch (ex: Throwable) {
                        logger.error("doc {} construct element failed {} ... ", docId, req, ex)
                        commander.replaceDoc(entity)
                        call.respond(HttpStatusCode.InternalServerError, "Có lỗi xảy ra")
                        return
                    }

                val newNames = cr.elements().mapNotNull { it?.name }
                for (rel in doc.renderDoc.elements) {
                    if (!rel.valid && rel.name in newNames) {
                        when (rel) {
                            is RenderVertex -> {
                                val newName = generatePointName(doc, *newNames.toTypedArray())
                                renamedEls += commander.renamePointElement(docId, rel.relIndex, newName)
                            }

                            is RenderLine -> {
                                val newName = generateLineName(doc, *newNames.toTypedArray())
                                renamedEls += commander.renameLineElement(docId, rel.relIndex, newName)
                            }
                        }
                    }
                }
                doc.mergeConstructionResult(cr, construction)
                renders += commander.renderConstructionResult(cr, cstReq.renderProp)
                elementIdxes +=
                    cr.elements()
                        .filterNotNull()
                        .filter { el -> doc.getConstructionIndex(el) == cr.ctIdx }
                        .mapNotNull { el -> doc.getIndex(el) }
                constructionIdxes.add(cr.ctIdx)
            }

            if (req.preview == true) {
                commander.replaceDoc(entity)
                call.respond(ApplyConstructionResponse(renders, elementIdxes, constructionIdxes, renamedEls))
                return
            }
            commander.replaceDocToDB(doc)
            call.respond(ApplyConstructionResponse(renders, elementIdxes, constructionIdxes, renamedEls))
        } catch (exist: ElementExistInDocumentException) {
            logger.warn("doc {} construct element exist {} ... ", docId, req)
            call.respond(HttpStatusCode.OK, ApplyConstructionResponse(emptySet(), emptySet(), emptySet()))
        } catch (ex: ViException) {
            logger.error("doc {} construct failed {} ... ", docId, req, ex)
            commander.replaceDoc(entity)
            call.respond(HttpStatusCode.OK, ApplyConstructionResponse(emptySet(), emptySet(), emptySet()))
        } catch (t: Throwable) {
            logger.error("doc {} construct element failed {} ... ", docId, req, t)
            commander.replaceDoc(entity)
            call.respond(HttpStatusCode.InternalServerError, "failed to construct elements")
        }
    }

    /**
     * Reconstructs a specific construction in a geometry document.
     *
     * Steps:
     * 1. Parses the ReconstructionRequest from the client.
     * 2. Retrieves the document by docId from the request parameters.
     * 3. Calls commander to clear unusable elements and perform reconstruction.
     * 4. Updates the document in the database and responds with the result.
     *
     * Error Handling:
     * - Returns appropriate HTTP status and logs for invalid requests, missing documents, or reconstruction errors.
     * Restores the original document on failure.
     */
    suspend fun reconstruct(call: ApplicationCall) {
        logger.debug("Request to string {}", call.request.toLogString()) // Log the incoming request for debugging
        val req =
            try {
                call.receive<ReconstructionRequest>() // Parse the request body as
                // ReconstructionRequest
            } catch (ex: Exception) {
                logger.error("Failed to parse ReconstructionRequest", ex) // Log parsing error
                call.respond(HttpStatusCode.BadRequest, "Invalid request body") // Respond with 400 if parsing fails
                return
            }
        val docId = call.parameters["docId"] // Extract docId from request parameters
        if (docId == null) {
            call.respond(HttpStatusCode.BadRequest, "Must specify document id") // Respond with 400 if docId is missing
            return
        }
        val doc = commander.retrieveDoc(docId) // Retrieve the document from storage
        if (doc == null) {
            call.respond(HttpStatusCode.NotFound, "Document doesn't exist") // Respond with 404 if document not found
            return
        }
        val entity = commander.convertDocToEntity(doc) // Backup the current document entity for rollback
        try {
            commander.clearUnusableElements(docId) // Remove any unusable elements before reconstruction
            val res = commander.reconstruct(doc, req.construction, req.ctIdx) // Perform the reconstruction
            commander.replaceDocToDB(doc) // Save the updated document to the database
            call.respond(HttpStatusCode.OK, res) // Respond with the reconstruction result
        } catch (exist: ElementExistInDocumentException) {
            logger.warn("doc {} reconstruct element exist {} ... ", docId, req)
            call.respond(HttpStatusCode.OK, ApplyConstructionResponse(emptySet(), emptySet(), emptySet()))
        } catch (ex: ViException) {
            logger.error("doc {} reconstruct element failed {} ... ", docId, req, ex)
            commander.replaceDoc(entity) // Rollback to original document
            call.respond(HttpStatusCode.BadRequest, "failed to reconstruct elements")
        } catch (t: Throwable) {
            logger.error("reconstruct unknown error occurred... ", t)
            commander.replaceDoc(entity) // Rollback to original document
            call.respond(HttpStatusCode.InternalServerError, "unknown error")
        }
    }

    /**
     * Moves a render element (e.g., point or line) to a new position in a geometry document.
     *
     * Steps:
     * 1. Parses the MoveElementRequest from the client.
     * 2. Retrieves the document by docId from the request parameters.
     * 3. Calls commander to move the element and responds with the result.
     *
     * Error Handling:
     * - Returns appropriate HTTP status and logs for invalid requests, missing documents, or move errors.
     */
    suspend fun moveElement(call: ApplicationCall) {
        logger.debug("Request to string {}", call.request.toLogString())
        val req =
            try {
                call.receive<MoveElementRequest>()
            } catch (ex: Exception) {
                logger.error("Failed to parse MoveElementRequest", ex)
                call.respond(HttpStatusCode.BadRequest, "Invalid request body")
                return
            }
        val docId = call.parameters["docId"]
        if (docId == null) {
            call.respond(HttpStatusCode.BadRequest, "Must specify document id")
            return
        }
        val doc = commander.retrieveDoc(docId)
        if (doc == null) {
            call.respond(HttpStatusCode.NotFound, "Document doesn't exist")
            return
        }
        try {
            val res = commander.moveElement(doc, req.relIdx, req.pos)
            call.respond(HttpStatusCode.OK, res)
        } catch (ex: ViException) {
            logger.error("doc {} move element failed {} ... ", docId, req, ex)
            call.respond(HttpStatusCode.BadRequest, "failed to move element")
        } catch (t: Throwable) {
            logger.error("unknown error occurred... ", t)
            call.respond(HttpStatusCode.InternalServerError, "unknown error")
        }
    }
}
