package viclass.editor.geo.impl.constructor.line

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithCoefficientTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Angle
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Ray
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.RayImpl
import viclass.editor.geo.impl.transformer.PointOnLineWithCoefficientTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class BisectorOfAngleEC constructor() : ElementConstructor<LineVi> {

    private enum class CGS {
        BisectorAngle, BisectorAngleSegment, BisectorAngleSegmentWithIntersectionLine
    }

    override fun template(): ConstructorTemplate {
        val cg1 = ConstraintGroupBuilder.create()
            .name(CGS.BisectorAngle.name)
            .hints("hint-BisectorOfAngle")
            .constraint(
                0, ConstraintParamDefManager.instance()[ConstraintParamDefManager.anAngle]!!,
                listOf("NameOfAngle"),
                "tpl-BisectorOfAngle"
            )
            .build()

        return ConstructorTemplateBuilder.create(this)
            .cgs(cg1)
            .elTypes(LineVi::class, LineSegment::class, Ray::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.BisectorAngle -> {
                Validations.validateNumConstraints(c, 1)
                constructBisectorAngle(doc, inputName, c)
            }

            CGS.BisectorAngleSegment -> {
                Validations.validateNumConstraints(c, 2)
                constructBisectorAngleSegment(doc, c)
            }

            CGS.BisectorAngleSegmentWithIntersectionLine -> {
                Validations.validateNumConstraints(c, 2)
                constructBisectorAngleSegmentWithIntersectionLine(doc, inputName, c)
            }

        }
    }

    private fun calculateBiSectorVectorOfAngle(a: List<Double>, b: List<Double>): List<Double> {
        // Calculate the dot product of a and b
        val dotProduct = a[0] * b[0] + a[1] * b[1]

        // Magnitude of vector a
        val magnitudeA = Math.sqrt(a[0] * a[0] + a[1] * a[1])

        // Magnitude of vector b
        val magnitudeB = Math.sqrt(b[0] * b[0] + b[1] * b[1])

        // Calculate the cosine of the angle between a and b
        val cosTheta = dotProduct / (magnitudeA * magnitudeB)

        // Calculate the sine of the angle between a and b
        val sinTheta = Math.sqrt(1 - cosTheta * cosTheta)

        // Calculate the x and y components of the angle bisector vector
        val cX = (magnitudeA * b[1] - magnitudeB * a[1]) / (2 * sinTheta)
        val cY = (magnitudeB * a[0] - magnitudeA * b[0]) / (2 * sinTheta)

        return listOf(cX, cY)
    }

    private fun findEndingPoint(
        sPointX: Double,
        sPointY: Double,
        uVectorX: Double,
        uVectorY: Double,
        k: Double
    ): List<Double> {
        val x = sPointX + uVectorX * k;
        val y = sPointY + uVectorY * k;
        return listOf(x, y);
    }

    private fun constructBisectorAngle(doc: GeoDoc, name: String?, c: Construction): ConstructionResult<LineVi> {
        val exr: ElementExtraction<Angle> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val angle = exr.result.result()!!

        val lsVector = angle.lineStart.parallelVector
        val leVector = angle.lineEnd.parallelVector
        val ds = angle.directionStart
        val de = angle.directionEnd

        val bisectorVectorArr = calculateBiSectorVectorOfAngle(
            listOf(ds * lsVector.x, ds * lsVector.y),
            listOf(de * leVector.x, de * leVector.y)
        )
        val bisectorVector = Vector3D.of(bisectorVectorArr[0], bisectorVectorArr[1], 0.0)

        val rayName = name ?: generateRayName(doc, extractAngleCenter(angle.name!!))
        val bisectorLine = RayImpl(doc, rayName, angle.anglePoint, bisectorVector)

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(bisectorLine)
        cr.mergeAsDependency(exr.result)

        return cr
    }

    private fun constructBisectorAngleSegment(doc: GeoDoc, c: Construction): ConstructionResult<LineVi> {
        val exr1 = extractFirstPossible<ElementExtraction<Angle>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2 = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, c.params[1], c.ctIdx)

        val angle = exr1.result.result() ?: throw ElementNotExistInDocumentException("The angle does not exist")
        val k = exr2.result

        val lsVector = angle.lineStart.parallelVector
        val leVector = angle.lineEnd.parallelVector
        val ds = angle.directionStart
        val de = angle.directionEnd

        val bisectorVectorArr = calculateBiSectorVectorOfAngle(
            listOf(ds * lsVector.x, ds * lsVector.y),
            listOf(de * leVector.x, de * leVector.y)
        )
        val bisectorVector = Vector3D.of(bisectorVectorArr[0], bisectorVectorArr[1], 0.0)

        val pointName = generatePointName(doc)
        val vu = bisectorVector.normalize()
        val coords = this.findEndingPoint(
            angle.anglePoint.coordinates().x,
            angle.anglePoint.coordinates().y,
            vu.x,
            vu.y,
            k
        )

        val newPoint = PointImpl(doc, pointName, coords[0], coords[1])
        newPoint.transformer = TransformMapping.fromClazz(PointOnLineWithCoefficientTransformer::class)
        newPoint.transformData = PointOnLineWithCoefficientTransformData(
            targetParamIdx = 1,
            paramKind = ParamKind.PK_Value,
            rootPoint = angle.anglePoint.coordinates().toArray(),
            unitVector = vu.toArray()
        )
        newPoint.movementPath = MovementLinePath(angle.anglePoint.coordinates().toArray(), vu.toArray())

        val bisectorLine = LineSegmentImpl(doc, "${angle.anglePoint.name}${newPoint.name}", angle.anglePoint, newPoint)

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(bisectorLine)
        cr.mergeAsDependency(exr1.result)
        cr.addDependency(newPoint, listOf(angle), true)

        return cr
    }

    private fun constructBisectorAngleSegmentWithIntersectionLine(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        val exr1: ElementExtraction<Angle> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)

        val angle = exr1.result.result() ?: throw ElementNotExistInDocumentException("The angle does not exist")
        val intersectionLine =
            exr2.result.result() ?: throw ElementNotExistInDocumentException("The intersection line does not exist")

        val lsVector = angle.lineStart.parallelVector
        val leVector = angle.lineEnd.parallelVector
        val ds = angle.directionStart
        val de = angle.directionEnd

        val bisectorVectorArr = calculateBiSectorVectorOfAngle(
            listOf(ds * lsVector.x, ds * lsVector.y),
            listOf(de * leVector.x, de * leVector.y)
        )
        val bisectorVector = Vector3D.of(bisectorVectorArr[0], bisectorVectorArr[1], 0.0)

        val pointName = inputName?.let {
            NamePattern.extractPointName(LineVi::class, inputName) - setOf(angle.anglePoint.name)
        }?.first() ?: generatePointName(doc)
        val tempLine = LineImpl(doc, null, angle.anglePoint, bisectorVector)
        val intersectionPoint =
            Intersections.of(intersectionLine, tempLine) ?: throw ConstructionException("Invalid angle")

        val newPoint = PointImpl(doc, pointName, intersectionPoint.x, intersectionPoint.y)

        val newLine = LineSegmentImpl(
            doc, "${angle.anglePoint.name}${newPoint.name}", angle.anglePoint,
            newPoint
        )

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(newLine)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.addDependency(newPoint, listOf(angle, intersectionLine), true)

        return cr
    }


    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }
}
