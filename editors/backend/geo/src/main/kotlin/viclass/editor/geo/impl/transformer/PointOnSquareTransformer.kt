package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.transformdata.PointOnPolygonTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.impl.constructor.adjustSquarePoints
import viclass.editor.geo.impl.constructor.extractSquareFromPointsParams
import viclass.editor.geo.impl.constructor.isValidSquare
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.extractors.PointWithCoordsExtractor.Companion.formatPoint
import viclass.editor.geo.transformer.Transformer

/**
 * Update the position of a point constrained on a square (supports 3D).
 * Store the new position value [x, y, z] as string in ParamStore.
 */
class PointOnSquareTransformer : Transformer<PointOnPolygonTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: PointOnPolygonTransformData, pos: Vector3D) {
        val (points, isClockwise) = extractSquareFromPointsParams(doc, c)
        updatePoint(points, transformData.idxInPointParam, doc, pos)

        if (!isValidSquare(points))
            points.adjustSquarePoints(doc, isClockwise, transformData.idxInPointParam)

        // Update the points in the construction
        (c.params[0].specs.getParam(PK_Value) as ParamStoreArray).values = points.map { p ->
            formatPoint(p.name, p.x, p.y, p.z)
        }
    }

    private fun updatePoint(points: MutableList<Point>, idx: Int, doc: GeoDoc, pos: Vector3D) {
        points[idx] = PointImpl(doc, points[idx].name, pos.x, pos.y, pos.z)
    }
}
