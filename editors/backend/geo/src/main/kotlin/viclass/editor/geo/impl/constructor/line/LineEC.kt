package viclass.editor.geo.impl.constructor.line

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.extractors.LineExtractor
import kotlin.reflect.KClass

@Singleton
class LineEC(
    val extractorManager: ParamExtractorManager
) : ElementConstructor<LineVi>, Logging {

    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }

    private enum class CGS {
        ByPointsName
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.ByPointsName.name)
                    .constraintDefault()
                    .build(),
            )
            .elTypes(LineVi::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ByPointsName -> {
                Validations.validateNumConstraints(c, 0)
                constructFromLineSegmentName(doc, inputName!!, c)
            }
        }
    }

    private fun constructFromLineSegmentName(
        doc: GeoDoc,
        inputName: String,
        c: Construction,
    ): ConstructionResult<LineVi> {
        val extractor = extractorManager[LineExtractor::class.simpleName!!]
        return extractor.extractConstructionResult(doc, inputName, c.ctIdx, LineVi::class)
    }
}
