package viclass.editor.geo.impl.doc

import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.elements.Element
import viclass.editor.geo.entity.ConstraintGroup
import viclass.editor.geo.entity.ConstructorTemplate
import kotlin.reflect.KClass

class ConstructorTemplateBuilder private constructor(val constructor: ElementConstructor<*>) {

    val cgs : MutableList<ConstraintGroup> = mutableListOf()
    val elTypes : MutableList<KClass<out Element>> = mutableListOf()

    fun cgs(vararg c: ConstraintGroup): ConstructorTemplateBuilder {
        cgs.addAll(listOf(*c))
        return this
    }

    fun elTypes(vararg types: KClass<out Element>): ConstructorTemplateBuilder {
        elTypes.addAll(listOf(*types))
        return this
    }


    fun build(): ConstructorTemplate {
        val id = constructor.outputType().simpleName + "/" + constructor::class.simpleName
        return ConstructorTemplate(constructor, id, elTypes, cgs)
    }

    companion object {
        fun create(constructor: ElementConstructor<*>): ConstructorTemplateBuilder {
            return ConstructorTemplateBuilder(constructor)
        }
    }
}
