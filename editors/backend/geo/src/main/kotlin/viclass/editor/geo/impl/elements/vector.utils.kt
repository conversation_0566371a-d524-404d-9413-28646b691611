package viclass.editor.geo.impl.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point

/**
 * <AUTHOR>
 * - Create vector from small index to large index, from first creation to next creation
 * - When creating a vector but both ele1 and ele2 have not been added to the document,
 * the ele must be sent in the correct order
 */

fun createVectorByEl(
    doc: GeoDoc,
    el1: Point,
    el2: Point,
): Vector3D {
    try {
        val index1 = doc.getIndex(el1)
        val index2 = doc.getIndex(el2)

        return when {
            (index1 != null && index2 == null) -> {
                // element1 have index and element2 not have index, calculate vector from have index to not have index
                el1.coordinates().vectorTo(el2.coordinates())
            }

            (index1 == null && index2 != null) -> {
                // element1 not have index and element2 have index, calculate vector from have index to not have index
                el2.coordinates().vectorTo(el1.coordinates())
            }

            index1 != null && index2 != null -> {
                // Both indices are non-null, compare index and calculate vector accordingly
                if (index1 < index2) {
                    el1.coordinates().vectorTo(el2.coordinates())
                } else {
                    el2.coordinates().vectorTo(el1.coordinates())
                }
            }

            else -> {
                // Both indices are null, handle as needed or repeat the above logic
                el1.coordinates().vectorTo(el2.coordinates())
            }
        }
    } catch (t: Throwable) {
        throw t
    }
}