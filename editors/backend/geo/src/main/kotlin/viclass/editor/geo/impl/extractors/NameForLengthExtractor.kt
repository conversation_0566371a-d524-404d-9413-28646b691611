package viclass.editor.geo.impl.extractors

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ExtractableWithConstructions
import viclass.editor.geo.constructor.ExtractionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.exceptions.ExtractionFailedException
import viclass.editor.geo.extractable.NameForLength
import viclass.editor.geo.server.logger
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class NameForLengthExtractor(
    private val pointExtractor: PointExtractor,
) : ParameterExtractor {
    override var id: String = javaClass.simpleName

    private val pointRegex: Regex = Regex("([A-Z]\\d*'?)")

    @Throws(ExtractionFailedException::class, ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : ExtractionResult<out Any>> extract(doc: GeoDoc, store: ParamStore, ctIdx: Int, clazz: KClass<out Element>?): T {
        if (store is ParamStoreValue) {
            try {
                var reference: Point? = null
                var unknow: String? = null
                val data: Sequence<Pair<String, Point?>> = pointRegex.findAll(store.value).map { it.groupValues[0] }.map {
                    it to try {
                        val pointResult: ConstructionResult<Point> = pointExtractor.extractConstructionResult(doc, it, ctIdx)
                        pointResult.result()
                    } catch (ex: ElementNotExistInDocumentException) {
                        null
                    }
                }

                val numReference = data.count { it.second != null }

                if (numReference < 1) throw ExtractionFailedException("Don't have any reference")
                if (numReference > 1) throw ExtractionFailedException("Too many reference")

                data.forEach {
                    if (it.second != null) reference = it.second
                    else unknow = it.first
                }

                return ExtractableWithConstructions(NameForLength(store.value, reference, unknow)) as T
            } catch (ex: ExtractionFailedException) {
                logger.error("unknown exception... ", ex)
                throw ex
            } catch (t: Throwable) {
                logger.error("unknown exception... ", t)
                throw ExtractionFailedException(t)
            }
        }

        throw ExtractionFailedException("Param store type not supported")
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(NameForLength::class)
    }
}
