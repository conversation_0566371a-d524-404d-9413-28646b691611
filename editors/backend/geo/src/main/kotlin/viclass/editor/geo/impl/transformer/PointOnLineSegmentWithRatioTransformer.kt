package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.geometry.euclidean.threed.line.Lines3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnLineSegmentWithRatioTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.DEFAULT_PRECISION
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.impl.constructor.rotate90Degrees
import viclass.editor.geo.transformer.Transformer

/**
 * calculate k: (AB = k * vectorUnit)
 */
class PointOnLineSegmentWithRatioTransformer constructor(): Transformer<PointOnLineSegmentWithRatioTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: PointOnLineSegmentWithRatioTransformData, pos: Vector3D) {
        val sP = Vector3D.of(transformData.sPoint)
        val eP = Vector3D.of(transformData.ePoint)
        val vector = sP.vectorTo(eP)
        val line = Lines3D.fromPoints(sP, eP, DEFAULT_PRECISION)
        val line2 = Lines3D.fromPointAndDirection(pos, vector.rotate90Degrees(), DEFAULT_PRECISION)
        val i = line.intersection(line2)
        if (!line.contains(i)) {
            throw ConstructionException("ratio value need to > 0 and < line segment length")
        }
        val ratio = Distances.of(sP, i) / Distances.of(sP, eP)

        if (ratio < 0 || ratio > 1) throw ConstructionException("ratio value need to > 0 and < line segment length")

        val ps = c.params[transformData.targetParamIdx].specs
            .getParam(transformData.paramKind) as ParamStoreValue
        ps.value = ratio.toString()
    }
}