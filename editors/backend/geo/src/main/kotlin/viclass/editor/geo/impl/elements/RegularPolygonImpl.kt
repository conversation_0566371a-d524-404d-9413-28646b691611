package viclass.editor.geo.impl.elements

import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.RegularPolygon
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.impl.constructor.Points
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
class RegularPolygonImpl(
    doc: GeoDoc, name: String?,
    points: List<Point>
) : PolygonImpl(doc, name, points), RegularPolygon {
    override val clazz: KClass<out RegularPolygon> = RegularPolygon::class

    /**
     * Validates that the polygon defined by the 'points' list is a regular polygon.
     *
     * A regular polygon has two key properties:
     * 1. All sides are of equal length.
     * 2. All vertices are equidistant from the center.
     *
     * This function verifies these properties by:
     * - Calculating the initial side length between the first and last points.
     * - Determining the polygon's center point.
     * - Calculating the initial radius (distance from the center to the first vertex).
     * - Iterating through the points, ensuring each side length and vertex distance from the
     * center are within the specified tolerance (DEFAULT_TOLERANCE) of the initial values.
     *
     * If any side length or vertex distance deviates beyond the tolerance, an
     * InvalidElementException is thrown, indicating the polygon is not regular.
     *
     * @throws InvalidElementException if the polygon is not regular due to unequal side lengths
     * or vertices not equidistant from the center.
     */
    override fun validate() {
        // Calculate the length of the initial side (between the first and last points).
        val initialSideLength = Distances.of(points.first(), points.last())

        // Calculate the center point of the polygon.
        val polygonCenter = Points.calculateCenterPoint(points)

        // Calculate the initial radius (distance from the center to the first vertex).
        val initialRadius = Distances.of(polygonCenter, points[0].coordinates())

        // Iterate through adjacent pairs of points to validate side lengths and vertex distances.
        for (i in 0 until points.size) {
            val currentPoint = points[i]
            val nextPoint = points[(i + 1) % points.size] // Wrap around for the last side.

            // Validate side length against the initial length.
            if (Math.abs(Distances.of(currentPoint, nextPoint) - initialSideLength) > DEFAULT_TOLERANCE) {
                throw InvalidElementException("$name is not a regular polygon: unequal side lengths.")
            }

            // Validate vertex distance from the center against the initial radius.
            if (Math.abs(Distances.of(polygonCenter, currentPoint.coordinates()) - initialRadius) > DEFAULT_TOLERANCE) {
                throw InvalidElementException("$name is not a regular polygon: vertices not equidistant from center.")
            }
        }
    }

    override fun mergeFrom(other: Element) {
        if (other !is RegularPolygon) return
        super<PolygonImpl>.mergeFrom(other)
    }
}
