package viclass.editor.geo.impl.constructor.line

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementExtraction
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Triangle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.createVectorByEl
import viclass.editor.geo.impl.extractors.LineExtractor
import viclass.editor.geo.impl.extractors.PointExtractor
import kotlin.reflect.KClass

@Singleton
class MedianOfTriangleEC constructor(
    private val extractorManager: ParamExtractorManager
) : ElementConstructor<LineVi> {

    private enum class CGS {
        MedianLineSegment, MedianLine
    }

    override fun template(): ConstructorTemplate {
        val cg1 = ConstraintGroupBuilder.create()
            .name(CGS.MedianLineSegment.name)
            .hints("hint-MedianOfTriangle")
            .constraint(
                0, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aTriangle]!!,
                listOf("NameOfTriangle"),
                "tpl-MedianOfTriangle"
            )
            .build()

        val cg2 = ConstraintGroupBuilder.create()
            .name(CGS.MedianLine.name)
            .hints("hint-MedianOfTriangleThroughVertex")
            .constraint(
                0, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aTriangle]!!,
                listOf("NameOfTriangle"),
                "tpl-MedianOfTriangle"
            )
            .constraint(
                1, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .build()

        return ConstructorTemplateBuilder.create(this)
            .cgs(cg1, cg2)
            .elTypes(LineVi::class, LineSegment::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.MedianLineSegment -> {
                Validations.validateNumConstraints(c, 1)
                constructMedianLineSegment(doc, inputName, c)
            }

            CGS.MedianLine -> {
                Validations.validateNumConstraints(c, 2)
                constructMedianLine(doc, inputName, c)
            }
        }
    }

    private fun constructMedianLine(doc: GeoDoc, name: String?, c: Construction): ConstructionResult<LineVi> {
        val extractionResult = extractFirstPossible<ElementExtraction<Triangle>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val triangle = extractionResult.result.result()!!
        val pointER = extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val fromVertex = pointER.result.result()!!

        if (!triangle.name!!.contains(fromVertex.name!!))
            throw ConstructionException("Vertex ${fromVertex.name} is not belong to triangle ${triangle.name}")

        val toLineName: String = triangle.name!!.replace(fromVertex.name!!, "")

        val extractorLineSegment = extractorManager[LineExtractor::class.simpleName!!]
        val toLineCR: ConstructionResult<LineSegment> = extractorLineSegment.extractConstructionResult(doc, toLineName, c.ctIdx)
        val toLine = toLineCR.result()!!

        // calculate median line
        val middlePointCR = calculatePoint(doc, "", toLine.p1, toLine.p2)
        val medianVector = createVectorByEl(doc, middlePointCR.result()!!, fromVertex)
        val medianLine = LineImpl(doc, name, fromVertex, medianVector)

        val cr = ConstructionResultImpl<LineVi>()

        if (c.entity.elType == LineSegment::class.simpleName) {
            val v = Intersections.of(toLine, medianLine) ?: throw ConstructionException("invalid triangle")
            val point = PointImpl(doc, generatePointName(doc), v)
            val line = LineSegmentImpl(doc, "${fromVertex.name}${point.name}", fromVertex, point)
            cr.setResult(line)
            cr.addDependency(point, listOf(), true)
        } else if (c.entity.elType == LineVi::class.simpleName) {
            cr.setResult(medianLine)
        }
        cr.mergeAsDependency(toLineCR)
        cr.addDependency(fromVertex, listOf(), true)

        return cr
    }

    private fun calculatePoint(doc: GeoDoc, name: String?, point: Point, point1: Point): ConstructionResultImpl<Point> {
        val c = ConstructionResultImpl<Point>()
        val x = (point.x + point1.x) / 2
        val y = (point.y + point1.y) / 2
        val z = (point.z + point1.z) / 2

        c.setResult(PointImpl(doc, name, x, y, z))
        return c
    }

    private fun constructMedianLineSegment(doc: GeoDoc, name: String?, c: Construction): ConstructionResult<LineVi> {
        val extractionResult = extractFirstPossible<ElementExtraction<Triangle>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val triangle = extractionResult.result.result()!!

        var fromVertexName: String? = null
        var toLineName: String? = null
        var middlePointName: String? = null

        for (regex in NamePattern[LineSegment::class]!!) {
            if (regex.matches(name!!)) {
                val matchResult = regex.find(name)!!
                val p1 = matchResult.groups[1]?.value!!
                val p2 = matchResult.groups[2]?.value!!

                if (triangle.name!!.contains(p1) && !triangle.name!!.contains(p2)) {
                    fromVertexName = p1
                    toLineName = triangle.name!!.replace(p1, "")
                    middlePointName = p2
                    break
                }

                if (!triangle.name!!.contains(p1) && triangle.name!!.contains(p2)) {
                    fromVertexName = p2
                    toLineName = triangle.name!!.replace(p2, "")
                    middlePointName = p1
                    break
                }

                throw ConstructionException("Median line $name is not valid")
            }
        }

        if (fromVertexName == null) throw ConstructionException("Median line $name is not valid")
        if (doc.findElementByName(middlePointName!!, Point::class, c.ctIdx) != null) {
            throw ElementExistInDocumentException("$middlePointName is already exist")
        }

        val extractorLineSegment = extractorManager[LineExtractor::class.simpleName!!]
        val extractorPoint = extractorManager[PointExtractor::class.simpleName!!]

        val toLineCR: ConstructionResult<LineSegment> = extractorLineSegment.extractConstructionResult(doc, toLineName!!, c.ctIdx)
        val fromVertexCR: ConstructionResult<Point> = extractorPoint.extractConstructionResult(doc, fromVertexName, c.ctIdx)

        val toLine = toLineCR.result()!!
        val fromVertex = fromVertexCR.result()!!

        // calculate median line
        val middlePointCR = calculatePoint(doc, middlePointName, toLine.p1, toLine.p2)
        val medianVector = createVectorByEl(doc, middlePointCR.result()!!, fromVertex)
        val medianLine = LineImpl(doc, name, fromVertex, medianVector)

        // calculate intersection
        val intersection = medianLine.line().intersection(toLine.line())
        val middlePoint = PointImpl(doc, middlePointName, intersection.x, intersection.y, intersection.z)

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(LineSegmentImpl(doc, name, fromVertex, middlePoint))
        cr.addDependency(fromVertex, listOf(), true)
        cr.addDependency(middlePoint, listOf(), true)

        return cr
    }

    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }
}
