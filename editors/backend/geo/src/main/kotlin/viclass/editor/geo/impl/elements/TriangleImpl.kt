package viclass.editor.geo.impl.elements

import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Triangle
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
open class TriangleImpl(
    doc: GeoDoc, name: String?,
    private var _point1: Point, private var _point2: Point, private var _point3: Point
) : Triangle, PolygonImpl(doc, name, listOf(_point1, _point2, _point3)) {

    override val clazz: KClass<out Triangle> = Triangle::class

    override val point1: Point
        get() = _point1
    override val point2: Point
        get() = _point2
    override val point3: Point
        get() = _point3

    /**
     * Validates the Triangle element to ensure it forms a valid triangle.
     *
     * This function verifies that the three points (point1, point2, point3) define a valid triangle
     * by applying the triangle inequality theorem: the sum of any two side lengths must be
     * strictly greater than the third side length. Due to floating-point precision limitations,
     * we check if the difference between the sum of two sides and the third side is within a
     * small tolerance (DEFAULT_TOLERANCE).
     *
     * If any combination of side lengths fails this check, it implies the points are collinear
     * or nearly collinear, indicating an invalid triangle.
     *
     * @throws InvalidElementException if the triangle inequality theorem is violated, indicating
     * that the points do not form a valid triangle.
     */
    override fun validate() {
        // Calculate the lengths of the triangle's three sides.
        val side1 = Distances.of(point1, point2)
        val side2 = Distances.of(point2, point3)
        val side3 = Distances.of(point3, point1)

        // Check if the triangle inequality theorem holds within the tolerance.
        // If any of these conditions are true, the points are collinear or nearly collinear.
        if (Math.abs(side1 + side2 - side3) < DEFAULT_TOLERANCE ||
            Math.abs(side1 + side3 - side2) < DEFAULT_TOLERANCE ||
            Math.abs(side2 + side3 - side1) < DEFAULT_TOLERANCE
        ) {
            // The triangle inequality theorem is violated, indicating an invalid triangle.
            throw InvalidElementException("$name is not a triangle")
        }
    }

    override fun vertices(): List<Point> {
        return listOf(point1, point2, point3)
    }

    /**
     * Checks if the given name matches the Triangle's name, considering both exact matches and name patterns.
     *
     * This function determines if the provided `name` matches the Triangle's name. It performs the following checks:
     * 1.  Validates the `name` against the naming pattern defined for the Triangle class using `NamePattern.isNameValid()`.
     * 2.  Checks for an exact match between the given `name` and the Triangle's `this.name`.
     * 3.  If the `name` matches the Triangle's naming pattern, it extracts the point names and checks if they
     * form a valid permutation of the Triangle's name.
     *
     * @param name The name to check for a match.
     * @return `true` if the name matches the Triangle's name, `false` otherwise.
     */
    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Triangle::class, name)) return false
        return NamePattern.extractPointName(Triangle::class, this.name!!).toSet() ==
                NamePattern.extractPointName(Triangle::class, name).toSet()
    }

    override fun mergeFrom(other: Element) {
        if (other !is Triangle) return
        super<PolygonImpl>.mergeFrom(other)

    }
}
