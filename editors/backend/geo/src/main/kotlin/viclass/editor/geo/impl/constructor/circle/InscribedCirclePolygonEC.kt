package viclass.editor.geo.impl.constructor.circle

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementExtraction
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.CircleImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.reflect.KClass

/** <AUTHOR> */
@Singleton
class InscribedCirclePolygonEC : ElementConstructor<Circle> {
    override fun outputType(): KClass<Circle> {
        return Circle::class
    }

    private enum class CGS {
        InscribedCirclePolygon
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.InscribedCirclePolygon.name)
                    .hints()
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPolygon]!!,
                        listOf("NameOfPolygon"),
                        "tpl-InscribedOf"
                    )
                    .build(),
            )
            .elTypes(Circle::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Circle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.InscribedCirclePolygon -> {
                Validations.validateNumConstraints(c, 1)
                constructInscribedCirclePolygon(doc, inputName, c)
            }
        }
    }

    private fun constructInscribedCirclePolygon(
        doc: GeoDoc,
        name: String?,
        c: Construction
    ): ConstructionResult<Circle> {
        val polygonCR =
            extractFirstPossible<ElementExtraction<Polygon>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val polygon = polygonCR.result.result() ?: throw ElementNotExistInDocumentException("Không tìm thấy đa giác")

        val points = polygon.vertices()
        if (points.size < 3)
            throw ConstructionException("Đa giác phải có ít nhất 3 cạnh")
        points.forEach { polygonCR.result.addDependency(it, listOf(), true) }

        val centerCoord = runCatching {
            Polygons.findInscribedCircleCenter(points.map { it.coordinates() })
                ?: throw ConstructionException("Không thể tìm thấy tâm đường tròn nội tiếp")
        }.getOrElse { e ->
            when (e) {
                is ConstructionException -> throw e
                else -> {
                    throw ConstructionException("Lỗi không xác định khi tính toán tâm đường tròn nội tiếp", e)
                }
            }
        }

        val centerName = name ?: generateCircleName(doc)
        val center = PointImpl(doc, centerName, centerCoord)

        val radius = Lines.distancePointToSegment(center, points[0], points[1])

        // Create a ConstructionResult for the center point, depending on the polygon
        val centerCR = ConstructionResultImpl<PointImpl>()
        centerCR.setResult(center)
        centerCR.mergeAsDependency(polygonCR.result)

        // Return the final ConstructionResult containing the inscribed circle
        return ConstructionResultImpl<Circle>().apply {
            setResult(CircleImpl(doc, centerName, center, radius))
            mergeAsDependency(polygonCR.result) // Circle depends on polygon
            mergeAsDependency(centerCR) // Circle depends on center (and its dependencies)
        }
    }
}
