package viclass.editor.geo.impl.constructor

import org.apache.commons.numbers.core.Precision
import viclass.editor.geo.constructor.ConstructionParams
import viclass.editor.geo.constructor.ExtractionResult
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.exceptions.ExtractionNotFoundException
import kotlin.math.PI
import kotlin.math.abs

const val DEFAULT_TOLERANCE = 1.0e-6
val DEFAULT_PRECISION = Precision.doubleEquivalenceOfEpsilon(DEFAULT_TOLERANCE)

/**
 * An util function to find the first possible extractor that satisfies among the candidate extractors
 * Record down the extractor id used inside the param specs
 */
@Throws(RuntimeException::class)
fun <T : ExtractionResult<out Any>> extractFirstPossible(
    doc: GeoDoc,
    extractionName: String,
    constraint: ConstructionParams,
    ctIdx: Int,
): T {
    var usedExtractor: String? = null
    // an extractor has been used before and recorded in specs
    if (constraint.specs.extractorIds.containsKey(extractionName)) {
        usedExtractor = constraint.specs.extractorIds[extractionName]
    }

    for (extr in constraint.extractor(extractionName)) {
        if (usedExtractor != null && extr.id != usedExtractor) continue

        val extractedResult = extr.extract<T>(doc, extractionName, constraint.paramDef.id, constraint.specs, ctIdx)
        constraint.useExtractor(extractionName, extr)

        return extractedResult
    }

    throw ExtractionNotFoundException("No extractor was found to be suitable for extracting $extractionName")
}

fun radian(degree: Double): Double = degree * PI / 180

fun degree(radian: Double): Double = radian * 180 / PI

fun Int.boundIndex(max: Int): Int = this.mod(max + 1)

fun Int.spaceToIndex(des: Int, max: Int): Int {
    val i1 = if (this < 0 || this > max) this.boundIndex(max) else this
    val i2 = if (des < 0 || des > max) des.boundIndex(max) else des
    if (i1 == i2) return 0

    val side1 = abs(i1 - i2) - 1
    val side2 = max - side1 - 1

    return if (side1 < side2) side1 else side2
}

fun Int.nextIndex(max: Int): Int = (this + 1).boundIndex(max)

fun Int.prevIndex(max: Int): Int = (this - 1).boundIndex(max)

fun Int.isAdjacentIndex(idx2: Int, max: Int): Boolean =
    (this + 1).mod(max + 1) == idx2 || (this - 1).mod(max + 1) == idx2
