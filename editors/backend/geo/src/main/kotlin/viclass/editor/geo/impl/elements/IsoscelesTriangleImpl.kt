package viclass.editor.geo.impl.elements

import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.IsoscelesTriangle
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import kotlin.math.abs
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
open class IsoscelesTriangleImpl constructor(
    doc: GeoDoc, name: String?,
    override val apexPoint: Point, point2: Point, point3: Point
) : IsoscelesTriangle, TriangleImpl(doc, name, apexPoint, point2, point3) {
    override val clazz: KClass<out IsoscelesTriangle> = IsoscelesTriangle::class

    override fun validate() {
        val l1 = Distances.of(apexPoint, point2)
        val l2 = Distances.of(apexPoint, point3)
        if (abs(l1 - l2) > DEFAULT_TOLERANCE)
            throw InvalidElementException("$name is not a isosceles triangle")
    }

    override fun mergeFrom(other: Element) {
        if (other !is IsoscelesTriangle) return
        super<TriangleImpl>.mergeFrom(other)
    }
}
