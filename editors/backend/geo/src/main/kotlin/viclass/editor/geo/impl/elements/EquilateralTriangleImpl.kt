package viclass.editor.geo.impl.elements

import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.EquilateralTriangle
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.impl.constructor.Points
import kotlin.math.abs


/**
 *
 * <AUTHOR>
 */
class EquilateralTriangleImpl constructor(
    doc: GeoDoc, name: String?,
    point1: Point, point2: Point, point3: Point
): EquilateralTriangle, TriangleImpl(doc, name, point1, point2, point3) {
    override val clazz = EquilateralTriangle::class

    /**
     * Validates that the triangle defined by 'point1', 'point2', and 'point3' is an Equilateral Triangle.
     *
     * Checks if all three sides of the triangle have equal lengths.
     *
     * @throws InvalidElementException if the triangle is not an Equilateral Triangle.
     */
    override fun validate() {
        // Calculate the lengths of the three sides.
        val side12 = Distances.of(point1, point2)
        val side23 = Distances.of(point2, point3)
        val side31 = Distances.of(point3, point1)

        // Check if all three sides are equal within the tolerance.
        if (abs(side12 - side23) > DEFAULT_TOLERANCE ||
            abs(side23 - side31) > DEFAULT_TOLERANCE ||
            abs(side31 - side12) > DEFAULT_TOLERANCE
        ) {
            throw InvalidElementException("$name is not an equilateral triangle")
        }
    }

    override fun mergeFrom(other: Element) {
        if (other !is EquilateralTriangle) return
        super<TriangleImpl>.mergeFrom(other)
    }
}
