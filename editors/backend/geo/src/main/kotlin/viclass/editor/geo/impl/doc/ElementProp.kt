package viclass.editor.geo.impl.doc

import viclass.editor.geo.elements.Element

/**
 * ElementProp is the class that holds element data that is necessary
 * for the document to manage elements. For example, the element index
 * inside the doc, the constructor index
 */
class ElementProp constructor(
    var el: Element,

    /**
     * Element index and constructor index
     */
    var elIndex : Int,

    /**
     * Index of the construction of this element
     */
    var cIndex : Int,

    /**
     * Whether the element is inferred from other elements
     * and is needed for the construction of other
     */
    var isInferred : Boolean
)
