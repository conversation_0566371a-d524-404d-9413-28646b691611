package viclass.editor.geo.impl.elements

import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Angle
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.constructor.Distances
import kotlin.math.acos
import kotlin.math.round
import kotlin.reflect.KClass

class AngleImpl(
    override val doc: GeoDoc,
    override var name: String?,
    private var _anglePoint: Point,
    private var _lineStart: LineVi,
    private var _directionStart: Int,
    private var _lineEnd: LineVi,
    private var _directionEnd: Int,
): Angle {
    override val clazz = Angle::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true

    override val anglePoint: Point
        get() = _anglePoint
    override val lineStart: LineVi
        get() = _lineStart
    override val directionStart: Int
        get() = _directionStart
    override val lineEnd: LineVi
        get() = _lineEnd
    override val directionEnd: Int
        get() = _directionEnd

    override fun vertices(): List<Point> {
        return listOf(anglePoint)
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Angle::class, name)) return false

        if (name == this.name) return true

        return false
    }

    override fun mergeFrom(other: Element) {
        if (other !is Angle) return
        super.mergeFrom(other)

        _directionStart = other.directionStart
        _directionEnd = other.directionEnd
    }

    override fun angle(): Float? {
        if(lineStart.p2 == null || lineEnd.p2 == null) return null

        val a = lineEnd.length()
        val b = Distances.of(
            if(lineStart.p2!!.name == _anglePoint.name) lineStart.p1 else lineStart.p2!!,
            if(lineEnd.p2!!.name == _anglePoint.name) lineEnd.p1 else lineEnd.p2!!
        )
        val c = lineStart.length()

        return (round((acos(((c * c) + (a * a) - (b * b)) / (2 * a * c)) * 180 / Math.PI) * 10) / 10.0).toFloat()
    }
}
