package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.MoveOrderTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.impl.constructor.rotate
import viclass.editor.geo.transformer.Transformer

/**
 * param store is a position = [x, y, z]
 */
class RhombusVertexTransformer constructor(): Transformer<MoveOrderTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: MoveOrderTransformData, pos: Vector3D) {
//        val ps1 = c.params[0].specs.getParam(ParamKind.PK_Value) as ParamStoreArray
//        val ps2 = c.params[0].specs.getParam(ParamKind.PK_Value) as ParamStoreArray
//        val ps3 = transformData.lengthParamIdx?.let { c.params[it].specs.getParam(ParamKind.PK_Value) as ParamStoreValue }
//        val p1 = Vector3D.of(ps1.values.map { it.toDouble() }.toDoubleArray())
//        val p2 = Vector3D.of(ps2.values.map { it.toDouble() }.toDoubleArray())
//        val center = Vector3D.of(transformData.center)
//        val p = Vector3D.of(transformData.source)
//        val vectorC0 = center.vectorTo(Vector3D.of(center.x+10, center.y, center.z))
//        val vectorCP = center.vectorTo(p)
//        val vectorCPos = center.vectorTo(pos)
//        val angleP = vectorC0.angleTo(vectorCP)
//        val anglePos = vectorC0.angleTo(vectorCPos)
//        val angleDelta = anglePos - angleP
//        val unitVectorPos = vectorCPos.normalize()
//        val k = vectorCPos.x / unitVectorPos.x
//        val rotateP1 = p1.rotate(angleDelta, center)
//        val rotateP2 = p2.rotate(angleDelta, center)
//        val unitVectorNewP1 = center.vectorTo(rotateP1).normalize()
////        val unitVectorNewP2 = center.vectorTo(rotateP2).normalize()
//        val p1New = unitVectorNewP1.multiply(k).add(center)
////        val p2New = unitVectorNewP2.multiply(k).add(center)
//        ps1.values = p1New.toArray().map { it.toString() }
//        ps2.values = rotateP2.toArray().map { it.toString() }
//        ps3?.let { it.value = (2 * pos.distance(center)).toString() }
    }
}