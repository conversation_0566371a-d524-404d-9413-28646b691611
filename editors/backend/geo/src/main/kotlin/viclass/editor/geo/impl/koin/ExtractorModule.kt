package viclass.editor.geo.impl.koin

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import org.koin.core.component.KoinComponent
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.impl.extractors.CircleExtractor
import viclass.editor.geo.impl.extractors.LineExtractor
import viclass.editor.geo.impl.extractors.PointExtractor

@Module
@ComponentScan("viclass.editor.geo.impl.extractors")
class ExtractorModule : KoinComponent {

    @Singleton @Named(PARAMETER_EXTRACTORS)
    fun provideParameterExtractors(): Set<ParameterExtractor> {
        return getKoin().getAll<ParameterExtractor>().toSet()
    }
    @Singleton
    fun provideEvaluator(): Evaluator = Evaluator()
}