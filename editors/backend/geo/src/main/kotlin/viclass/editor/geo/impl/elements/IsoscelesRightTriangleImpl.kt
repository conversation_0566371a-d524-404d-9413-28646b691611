package viclass.editor.geo.impl.elements

import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.IsoscelesRightTriangle
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import kotlin.math.abs


/**
 *
 * <AUTHOR>
 */
class IsoscelesRightTriangleImpl constructor(
    doc: GeoDoc, name: String?,
    override val rightPoint: Point, point2: Point, point3: Point
): IsoscelesRightTriangle, IsoscelesTriangleImpl(doc, name, rightPoint, point2, point3) {
    override val clazz = IsoscelesRightTriangle::class

    /**
     * Validates whether the triangle is an isosceles right triangle.
     *
     * This method is called externally and is automatically triggered when the constructor sets the result.
     * It ensures that the triangle meets the following conditions:
     *
     * 1. The two legs of the triangle (formed by `rightPoint` to `point2` and `rightPoint` to `point3`)
     *    must have equal lengths within a predefined tolerance.
     * 2. The two legs must be perpendicular to each other, meaning their dot product should be close to zero.
     *
     * @throws InvalidElementException If the triangle is not isosceles or not a right triangle.
     */
    override fun validate() {
        val l1 = LineSegmentImpl(doc, "${rightPoint.name}${point2.name}", rightPoint, point2)
        val l2 = LineSegmentImpl(doc, "${rightPoint.name}${point3.name}", rightPoint, point3)
        if (abs(l1.length() - l2.length()) > DEFAULT_TOLERANCE)
            throw InvalidElementException("$name is not a isosceles triangle")
        if (abs(l1.parallelVector.dot(l2.parallelVector)) > DEFAULT_TOLERANCE)
            throw InvalidElementException("$name is not a right triangle")
    }

    override fun mergeFrom(other: Element) {
        if (other !is IsoscelesRightTriangle) return
        super<IsoscelesTriangleImpl>.mergeFrom(other)
    }
}
