package viclass.editor.geo.impl.elements

import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Trapezoid
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import kotlin.math.abs
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
open class TrapezoidImpl(
    doc: GeoDoc, name: String?,
    point1: Point, point2: Point, point3: Point, point4: Point
) : Trapezoid, QuadrilateralImpl(doc, name, point1, point2, point3, point4) {
    override val clazz: KClass<out Trapezoid> = Trapezoid::class

    /**
     * Validates if the quadrilateral formed by the given points is a valid Trapezoid.
     *
     * This function checks if the shape meets the requirements of a Trapezoid,
     * specifically that the base sides (point3, point4) and (point1, point2) are parallel,
     * and that the shape is not a parallelogram.
     *
     * This method is called externally and is automatically triggered when the constructor sets the result.
     *
     * @throws InvalidElementException if the shape is not a valid Trapezoid.
     */
    override fun validate() {
        super<QuadrilateralImpl>.validate()

        // Calculate the slopes of the lines formed by the points
        val slope12 = calculateSlope(point1, point2)
        val slope34 = calculateSlope(point3, point4)

        // Check if the base sides are parallel
        if (!areSlopesEqual(slope12, slope34)) {
            throw InvalidElementException("$name is not a Trapezoid: base sides are not parallel.")
        }

        //Check that it is not a parallelogram.
        val slope23 = calculateSlope(point2, point3)
        val slope41 = calculateSlope(point4, point1)

        if(areSlopesEqual(slope23, slope41)){
            throw InvalidElementException("$name is a parallelogram, not a trapezoid.")
        }
    }

    /**
     * Calculates the slope of a line formed by two points.
     *
     * @param point1 The first point.
     * @param point2 The second point.
     * @return The slope of the line, or Double.NaN if the line is vertical.
     */
    private fun calculateSlope(point1: Point, point2: Point): Double {
        val deltaX = point2.x - point1.x
        val deltaY = point2.y - point1.y

        if (deltaX == 0.0) {
            return Double.NaN // Vertical line
        }

        return deltaY / deltaX
    }

    /**
     * Checks if two slopes are equal.
     *
     * @param slope1 The first slope.
     * @param slope2 The second slope.
     * @return True if the slopes are equal, false otherwise.
     */
    private fun areSlopesEqual(slope1: Double, slope2: Double): Boolean {
        if (slope1.isNaN() && slope2.isNaN()) {
            return true // Both lines are vertical
        }
        return abs(slope1 - slope2) < DEFAULT_TOLERANCE
    }

    override fun mergeFrom(other: Element) {
        if (other !is Trapezoid) return
        super<QuadrilateralImpl>.mergeFrom(other)
    }
}
