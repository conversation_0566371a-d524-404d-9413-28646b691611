package viclass.editor.geo.impl.extractors

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.BooleanExtraction
import viclass.editor.geo.constructor.BooleanListExtraction
import viclass.editor.geo.constructor.ExtractionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.exceptions.ExtractionFailedException
import viclass.editor.geo.server.logger
import kotlin.reflect.KClass

@Singleton
class BooleanExtractor() : ParameterExtractor {
    override var id: String = javaClass.simpleName

    @Throws(ExtractionFailedException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : ExtractionResult<out Any>> extract(
        doc: GeoDoc, store: ParamStore, ctIdx: Int, clazz: KClass<out Element>?
    ): T {
        try {
            return when (store) {
                is ParamStoreValue -> {
                    if (store.value.possiblyBoolean()) {
                        BooleanExtraction(store.value.toBoolean()) as T
                    } else {
                        throw ExtractionFailedException("Value ${store.value} is not a boolean")
                    }
                }

                is ParamStoreArray -> {
                    val result = store.values.map { v ->
                        if (v.possiblyBoolean()) {
                            v.toBoolean()
                        } else {
                            throw ExtractionFailedException("Value $v is not a boolean")
                        }
                    }
                    BooleanListExtraction(result) as T
                }

                else -> throw ExtractionFailedException("Param store type not supported")
            }
        } catch (t: Throwable) {
            logger.error("unknown exception... ", t)
            throw ExtractionFailedException(t)
        }
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(Boolean::class)
    }
}
