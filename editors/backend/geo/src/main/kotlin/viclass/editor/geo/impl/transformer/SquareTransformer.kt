package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ElementListExtraction
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.transformdata.SquareTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.transformer.Transformer

/**
 * Update the position of a point constrained on a square (supports 3D).
 * Store the new position value [x, y, z] as string in ParamStore.
 */
class SquareTransformer : Transformer<SquareTransformData> {
    // Reformat the stored point string: "name,x,y,z"
    fun formatPoint(name: String?, x: Double, y: Double, z: Double): String {
        return "${name ?: ""},$x,$y,$z"
    }

    override fun apply(doc: GeoDoc, c: Construction, transformData: SquareTransformData, pos: Vector3D) {
        // Get the ParamStore that contains the square's points
        val paramStoreArray =
            c.params[transformData.targetParamIdx].specs.getParam(PK_Value) as ParamStoreArray

        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Value, c.params[0], c.ctIdx)
        val points = ext.result.map { it.result() ?: throw IllegalArgumentException("Invalid point extraction") }
            .takeIf { it.isNotEmpty() }
            ?: throw IllegalArgumentException("No points found in param store at index ${transformData.targetParamIdx}.")

        require(points.size == 4) { "There must be exactly 4 points to define a square" }

//        // Find the vertex opposite to the one being moved
//        val movedIdx = transformData.idxInPointParam
//        val oppositeIdx = (movedIdx + 2) % 4
//        val opposite = points.getOrNull(oppositeIdx)
//            ?: throw IllegalArgumentException("Opposite vertex not found")
//
//        if (points.withIndex().any { (index, point) -> index != oppositeIdx && point.transformer != null })
//            throw IllegalArgumentException("Cannot move point ${points[movedIdx].name} because other points are constrained.")
//
//        // Calculate the center of the square in 3D space
//        val centerX = (pos.x + opposite.x) / 2.0
//        val centerY = (pos.y + opposite.y) / 2.0
//        val centerZ = (pos.z + opposite.z) / 2.0
//
//        // Vector from the center to the new vertex, as Vector3D
//        val v = Vector3D.of(
//            pos.x - centerX,
//            pos.y - centerY,
//            pos.z - centerZ
//        )
//
//        // Determine the plane containing the square via three initial points
//        // Pick any 3 points to determine the plane
//        val p0 = Vector3D.of(points[0].x, points[0].y, points[0].z)
//        val p1 = Vector3D.of(points[1].x, points[1].y, points[1].z)
//        val p2 = Vector3D.of(points[2].x, points[2].y, points[2].z)
//
//        // Vectors lying in the plane
//        val u = p1.subtract(p0)
//        val w = p2.subtract(p0)
//
//        // Normal vector of the plane
//        val n = u.cross(w).normalizeOrNull()
//            ?: throw IllegalArgumentException("Points are not coplanar or are coincident!")
//
//        // Function to rotate vector v around axis n by angle
//        fun rotateVector(
//            vector: Vector3D,
//            axis: Vector3D,
//            angle: Double
//        ): Vector3D {
//            // Use Rodrigues' formula for rotating a vector
//            val cosA = Math.cos(angle)
//            val sinA = Math.sin(angle)
//            val dot = axis.dot(vector)
//            val cross = axis.cross(vector)
//            val term1 = vector.multiply(cosA)
//            val term2 = cross.multiply(sinA)
//            val term3 = axis.multiply(dot * (1 - cosA))
//            return term1.add(term2).add(term3)
//        }
//
//        // Rotate v around n 90 degrees (pi/2) and -90 degrees (-pi/2)
//        val angle90 = Math.PI / 2
//        val rotated1 = rotateVector(v, n, angle90)
//        val rotated2 = rotateVector(v, n, -angle90)
//
//        val r1 = Vector3D.of(centerX + rotated1.x, centerY + rotated1.y, centerZ + rotated1.z)
//        val r2 = Vector3D.of(centerX + rotated2.x, centerY + rotated2.y, centerZ + rotated2.z)
//
//        // Map each point according to its role:
//        // - movedIdx: use the new coordinates
//        // - oppositeIdx: keep unchanged
//        // - the other two vertices: calculate correct position by right-angle check
//        paramStoreArray.values = points.mapIndexedNotNull { index, point ->
//            when (index) {
//                movedIdx -> formatPoint(point.name, pos.x, pos.y, pos.z)
//                (movedIdx + 1) % 4 -> formatPoint(point.name, r1.x, r1.y, r1.z)
//                (movedIdx + 2) % 4 -> formatPoint(point.name, opposite.x, opposite.y, opposite.z)
//                (movedIdx + 3) % 4 -> formatPoint(point.name, r2.x, r2.y, r2.z)
//                else -> null // No need to update this point
//            }
//        }
    }
}