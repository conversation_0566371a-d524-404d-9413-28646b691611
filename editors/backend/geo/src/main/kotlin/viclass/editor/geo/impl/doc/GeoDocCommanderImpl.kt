package viclass.editor.geo.impl.doc

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.bson.types.ObjectId
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.dbentity.*
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.renderdata.RenderElement
import viclass.editor.geo.dbentity.renderdata.RenderLine
import viclass.editor.geo.dbentity.renderdata.RenderVertex
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.doc.GeoDocCommander
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.exceptions.*
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.db.GeoDocumentDBS
import viclass.editor.geo.impl.elements.serializers
import viclass.editor.geo.impl.koin.CONSTRUCTOR_TEMPLATES
import viclass.editor.geo.impl.render.RenderDocImpl
import viclass.editor.geo.models.request.ElConstructionRequest
import viclass.editor.geo.models.request.RelType
import viclass.editor.geo.models.request.RenameElementModel
import viclass.editor.geo.models.response.MoveElementResponse
import viclass.editor.geo.models.response.ReconstructionResponse
import viclass.editor.geo.render.RenderDocState
import viclass.editor.geo.server.logger
import java.util.*
import kotlin.reflect.KClass
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.memberProperties

// GeoDocCommanderImpl is the main implementation of the GeoDocCommander interface.
// It manages the lifecycle, caching, and manipulation of geometric documents (GeoDoc),
// including their construction, serialization, rendering, and database persistence.
@Singleton
class GeoDocCommanderImpl
constructor(
    // Map of constructor templates, injected by Koin DI. Used to instantiate geometric
    // constructions by type.
    @Named(CONSTRUCTOR_TEMPLATES) private val ctpl: Map<String, ConstructorTemplate>,
    // Manager for constraint parameter definitions, provides parameter type and validation
    // info.
    private val defMan: ConstraintParamDefManager,
    // Manager for parameter extractors, used to extract construction parameters from the
    // document context.
    private val peMan: ParamExtractorManager,
    // Database service for GeoDocument persistence (CRUD operations).
    private val geoDocDBS: GeoDocumentDBS
) : GeoDocCommander {

    /**
     * In-memory cache of loaded GeoDoc instances, keyed by document ID. This cache should be
     * cleaned up when it grows too large or when documents have not been modified for a certain
     * period (see TODO).
     *
     * TODO: Implement cache eviction policy for memory management.
     */
    private val docs = mutableMapOf<String, GeoDoc>() // cache all the document

    // TODO get the geo document and create the necessary geodoc when the doc isn't in the cache
    override suspend fun retrieveDoc(id: String): GeoDoc? {
        var doc = docs[id]
        if (doc == null) {
            val bsonDoc = geoDocDBS.fetch(id) ?: return null
            doc = convertEntityToDoc(bsonDoc)
            docs[id] = doc
        }

        return doc
    }

    override fun replaceDoc(entity: GeoDocument): GeoDocImpl {
        val doc = convertEntityToDoc(entity)
        docs[entity.id] = doc
        return doc
    }

    override fun convertEntityToDoc(entity: GeoDocument): GeoDocImpl {
        val doc = GeoDocImpl(this, entity.numDim, entity.id)
        doc.copyElementsFromEntity(entity)
        doc.constructions = entity.elConstructions.map { convertEntityToConstruction(doc, it) }
        doc.renderDoc = RenderDocImpl(doc, entity.docRenderProp.toRenderDocState(doc.id, doc.numDim))
        doc.renderDoc.addRenderEls(entity.renderEl)
        doc.docDefaultElRenderProp = entity.docDefaultElRenderProp

        return doc
    }

    @Throws(RuntimeException::class)
    override fun convertDocToEntity(geoDoc: GeoDoc): GeoDocument {
        // 1. generate the ElementData to store into database
        // 2. convert a doc to GeoDocument
        // 3. save to database

        // this is safe because doc is immutable
        val elConstructions = geoDoc.constructions.map { it.entity.clone() }

        // retrieve the element data
        val elementsData = geoDoc.elements.map { el ->
            val serializer = serializers[el::class.simpleName!!] ?: throw SerializerException(
                "Serializer doesn't exist for ${el::class.simpleName}"
            )
            val geometry = serializer(geoDoc, el)
            val elData = ElementData(
                name = el.name,
                elType = el::class.simpleName!!,
                cIdx = geoDoc.getConstructionIndex(el) ?: throw ElementNotExistInDocumentException(
                    "Không thể tìm thấy cấu trúc. Phần tử không tồn tại trong tài liệu"
                ),
                elIdx = geoDoc.getIndex(el)!!,
                deps = geoDoc.dependencies(el).map { geoDoc.getIndex(it)!! },
                inferred = geoDoc.isInferred(el),
                geometry = geometry,
                transformData = el.transformData?.clone(),
                transformer = el.transformer?.javaClass?.name,
                movementPath = el.movementPath,
                usable = el.usable,
                deleted = el.deleted,
                valid = el.valid
            )

            return@map elData
        }

        val entity = GeoDocument(
            geoDoc.id,
            geoDoc.numDim,
            elConstructions,
            elementsData,
            geoDoc.renderDoc.elements.map { it.clone() },
            geoDoc.docDefaultElRenderProp.clone(),
            geoDoc.renderDoc.state.toDocRenderProp().clone(),
        )

        return entity
    }

    override suspend fun saveDocToDB(geoDoc: GeoDoc) {
        val entity = convertDocToEntity(geoDoc)
        geoDocDBS.insert(entity)
    }

    override suspend fun replaceDocToDB(geoDoc: GeoDoc) {
        val entity = convertDocToEntity(geoDoc)
        entity.updatedAt = Date()
        geoDocDBS.replace(entity)
    }

    override suspend fun newDoc(
        numDimension: Int, canvasWidth: Double, canvasHeight: Double, unit: Int
    ): GeoDoc {
        val doc = GeoDocImpl(this, numDimension, ObjectId().toHexString())
        val renderDocState = RenderDocState(doc.id, doc.numDim, unit, canvasWidth, canvasHeight)
        val render = RenderDocImpl(doc, renderDocState)

        doc.renderDoc = render
        doc.docDefaultElRenderProp = DocDefaultElRenderProp()

        docs[doc.id] = doc // cache newly created doc into memory

        saveDocToDB(doc)

        return doc
    }

    private fun convertEntityToConstruction(doc: GeoDoc, entity: ElConstruction): ConstructionImpl {
        val tpl = ctpl[entity.ctId] ?: throw ConstructionResultException("Constructor ${entity.ctId} not found")
        val conParams = entity.paramSpecs?.map { constructionParamFromSpec(doc, it, entity.ctIdx) } ?: listOf()
        return ConstructionImpl(entity, tpl, conParams)
    }

    /**
     * Find all possible extractors for a particular param specs
     * @param doc the document containing the context
     * @param spec ParamSpecs containing the values
     * @return
     */
    @Throws(
        ExtractionFailedException::class, RuntimeException::class
    )
    private fun determineExtractor(
        doc: GeoDoc, spec: ParamSpecs, ctIdx: Int
    ): Map<String, List<ParameterExtractor>> {
        val def = defMan[spec.paramDefId] ?: kotlin.run {
            logger.error(
                "Param definition with id [{}] doesn't exist", spec.paramDefId
            )
            throw ExtractionFailedException(
                "Param definition with id ${spec.paramDefId} doesn't exist."
            )
        }
        val paramTypes: Map<String, KClass<*>> = def.paramTypes
        val extractors = mutableMapOf<String, List<ParameterExtractor>>()

        for (paramName in spec.params.keys) {
            val extractorId = spec.extractorIds[paramName]
            if (extractorId != null) {
                val e = peMan[extractorId]
                extractors[paramName] = listOf(e) // use the specified param extractor instead of
                // re-determine
            } else {
                val store = spec.getParam(paramName)
                val type = paramTypes[paramName] ?: kotlin.run {
                    logger.error(
                        "spec [{}] param type [{}] not found", spec, paramName
                    )
                    throw ExtractionFailedException(
                        "Unable to find param $paramName of template ${spec.tplStrLangId}"
                    )
                }
                extractors[paramName] = peMan.findExtractors(doc, type, store, ctIdx)
                if (extractors[paramName].isNullOrEmpty()) {
                    logger.error(
                        "spec [{}] extractors for type [{}] and store [{}] not found", spec, paramName, store
                    )
                    throw ExtractionFailedException(
                        "Unable to find extractors for $paramName of template ${spec.tplStrLangId}"
                    )
                }
            }
        }

        return extractors
    }

    /**
     * Convert the param spec to a construction param This mainly means determining the param
     * extractors needed.
     */
    @Throws(RuntimeException::class)
    private fun constructionParamFromSpec(
        doc: GeoDoc, spec: ParamSpecs, ctIdx: Int
    ): ConstructionParamImpl {
        val defId = spec.paramDefId
        val def = defMan[defId] ?: throw ConstructionResultException(
            "Unknown constraint param definition with id = $defId"
        )
        val consParam = ConstructionParamImpl(spec, def)
        consParam.candidateExtractors = determineExtractor(doc, spec, ctIdx)

        return consParam
    }

    // TODO construction should be applied sequentially to the geodoc
    @Throws(InvalidElementNameException::class, ElementExistInDocumentException::class)
    override suspend fun applyConstruction(
        doc: GeoDoc, cmd: ElConstructionRequest
    ): Pair<Construction, ConstructionResult<out Element>> {
        // convert cmd from el-construction to construction
        val entity = ElConstruction(
            ctId = cmd.ctId,
            elType = cmd.elType,
            name = cmd.name,
            cgName = cmd.cgName,
            paramSpecs = cmd.paramSpecs,
        )
        val construction = convertEntityToConstruction(doc, entity)
        val outputType = construction.constructor.outputType()

        // Validate requested name if provided
        if (!cmd.name.isNullOrBlank()) {
            // Check name pattern validity
            if (!NamePattern.isNameValid(outputType, cmd.name!!)) {
                throw InvalidElementNameException(
                    "${cmd.name} is not a valid name for output type ${cmd.elType}"
                )
            }

            // Check if element with this name already exists
            if (doc.findElementByName(cmd.name, outputType, entity.ctIdx) != null) {
                throw ElementExistInDocumentException(
                    "${cmd.name} is already exist for output type ${cmd.elType}"
                )
            }
        }

        // Construct the element
        val elName = if (cmd.name.isNullOrBlank()) null else cmd.name
        val cr = construction.constructor.construct(doc, elName, construction)

        // Validate result name if provided by constructor
        val elRsName = cr.result()?.name
        if (!elRsName.isNullOrBlank()) {
            // Check name pattern validity for generated name
            if (!NamePattern.isNameValid(outputType, elRsName)) {
                throw InvalidElementNameException(
                    "Constructed element name $elRsName is not a valid name for output type ${cmd.elType}"
                )
            }

            // Check if element with this name already exists
            if (doc.findElementByName(elRsName, outputType, entity.ctIdx) != null) {
                throw ElementExistInDocumentException(
                    "$elRsName is already exist for output type ${cmd.elType}"
                )
            }
        }

        return construction to cr
    }

    override suspend fun reconstruct(
        doc: GeoDoc, cmd: ElConstructionRequest, ctIdx: Int
    ): ReconstructionResponse {
        val oldConstruction =
            doc.constructions.getOrNull(ctIdx) ?: throw ConstructionResultException("Not found construction")
        val oldEntity = oldConstruction.entity.clone()

        val entity = ElConstruction(
            ctId = cmd.ctId,
            elType = cmd.elType,
            name = oldConstruction.elName,
            cgName = cmd.cgName,
            paramSpecs = cmd.paramSpecs,
            ctIdx = ctIdx
        )

        val construction = convertEntityToConstruction(doc, entity)

        oldConstruction.mergeFrom(construction)

        val elements = reconstruct(doc, ctIdx)

        val renders = elements.map { doc.renderDoc.render(it, rerender = true) }.flatten()
        return ReconstructionResponse(renders, oldEntity, entity)
    }

    /**
     * Recursively reconstructs elements starting from a given construction index. This function
     * re-evaluates the specified construction and then iteratively re-evaluates any subsequent
     * constructions in the document that depend on the elements modified by the preceding
     * reconstructions.
     *
     * The reconstruction propagates "forward" through the construction list, meaning only
     * constructions with indices greater than the currently processed one are considered for
     * re-evaluation in the same wave.
     *
     * @param doc The geometric document to operate on.
     * @param initialConstructionIndex The index of the first construction to re-evaluate.
     * @return A list of unique elements that were updated or invalidated during the
     * ```
     *         reconstruction process.
     * ```
     */
    private fun reconstruct(doc: GeoDoc, initialConstructionIndex: Int): List<Element> {
        val allAffectedElements = mutableSetOf<Element>()
        // A queue of construction indices that need to be re-evaluated.
        // Using a SortedSet to process in ascending order of indices.
        val constructionIndicesToRevisit = sortedSetOf<Int>()
        constructionIndicesToRevisit.add(initialConstructionIndex)

        while (constructionIndicesToRevisit.isNotEmpty()) {
            val currentIndex = constructionIndicesToRevisit.first()
            constructionIndicesToRevisit.remove(currentIndex) // Process and remove from queue

            val constructionToReEvaluate =
                doc.constructions.getOrNull(currentIndex) ?: continue // Should not happen if indices are managed
            // correctly, but
            // good for safety

            val elementConstructor = constructionToReEvaluate.constructor
            val constructionResult = try {
                elementConstructor.construct(
                    doc, constructionToReEvaluate.elName, constructionToReEvaluate
                )
            } catch (ex: ConstructionException) {
                // If construction fails, create an empty result.
                // GeoDoc.reMergeConstructionResult will handle invalidating
                // the old
                // element.
                ConstructionResultImpl<Element>().apply {
                    this.ctIdx = currentIndex // Set ctIdx for consistency,
                    // though reMerge uses
                    // construction.ctIdx
                }
            }

            // Merge the new result, updating elements in the document.
            // `elementsChangedInThisStep` contains elements newly created, modified, or
            // invalidated.
            val elementsChangedInThisStep = doc.reMergeConstructionResult(constructionResult, constructionToReEvaluate)
            allAffectedElements.addAll(elementsChangedInThisStep)

            // Find all elements in the document that depend on the elements changed in
            // this step.
            val dependentElements = doc.elements.filter {
                doc.dependencies(it).any { dependency ->
                    elementsChangedInThisStep.contains(dependency)
                }
            }
            // Get their construction indices and add to the queue if they are after the
            // current
            // one.
            dependentElements.mapNotNull { doc.getConstructionIndex(it) }
                .filter { it > currentIndex } // Enforce forward propagation
                .let { constructionIndicesToRevisit.addAll(it) }
        }
        return allAffectedElements.toList()
    }

    override suspend fun moveElement(
        doc: GeoDoc, reIdx: Int, pos: DoubleArray
    ): MoveElementResponse {
        // Create a document copy to perform the operation
        val clonedDocument = cloneDoc(doc)
        val renderElement = clonedDocument.renderDoc.elements[reIdx]
        val targetElement = clonedDocument.elements.getOrNull(renderElement.elIndexes.first())

        /// Check the validity of the element to be moved
        if (targetElement !is Point) throw ConstructionException("Chỉ các điểm mới có thể được di chuyển")
        if (targetElement.transformer == null) throw ConstructionException("Không tìm thấy bộ chuyển đổi")
        if (targetElement.transformData == null) throw ConstructionException("Không tìm thấy dữ liệu chuyển đổi")

        // Get the construction information of the element
        val constructionIndex = clonedDocument.getConstructionIndex(targetElement)
            ?: throw ConstructionException("Không tìm thấy chỉ số xây dựng")
        val oldConstruction = clonedDocument.constructions.getOrNull(constructionIndex)
            ?: throw ConstructionException("Không tìm thấy thông tin xây dựng")

        // Create a new entity from the old construction information
        val newEntity = ElConstruction(
            ctId = oldConstruction.entity.ctId,
            elType = oldConstruction.entity.elType,
            name = oldConstruction.elName,
            cgName = oldConstruction.entity.cgName,
            paramSpecs = oldConstruction.entity.paramSpecs?.map { it.clone() },
            ctIdx = constructionIndex
        )

        // Create and apply the transformation to move the point
        val newConstruction = convertEntityToConstruction(clonedDocument, newEntity)
        targetElement.transformer?.apply(
            clonedDocument,
            newConstruction,
            targetElement.transformData!!,
            Vector3D.of(pos[0], pos[1], pos.getOrNull(2) ?: 0.0)
        )

        // Update the old construction information
        oldConstruction.mergeFrom(newConstruction)

        // Reconstruct and render the related elements
        val updatedElements = reconstruct(clonedDocument, constructionIndex)
        val renderedElements = updatedElements.map { clonedDocument.renderDoc.render(it, rerender = true) }.flatten()

        // Clear unused elements to avoid conflicts
        clonedDocument.clearUnusableElements()
        clonedDocument.clearUnusableConstruction()
        clonedDocument.renderDoc.clearUnusableElements()

        // Return the move result
        return MoveElementResponse(renderedElements, oldConstruction.entity, newConstruction.entity)
    }

    @Throws(RuntimeException::class)
    override suspend fun renderConstructionResult(
        result: ConstructionResult<out Element>, prop: RenderProp?
    ): List<RenderElement> {
        val el = result.result()

        el ?: throw ConstructionException("Element cannot be null.")

        val doc = el.doc

        val constIndex = doc.getConstructionIndex(el) ?: throw ElementNotExistInDocumentException(
            "Cannot find construction. Element doesn't exist inside the document"
        )

        val elByConst = doc.elementsFromConstruction(constIndex)

        return elByConst.map { doc.renderDoc.render(it, renderProp = prop?.clone()) }.flatten()
    }

    override suspend fun updateDocSize(docId: String, width: Double, height: Double) {
        val doc = retrieveDoc(docId) as GeoDocImpl? ?: throw ViException("Notfound document")

        doc.renderDoc.updateDocSize(width, height)

        geoDocDBS.updateDocSize(docId, width, height)
    }

    override suspend fun updateElsProp(
        docId: String, elIndexes: List<Int>, elementRenderProps: RenderProp
    ) {
        val doc = retrieveDoc(docId) ?: throw ViException("Not found document")

        elIndexes.map { doc.renderDoc.elements[it].renderProp }.map {
            val elRenderPropProperties = it::class.memberProperties
            elRenderPropProperties.forEach { property ->
                val sourceValue = property.getter.call(elementRenderProps) // Get source value
                if (sourceValue != null && property is KMutableProperty<*>) {
                    property.setter.call(it, sourceValue) // Set destination value
                }
            }
        }
    }

    override suspend fun renamePointElement(
        docId: String, relIndex: Int, newName: String
    ): List<RenameElementModel> {
        val doc = retrieveDoc(docId) ?: throw ViException("Notfound document")
        val rel = doc.renderDoc.elements.getOrNull(relIndex) ?: throw ViException("Notfound element")

        if (rel !is RenderVertex) throw InvalidElementNameException("Not support rename for ${rel::class.simpleName}")
        if (!NamePattern.isNameValid(Point::class, newName)) throw InvalidElementNameException("Name is not valid")
        if (doc.renderDoc.findRenderElByName(newName, RenderVertex::class)
                ?.let { it != rel } == true
        ) throw InvalidElementNameException("Name $newName was used by other element")

        val oldName = rel.name
        val regex = "$oldName(?!\\d)".toRegex()

        doc.elements.filter { it.name != null }.forEach { el ->
            el.name = el.name!!.replace(regex, newName)
        }

        doc.constructions.forEach { ct ->
            ct.elName?.let { ct.entity.name = it.replace(regex, newName) }

            ct.params.forEach { p ->
                p.specs.params.values.forEach { ps ->
                    when (ps) {
                        is ParamStoreValue -> {
                            ps.value = ps.value.replace(regex, newName)
                        }

                        is ParamStoreArray -> {
                            ps.values = ps.values.map { it.replace(regex, newName) }
                        }
                    }
                }
            }
        }

        val renamed = doc.renderDoc.elements.mapNotNull { el ->
            if (regex.findAll(el.name).count() > 0) {
                val old = el.name
                el.name = el.name.replace(regex, newName)
                RenameElementModel(el.relIndex, RelType.Vertex, old, el.name)
            } else null
        }

        return renamed
    }

    override suspend fun renameLineElement(
        docId: String, relIndex: Int, newName: String
    ): List<RenameElementModel> {
        val doc = retrieveDoc(docId) ?: throw ViException("Notfound document")
        val rel = doc.renderDoc.elements.getOrNull(relIndex) ?: throw ViException("Notfound element")

        if (rel !is RenderVertex) throw InvalidElementNameException("Not support rename for ${rel::class.simpleName}")
        if (!NamePattern.isNameValid(LineVi::class, newName)) throw InvalidElementNameException("Name is not valid")
        if (doc.renderDoc.findRenderElByName(newName, RenderLine::class)
                ?.let { it != rel } == true
        ) throw InvalidElementNameException("Name was used by other element")

        val oldName = rel.name

        doc.elements.filter { it.name != null && it.name == oldName }.forEach { el ->
            el.name = newName
        }

        doc.constructions.filter { it.elName != null && it.elName == oldName }.forEach { ct ->
            ct.entity.name = newName

            ct.params.forEach { p ->
                p.specs.params.values.forEach { ps ->
                    when (ps) {
                        is ParamStoreValue -> {
                            if (ps.value == oldName) ps.value = newName
                        }

                        is ParamStoreArray -> {
                            ps.values = ps.values.map { if (it == oldName) newName else it }
                        }
                    }
                }
            }
        }

        val renders = doc.renderDoc.elements.mapNotNull { el ->
            if (el.name == oldName) {
                val old = el.name
                el.name = newName
                RenameElementModel(el.relIndex, RelType.Vertex, old, el.name)
            } else null
        }

        return renders
    }

    override suspend fun updateDocState(
        docId: String, docRenderProp: DocRenderProp
    ): DocRenderProp? {
        val doc = retrieveDoc(docId) as GeoDocImpl? ?: throw ViException("Not found document")

        doc.renderDoc.updateDocState(docRenderProp)

        return geoDocDBS.updateDocState(docId, doc.renderDoc.state.toDocRenderProp())?.docRenderProp
    }

    override suspend fun updateDocDefaultElRenderProps(
        docId: String, docDefaultElRenderProp: DocDefaultElRenderProp
    ): DocDefaultElRenderProp? {
        val doc = retrieveDoc(docId) as GeoDocImpl? ?: throw ViException("Not found document")

        doc.renderDoc.updateDocDefaultElRenderProps(docDefaultElRenderProp)

        return geoDocDBS.updateDocDefaultElRenderProps(docId, doc.docDefaultElRenderProp)?.docDefaultElRenderProp
    }

    override suspend fun duplicateDocument(docIds: List<String>): Map<String, String> {
        val docs = docIds.mapNotNull { retrieveDoc(it) }.map { convertDocToEntity(it) }.map {
            it.id = ObjectId().toHexString()
            it
        }
        val newDocs = geoDocDBS.insertDocuments(docs)
        newDocs.mapNotNull { newDocId -> retrieveDoc(newDocId) }
        return newDocs.withIndex().map { docIds[it.index] to it.value }.associate { it }
    }

    override suspend fun deleteDocument(docId: String) {
        docs.remove(docId)
        geoDocDBS.deleteDoc(docId)
    }

    override suspend fun updateDeletedElements(
        docId: String, deleted: Boolean?, elIndexes: List<Int>, relIndexes: List<Int>
    ) {
        val doc = retrieveDoc(docId) ?: throw ViException("Not found document")
        doc.updateElsDeleted(elIndexes.toSet(), deleted)
        doc.renderDoc.updateRelsDeleted(relIndexes.toSet(), deleted)
    }

    override suspend fun updateUsableElements(
        docId: String, usable: Boolean, ctIdxes: List<Int>, elIndexes: List<Int>, relIndexes: List<Int>
    ) {
        val doc = retrieveDoc(docId) ?: throw ViException("Not found document")
        doc.updateConstructionsUsable(ctIdxes.toSet(), usable)
        doc.updateElsUsable(elIndexes.toSet(), usable)
        doc.renderDoc.updateRelsUsable(relIndexes.toSet(), usable)
    }

    override suspend fun clearUnusableElements(docId: String) {
        val doc = retrieveDoc(docId) ?: throw ViException("Not found document")
        doc.clearUnusableElements()
        doc.renderDoc.clearUnusableElements()
        doc.clearUnusableConstruction()
    }

    override suspend fun deleteRenderElements(
        docId: String, relIndexes: List<Int>
    ): Pair<List<Int>, List<Int>> {
        val doc = retrieveDoc(docId) ?: throw ViException("Not found document")
        val deletedElIdxes = sortedSetOf<Int>()
        val deletedRelIdxes = mutableSetOf<Int>()
        for (relIdx in relIndexes) {
            val rel = doc.renderDoc.elements.getOrNull(relIdx) ?: continue
            val (els, rels) = deleteRenderElement(doc, rel)
            deletedElIdxes.addAll(els)
            deletedRelIdxes.addAll(rels)
        }
        logger.info(
            "deleted elements: {}, render elements: {}",
            deletedElIdxes,
            jacksonObjectMapper().writeValueAsString(deletedRelIdxes)
        )
        return deletedElIdxes.toList() to deletedRelIdxes.toList()
    }

    private fun deleteRenderElement(doc: GeoDoc, rel: RenderElement): Pair<List<Int>, List<Int>> {
        val deps = rel.elIndexes.map { elIdx -> getElDepsIdxes(doc, elIdx, mutableListOf(elIdx)) }.flatten().toSet()

        deps.map { doc.elements.getOrNull(it)?.deleted = true }
        val deletedRelIdxes = doc.renderDoc.deleteRenderElements(rel.relIndex, deps.toList())

        return deps.toList() to deletedRelIdxes.toList()
    }

    private fun getElDepsIdxes(
        doc: GeoDoc, elIdx: Int, init: MutableList<Int> = mutableListOf(elIdx)
    ): List<Int> {
        val rootEl = doc.elements.getOrNull(elIdx) ?: return emptyList()
        for (idx in elIdx + 1 until doc.elements.size) {
            if (init.contains(idx)) continue
            val el = doc.elements.getOrNull(idx) ?: continue
            if (doc.dependencies(el).contains(rootEl)) {
                init.add(idx)
                getElDepsIdxes(doc, idx, init)
            }
        }
        return init
    }

    override fun <T : GeoDoc> cloneDoc(doc: T): T {
        val entity = convertDocToEntity(doc)
        val clonedDoc = convertEntityToDoc(entity)
        return clonedDoc as T
    }
}
