package viclass.editor.geo.impl.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Ray
import viclass.editor.geo.math.MFunc


/**
 *
 * <AUTHOR>
 */
class RayImpl(
    doc: GeoDoc, name: String?,
    private var _p1: Point,
    private var _parallelVector: Vector3D,
    private var _p2: Point? = null
) : Ray, LineImpl(doc, name, _p1, _parallelVector, _p2) {
    override val clazz = Ray::class

    override val p1: Point
        get() = _p1
    override val parallelVector: Vector3D
        get() = _parallelVector
    override val p2: Point?
        get() = _p2

    override fun root(): Point {
        return p1
    }

    override fun end(): Point? {
        return p2
    }

    override fun parallelVector(): Vector3D {
        return parallelVector
    }

    override fun vertices(): List<Point> {
        return listOfNotNull(p1, p2)
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Ray::class, name)) return false

        return this.name == name
    }

    override fun parametricFunc(dim: Dimension): MFunc {
        TODO("Not yet implemented")
    }

    override fun mergeFrom(other: Element) {
        if (other !is Ray) return
        super<LineImpl>.mergeFrom(other)

        _parallelVector = other.parallelVector
    }
}
