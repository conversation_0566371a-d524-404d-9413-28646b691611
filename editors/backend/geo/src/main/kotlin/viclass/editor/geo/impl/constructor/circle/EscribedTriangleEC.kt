package viclass.editor.geo.impl.constructor.circle

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementExtraction
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Triangle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.CircleImpl
import kotlin.reflect.KClass

@Singleton
class EscribedTriangleEC : ElementConstructor<Circle> {

    override fun outputType(): KClass<Circle> {
        return Circle::class
    }

    private enum class CGS {
        EscribedTriangle
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.EscribedTriangle.name)
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[ConstraintParamDefManager.aTriangle]!!,
                        listOf("NameOfTriangle"),
                        "tpl-EscribedOf"
                    )
                    .constraint(
                        1,
                        ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-OppositeOf"
                    )
                    .build(),
            )
            .elTypes(Circle::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<Circle> {
        when (CGS.valueOf(c.cgName)) {
            CGS.EscribedTriangle -> {
                Validations.validateNumConstraints(c, 1)

                val extraction = extractFirstPossible<ElementExtraction<Triangle>>(
                    doc, ParamKind.PK_Name, c.params[0], c.ctIdx
                )
                val triangle = extraction.result.result()
                    ?: throw ElementNotExistInDocumentException("not found triangle")

                val extraction2 = extractFirstPossible<ElementExtraction<Point>>(
                    doc, ParamKind.PK_Name, c.params[1], c.ctIdx
                )
                val point = extraction2.result.result()
                    ?: throw ElementNotExistInDocumentException("not found point")

                // center name
                var centerName: String? = null
                if (inputName != null) {
                    NamePattern[Circle::class]!![0].find(inputName)?.let {
                        centerName = it.groupValues[1]
                    }
                } else {
                    centerName = generateCircleName(doc)
                }

                val points = triangle.vertices()
                val center = Triangles.findInscribedCenter(doc, centerName, triangle, point)

                // for of points
                var pPre: Point? = null
                var pNex: Point? = null
                for (i in 0..2) {
                    if (points[i].name == point.name) {
                        pPre = points[(i + 2) % 3]
                        pNex = points[(i + 1) % 3]
                        break
                    }
                }

                if (pPre == null || pNex == null) {
                    throw ConstructionException("Point is not in the triangle")
                }

                val radius = Lines.distancePointToSegment(center, pPre, pNex)

                val cr = ConstructionResultImpl<Circle>()
                cr.setResult(CircleImpl(doc, inputName ?: centerName, center, radius))
                cr.addDependency(triangle, listOf(), true)
                cr.addDependency(point, listOf(), true)
                cr.addDependency(center, listOf(triangle, point), true)

                return cr
            }
        }
    }
}
