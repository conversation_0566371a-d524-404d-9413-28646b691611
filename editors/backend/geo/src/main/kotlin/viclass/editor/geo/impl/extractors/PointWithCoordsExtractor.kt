package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.reflect.KClass

@Singleton
class PointWithCoordsExtractor(private val evaluator: Evaluator) : ParameterExtractor {
    override var id: String = javaClass.simpleName

    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(
        doc: GeoDoc, pk: String, ctIdx: Int, clazz: KClass<out T>?
    ): ConstructionResult<T> {
        val extractedName = evaluator.extractElementName(pk, listOf("Hinh", "Diem"))
        val result = ConstructionResultImpl<Point>()

        var p = extractPointsFromStr(doc, extractedName!!)
        if (p == null) return result as ConstructionResult<T>

        p = doc.findElementByName(p.name, Point::class, ctIdx) ?: p
        result.setResult(p)

        return result as ConstructionResult<T>
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(Point::class)
    }

    override fun isSupport(doc: GeoDoc, type: KClass<*>, store: ParamStore?): Boolean {
        return super.isSupport(doc, type, store) && isPointWithCoordsValue(doc, store)
    }

    companion object {
        fun formatPoint(name: String?, x: Double, y: Double, z: Double): String {
            return "${name ?: ""},$x,$y,$z"
        }

        fun isPointWithCoordsValue(doc: GeoDoc, store: ParamStore?): Boolean {
            return extractPointsFromStore(doc, store).isNotEmpty()
        }

        fun extractPointsFromStore(doc: GeoDoc, store: ParamStore?): List<Point> {
            if (store is ParamStoreArray) return store.values.mapNotNull { value -> extractPointsFromStr(doc, value) }
            else if (store is ParamStoreValue) return listOfNotNull(extractPointsFromStr(doc, store.value))
            return emptyList()
        }

        fun extractPointsFromStr(doc: GeoDoc, str: String): Point? {
            val parts = str.split(",")
            if (parts.size == 4) {
                val name = parts[0].trim()
                val x = parts[1].trim().toDoubleOrNull() ?: return null
                val y = parts[2].trim().toDoubleOrNull() ?: return null
                val z = parts[3].trim().toDoubleOrNull() ?: return null
                val newP = PointImpl(doc, name, x, y, z)
                newP.usable = false
                return newP
            }
            return null
        }
    }
}