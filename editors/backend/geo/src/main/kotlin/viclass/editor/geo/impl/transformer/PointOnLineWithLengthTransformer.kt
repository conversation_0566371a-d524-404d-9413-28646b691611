package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.geometry.euclidean.threed.line.Lines3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithLengthTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.transformer.Transformer

/**
 * calculate length, nth: (AB = length)
 */
class PointOnLineWithLengthTransformer constructor(): Transformer<PointOnLineWithLengthTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: PointOnLineWithLengthTransformData, pos: Vector3D) {
        val root = Vector3D.of(transformData.root)
        val unitVector = Vector3D.of(transformData.unitVector)
        val line = Lines3D.fromPointAndDirection(root, unitVector, DEFAULT_PRECISION)
        val line2 = Lines3D.fromPointAndDirection(pos, unitVector.rotate90Degrees(), DEFAULT_PRECISION)
        val i = line.intersection(line2)
        val newVector = root.vectorTo(i)
        val coefficient = newVector.x / unitVector.x

        val length = newVector.norm()
        val nth = if (coefficient <= 0) 2 else 1

        val cpLength = c.params[transformData.lengthParamIdx]
        val cpnth = c.params[transformData.nthParamIdx]

        if (transformData.lengthParamKind == ParamKind.PK_Value) {
            val lengthParam = cpLength.specs.getParam(ParamKind.PK_Value) as ParamStoreValue
            lengthParam.value = length.toString()
        } else if(transformData.lengthParamKind == ParamKind.PK_Expr) {
            val lengthParam = cpLength.specs.getParam(ParamKind.PK_Expr) as ParamStoreValue
            lengthParam.value = length.toString()
        }

        val nthParam = cpnth.specs.getParam(ParamKind.PK_Value) as ParamStoreValue
        nthParam.value = nth.toString()
    }
}