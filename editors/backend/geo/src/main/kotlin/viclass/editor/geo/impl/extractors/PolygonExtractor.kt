package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.exceptions.ExtractionFailedException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.elements.PolygonImpl
import kotlin.reflect.KClass

/**
 * A Polygon extractor extract Polygons from given names. The name should have follow conventional format
 *
 * <AUTHOR>
 */
@Singleton
class PolygonExtractor constructor(private val evaluator: Evaluator) : ParameterExtractor {

    override var id: String = javaClass.simpleName

    @Throws(ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(
        doc: GeoDoc,
        pk: String,
        ctIdx: Int,
        clazz: KClass<out T>?
    ): ConstructionResult<T> {
        // check name and points
        val shapeName = evaluator.extractRootOperator(pk) { extractElementName(it, listOf("Hinh")) }
            ?: throw ElementNotExistInDocumentException("Invalid expression: $pk")
        val names = NamePattern.extractPointName(Polygon::class, shapeName)
        if (names.size < 3) {
            throw ElementNotExistInDocumentException("No Polygon with name $shapeName could be found or constructed")
        }
        val points = names.map {
            val p = doc.findElementByName(it, Point::class, ctIdx)
                ?: throw ElementNotExistInDocumentException("No Point with name $it could be found or constructed")
            p
        }

        doc.findElementByName(shapeName, Polygon::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Polygon>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        doc.findElementByName(shapeName, Triangle::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Triangle>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        doc.findElementByName(shapeName, Square::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Square>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        doc.findElementByName(shapeName, Parallelogram::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Parallelogram>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        doc.findElementByName(shapeName, Rectangle::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Rectangle>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }
        doc.findElementByName(shapeName, Quadrilateral::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Quadrilateral>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        val polygon: Polygon = PolygonImpl(doc, shapeName, points)
        val cr = ConstructionResultImpl<Polygon>()
        cr.setResult(polygon)
        cr.newly = true
        return cr as ConstructionResult<T>
    }

    @Throws(ExtractionFailedException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : ExtractionResult<out Any>> extract(
        doc: GeoDoc,
        store: ParamStore,
        ctIdx: Int,
        clazz: KClass<out Element>?
    ): T {
        return when (store) {
            is ParamStoreValue -> ElementExtraction(extractConstructionResult(doc, store.value, ctIdx, clazz)) as T
            is ParamStoreArray -> {
                val crList = store.values.map {
                    extractConstructionResult(doc, it, ctIdx, clazz)
                }
                ElementListExtraction(crList) as T
            }

            else -> throw ExtractionFailedException("Param store type not supported")
        }
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(
            Polygon::class,
            Quadrilateral::class,
            Square::class,
            Triangle::class,
            Parallelogram::class,
            Rectangle::class,
        )
    }
}
