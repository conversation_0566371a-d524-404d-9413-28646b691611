package viclass.editor.geo.impl.constructor.triangle

import common.libs.logger.Logging
import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithLengthTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aName
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.lengthAssignment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.nameWithValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.IsoscelesTriangle
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Triangle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.InvalidElementNameException
import viclass.editor.geo.extractable.NameForLength
import viclass.editor.geo.extractable.ValueExpression
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.*
import viclass.editor.geo.impl.transformer.PointOnLineWithLengthTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.math.*
import kotlin.reflect.KClass

/**
 * <AUTHOR>
 */
@Singleton
class IsoscelesTriangleEC : ElementConstructor<IsoscelesTriangle>, Logging {

    private enum class CGS {
        BaseLineSegmentAndApexAngle, BaseLineSegmentAndHeight, BaseLineSegmentAndHeightValue, BaseLineSegmentAndBaseAngle,
        SideLineSegmentApexAngle, SideLineSegmentHeight, SideLineSegmentBaseAngle, FromPoints
    }

    override fun outputType(): KClass<IsoscelesTriangle> {
        return IsoscelesTriangle::class
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromPoints.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("Points"),
                        "tpl-Points"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aName]!!,
                        listOf("Point"),
                        "tpl-AtPoint"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.BaseLineSegmentAndHeight.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-BaseSideIs"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf("Height", "Expression"),
                        "tpl-Height"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.BaseLineSegmentAndHeightValue.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-BaseSideIs"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value"),
                        "tpl-HeightValue"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.BaseLineSegmentAndBaseAngle.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-BaseSideIs"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Degree"),
                        "tpl-BaseAngleIs"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.BaseLineSegmentAndApexAngle.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-BaseSideIs"
                    )
                    .constraintOptional(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Degree"),
                        "tpl-ApexAngleIs"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.SideLineSegmentHeight.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-SideIs"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf("Height", "Expression"),
                        "tpl-Height"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.SideLineSegmentBaseAngle.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-SideIs"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[nameWithValue]!!,
                        listOf("NameOfPoint", "Degree"),
                        "tpl-BaseAngle"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.SideLineSegmentApexAngle.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-SideIs"
                    )
                    .constraintOptional(
                        1, ConstraintParamDefManager.instance()[nameWithValue]!!,
                        listOf(0),
                        listOf("NameOfPoint", "Degree"),
                        "tpl-ApexAngle"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
            )
            .elTypes(IsoscelesTriangle::class)
            .build()
    }

    val nn: (GeoDoc, ArrayList<String>) -> String = { d, l -> generatePointName(d, l) }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<IsoscelesTriangle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 2)
                constructFromPoints(doc, inputName, c)
            }

            CGS.BaseLineSegmentAndHeight, CGS.BaseLineSegmentAndHeightValue -> {
                Validations.validateNumConstraints(c, 2)
                constructFromBaseLineSegmentAndHeight(doc, inputName, c)
            }

            CGS.BaseLineSegmentAndBaseAngle -> {
                Validations.validateNumConstraints(c, 2)
                constructFromBaseLineSegmentAndBaseAngle(doc, inputName, c)
            }

            CGS.BaseLineSegmentAndApexAngle -> {
                Validations.validateNumConstraints(c, 1)
                constructFromBaseLineSegmentAndApexAngle(doc, inputName, c)
            }

            CGS.SideLineSegmentHeight -> {
                Validations.validateNumConstraints(c, 2)
                constructFromSideLineSegmentAndHeight(doc, inputName, c)
            }

            CGS.SideLineSegmentBaseAngle -> {
                Validations.validateNumConstraints(c, 2)
                constructFromSideLineSegmentAndBaseAngle(doc, inputName, c)
            }

            CGS.SideLineSegmentApexAngle -> {
                Validations.validateNumConstraints(c, 1)
                constructFromSideLineSegmentAndApexAngle(doc, inputName, c)
            }
        }
    }

    private fun constructFromPoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<IsoscelesTriangle> {
        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx)
        val apexName = extractFirstPossible<StringExtraction>(doc, PK_Name, c.params[1], c.ctIdx).result
        inputName?.let {
            val pointNames = NamePattern.extractPointName(Triangle::class, it).sortedBy { it }
            if (ext.result.map { it.result()?.name }.sortedBy { it } != pointNames)
                throw InvalidElementNameException("input name is not match with input points")
        }
        val points = ext.result.map { it.result()!! }
        val apexPoint =
            points.firstOrNull { it.name == apexName } ?: throw ConstructionException("apex point not found")
        val basePoint = points.filter { it.name != apexName }
        val triangle = IsoscelesTriangleImpl(
            doc,
            inputName ?: points.joinToString("") { it.name.toString() },
            apexPoint,
            basePoint[0],
            basePoint[1]
        )
        val cr = ConstructionResultImpl<IsoscelesTriangle>()
        cr.addDependencies(points, true)
        cr.setResult(triangle)
        return cr
    }

    private fun constructFromSideLineSegmentAndApexAngle(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<IsoscelesTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var apexAngle: Double? = null
        var apexVertexName: String? = null
        val baseVertex: Point?
        val apexVertex: Point?
        val pName: String
        var thResult: Int? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                }

                nameWithValue -> {
                    apexVertexName = extractFirstPossible<StringExtraction>(doc, ParamKind.PK_Name, p, c.ctIdx).result

                    apexAngle =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx).result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                }
            }
        }

        if (apexAngle == null || apexAngle <= 0 || 180.0 <= apexAngle)
            throw ConstructionException("Apex angle $apexAngle is not valid")

        val line = lineSegmentExtractionResult!!.result()!!
        val p2 = line.p1
        val p3 = line.p2

        if (apexVertexName == p2.name!!) {
            baseVertex = p3
            apexVertex = p2
        } else {
            baseVertex = p2
            apexVertex = p3
        }

        if (inputName != null) {
            val names = NamePattern.extractPointName(Triangle::class, inputName)
            pName = (names - line.vertices().map { it.name!! }).first()
        } else {
            pName = generatePointName(doc, *line.vertices().map { it.name!! }.toTypedArray())
        }

        val name = inputName ?: "$pName${line.name}"

        val sideLength = line.length()
        val baseLength = 2 * sideLength * sin(radian(apexAngle / 2))

        val c1 = CircleImpl(doc, null, apexVertex, sideLength)
        val c2 = CircleImpl(doc, null, baseVertex, baseLength)

        val baseVertexes = Intersections.of(c1, c2)
            ?: throw ConstructionException("Cannot calculate missing base vertex")

        val v = if (baseVertexes.size == 1) baseVertexes[0]
        else if (thResult == null) baseVertexes[0]
        else Orders.pointsOnCircle(c1, c2.centerPoint.coordinates(), baseVertexes[0], baseVertexes[1])[thResult!!]

        val p1 = PointImpl(doc, pName, v)

        val cr = ConstructionResultImpl<IsoscelesTriangle>()
        cr.setResult(IsoscelesTriangleImpl(doc, name, p1, p2, p3))
        if (!lineSegmentExtractionResult.newly) cr.mergeAsDependency(lineSegmentExtractionResult)
        else {
            cr.addDependency(p2, listOf(), true)
            cr.addDependency(p3, listOf(), true)
        }
        cr.addDependency(p1, listOf(), true)

        return cr
    }

    private fun constructFromSideLineSegmentAndBaseAngle(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<IsoscelesTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var baseAngle: Double? = null
        var baseVertexName: String? = null
        val baseVertex: Point?
        val apexVertex: Point?
        val pName: String
        var thResult: Int? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                }

                nameWithValue -> {
                    baseVertexName = extractFirstPossible<StringExtraction>(doc, ParamKind.PK_Name, p, c.ctIdx).result

                    baseAngle =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx).result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                }
            }
        }

        if (baseAngle == null || baseAngle <= 0 || 90.0 <= baseAngle)
            throw ConstructionException("Base angle $baseAngle is not valid")

        val line = lineSegmentExtractionResult!!.result()!!
        val p2 = line.p1
        val p3 = line.p2

        if (baseVertexName == p2.name!!) {
            baseVertex = p2
            apexVertex = p3
        } else {
            baseVertex = p3
            apexVertex = p2
        }

        if (inputName != null) {
            val names = NamePattern.extractPointName(Triangle::class, inputName)
            pName = (names - line.vertices().map { it.name!! }).first()
        } else {
            pName = generatePointName(doc, *line.vertices().map { it.name!! }.toTypedArray())
        }

        val name = inputName ?: "$pName${line.name}"

        val sideLength = line.length()
        val baseLength = 2 * sideLength * cos(radian(baseAngle))

        val c1 = CircleImpl(doc, null, apexVertex, sideLength)
        val c2 = CircleImpl(doc, null, baseVertex, baseLength)

        val baseVertexes = Intersections.of(c1, c2)
            ?: throw ConstructionException("Cannot calculate missing base vertex")

        val v = if (baseVertexes.size == 1) baseVertexes[0]
        else if (thResult == null) baseVertexes[0]
        else Orders.pointsOnCircle(c1, c2.centerPoint.coordinates(), baseVertexes[0], baseVertexes[1])[thResult]

        val p1 = PointImpl(doc, pName, v)

        val cr = ConstructionResultImpl<IsoscelesTriangle>()
        cr.setResult(IsoscelesTriangleImpl(doc, name, p1, p2, p3))
        if (!lineSegmentExtractionResult.newly) cr.mergeAsDependency(lineSegmentExtractionResult)
        else {
            cr.addDependency(p2, listOf(), true)
            cr.addDependency(p3, listOf(), true)
        }
        cr.addDependency(p1, listOf(), true)

        return cr
    }

    private fun constructFromSideLineSegmentAndHeight(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<IsoscelesTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var heightName: NameForLength? = null
        var heightLength: Double? = null
        val apexVertex: Point?
        val baseVertex: Point?
        val pName: String
        var thResult: Int? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                }

                lengthAssignment -> {
                    val le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc,
                        ParamKind.PK_Expr,
                        p,
                        c.ctIdx
                    )
                    val nl = extractFirstPossible<ExtractableWithConstructions<NameForLength>>(
                        doc,
                        ParamKind.PK_Name,
                        p,
                        c.ctIdx
                    )
                    heightLength = le.result.value
                    heightName = nl.result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                }
            }
        }

        val line = lineSegmentExtractionResult!!.result()!!
        val p2 = line.p1
        val p3 = line.p2

        val sideLength = line.length()

        if (sideLength <= heightLength!!) throw ConstructionException("The height is invalid")

        if (p2.name == heightName?.reference?.name) {
            apexVertex = p2
            baseVertex = p3
        } else {
            apexVertex = p3
            baseVertex = p2
        }

        if (inputName != null) {
            val vertexNames = NamePattern.extractPointName(Triangle::class, inputName)
            pName = vertexNames.subtract(line.vertices().map { it.name!! }.toSet()).first()

        } else {
            val includedName = ArrayList<String>()
            heightName?.unknown?.let { includedName.add(it) }
            includedName.addAll(line.vertices().map { it.name!! })
            pName = generatePointName(doc, includedName)
        }

        val name: String = inputName ?: "${pName}${line.name}"

        val baseLength = 2 * sqrt(sideLength.pow(2) - heightLength.pow(2))

        val c1 = CircleImpl(doc, null, apexVertex, sideLength)
        val c2 = CircleImpl(doc, null, baseVertex, baseLength)

        val baseVertexes = Intersections.of(c1, c2)
            ?: throw ConstructionException("Cannot calculate missing base vertex")

        val v = if (baseVertexes.size == 1) baseVertexes[0]
        else if (thResult == null) baseVertexes[0]
        else Orders.pointsOnCircle(c1, c2.centerPoint.coordinates(), baseVertexes[0], baseVertexes[1])[thResult!!]

        val p1 = PointImpl(doc, pName, v)
        val pH = Points.calculateCenterPoint(doc, heightName!!.unknown, baseVertex, p1)
        val height = LineSegmentImpl(doc, heightName.name, apexVertex, pH)

        val cr = ConstructionResultImpl<IsoscelesTriangle>()
        cr.setResult(IsoscelesTriangleImpl(doc, name, p1, p2, p3))
        if (!lineSegmentExtractionResult.newly) cr.mergeAsDependency(lineSegmentExtractionResult)
        else {
            cr.addDependency(p2, listOf(), true)
            cr.addDependency(p3, listOf(), true)
        }
        cr.addDependency(p1, listOf(), true)
        cr.addDependency(pH, listOf(), true)
        cr.addDependency(height, listOf(), true)

        return cr
    }

    private fun constructFromBaseLineSegmentAndHeight(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<IsoscelesTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var heightName: NameForLength? = null
        var heightLength: Double? = null
        var thResult: Int? = null
        var line: LineSegment? = null
        var p2: Point? = null
        var p3: Point? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                    line = lineSegmentExtractionResult.result()!!
                    val lineNameSubmit = line.name!!
                    val pointsName = NamePattern.extractPointName(LineSegment::class, lineNameSubmit)
                    val points = pointsName.map { n -> doc.findElementByName(n, Point::class, c.ctIdx) }
                    p2 = points[0]!!
                    p3 = points[1]!!
                }

                lengthAssignment -> {
                    val le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc,
                        ParamKind.PK_Expr,
                        p,
                        c.ctIdx
                    )
                    val nl = extractFirstPossible<ExtractableWithConstructions<NameForLength>>(
                        doc,
                        ParamKind.PK_Name,
                        p,
                        c.ctIdx
                    )
                    heightLength = le.result.value
                    heightName = nl.result
                }

                aValue -> if (p.specs.indexInCG == 1) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    heightLength = extractedResult.result
                } else if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                }
            }
        }

        val sideVertexNames = line!!.vertices().map { it.name!! }

        val apexName: String

        if (inputName != null) {
            val vertexNames = NamePattern.extractPointName(Triangle::class, inputName)
            apexName = vertexNames.subtract(sideVertexNames.toSet()).first()

        } else {
            val excludedName = ArrayList<String>()
            heightName?.unknown?.let { excludedName.add(it) }
            excludedName.addAll(sideVertexNames)
            apexName = generatePointName(doc, excludedName)
        }

        val name: String = inputName ?: "${apexName}${line.name}"

        val names = NamePattern.extractPointName(Triangle::class, name)
        if (!names.containsAll(sideVertexNames))
            throw ConstructionException("Name of triangle not match")

        val v = findApexVertexWithBaseVertexAndHeight(doc, p2!!, p3!!, heightLength!!, thResult)

        val apexVertex = PointImpl(doc, apexName, v)
        if (heightName == null) {
            val root = Points.calculateCenterPoint(p2, p3).toArray()
            val unitVector = line.parallelVector.rotate90Degrees().toArray()
            apexVertex.transformer = TransformMapping.fromClazz(PointOnLineWithLengthTransformer::class)
            apexVertex.transformData = PointOnLineWithLengthTransformData(
                lengthParamIdx = 1,
                lengthParamKind = ParamKind.PK_Value,
                nthParamIdx = 2,
                root = root,
                unitVector = line.parallelVector.rotate90Degrees().toArray()
            )
            apexVertex.movementPath = MovementLinePath(root, unitVector)
        }

        val cr = ConstructionResultImpl<IsoscelesTriangle>()
        cr.setResult(IsoscelesTriangleImpl(doc, name, apexVertex, p2, p3))
        if (!lineSegmentExtractionResult!!.newly) cr.mergeAsDependency(lineSegmentExtractionResult)
        else {
            cr.addDependency(p2, listOf(), true)
            cr.addDependency(p3, listOf(), true)
        }
        cr.addDependency(apexVertex, listOf(), true)

        if (heightName?.unknown != null) {
            val pH = Points.calculateCenterPoint(doc, heightName.unknown, p2, p3)
            val height = LineSegmentImpl(doc, heightName.name, apexVertex, pH)
            cr.addDependency(pH, listOf(), true)
            cr.addDependency(height, listOf(), true)
        }

        return cr

    }

    private fun constructFromBaseLineSegmentAndApexAngle(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<IsoscelesTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var apexAngle: Double? = null
        var thResult: Int? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                } else {
                    val extraction = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    apexAngle = extraction.result
                }
            }
        }

        if (apexAngle == null || apexAngle <= 0 || 180.0 <= apexAngle)
            throw ConstructionException("Apex angle $apexAngle is not valid")

        val line = lineSegmentExtractionResult!!.result()!!
        val p2 = line.p1
        val p3 = line.p2

        val sideVertexNames = line.vertices().map { it.name!! }

        val l = Distances.of(p2, p3)
        val distance = (l / 2) * tan(radian((180 - apexAngle) / 2))

        val apexName: String

        if (inputName != null) {
            val vertexNames = NamePattern.extractPointName(Triangle::class, inputName)
            apexName = vertexNames.subtract(sideVertexNames.toSet()).first()

        } else {
            val excludedName = ArrayList<String>()
            excludedName.addAll(sideVertexNames)
            apexName = generatePointName(doc, excludedName)
        }

        val name: String = inputName ?: "${apexName}${line.name}"

        val names = NamePattern.extractPointName(Triangle::class, name)
        if (!names.containsAll(sideVertexNames))
            throw ConstructionException("Name of triangle not match")

        val v = findApexVertexWithBaseVertexAndHeight(doc, p2, p3, distance)

        val apexVertex = PointImpl(doc, apexName, v)

        val cr = ConstructionResultImpl<IsoscelesTriangle>()
        cr.setResult(IsoscelesTriangleImpl(doc, name, apexVertex, p2, p3))
        if (!lineSegmentExtractionResult.newly) cr.mergeAsDependency(lineSegmentExtractionResult)
        else {
            cr.addDependency(p2, listOf(), true)
            cr.addDependency(p3, listOf(), true)
        }
        cr.addDependency(apexVertex, listOf(), true)

        return cr
    }

    private fun constructFromBaseLineSegmentAndBaseAngle(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<IsoscelesTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var baseAngle: Double? = null
        var thResult: Int? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                } else {
                    val extraction = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    baseAngle = extraction.result
                }
            }
        }

        if (baseAngle == null || baseAngle <= 0 || 90.0 <= baseAngle)
            throw ConstructionException("Base angle $baseAngle is not valid")

        val line = lineSegmentExtractionResult!!.result()!!
        val p2 = line.p1
        val p3 = line.p2

        val sideVertexNames = line.vertices().map { it.name!! }

        val l = Distances.of(p2, p3)
        val distance = (l / 2) * tan(radian(baseAngle))

        val apexName: String

        if (inputName != null) {
            val vertexNames = NamePattern.extractPointName(Triangle::class, inputName)
            apexName = vertexNames.subtract(sideVertexNames.toSet()).first()

        } else {
            val excludedName = ArrayList<String>()
            excludedName.addAll(sideVertexNames)
            apexName = generatePointName(doc, excludedName)
        }

        val name: String = inputName ?: "${apexName}${line.name}"

        val names = NamePattern.extractPointName(Triangle::class, name)
        if (!names.containsAll(sideVertexNames))
            throw ConstructionException("Name of triangle not match")

        val v = findApexVertexWithBaseVertexAndHeight(doc, p2, p3, distance)

        val apexVertex = PointImpl(doc, apexName, v)

        val cr = ConstructionResultImpl<IsoscelesTriangle>()
        cr.setResult(IsoscelesTriangleImpl(doc, name, apexVertex, p2, p3))
        if (!lineSegmentExtractionResult.newly) cr.mergeAsDependency(lineSegmentExtractionResult)
        else {
            cr.addDependency(p2, listOf(), true)
            cr.addDependency(p3, listOf(), true)
        }
        cr.addDependency(apexVertex, listOf(), true)

        return cr
    }

    private fun findApexVertexWithBaseVertexAndHeight(
        doc: GeoDoc,
        p2: Point,
        p3: Point,
        h: Double,
        thResult: Int? = null
    ): Vector3D {
        val midP = Points.calculateCenterPoint(doc, null, p2, p3)
        val points = RightTriangles.computeRightTriangleAdjacentVertices(midP, p2, h)
        val v = if (points.size == 1) points[0]
        else if (thResult == null) points[0]
        else Orders.pointByParallelVector(
            createVectorByEl(doc, p2, p3),
            p2.coordinates(),
            points[0],
            points[1]
        )[thResult]
        return v
    }
}
