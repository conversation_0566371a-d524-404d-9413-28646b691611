package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Angle
import viclass.editor.geo.elements.Element
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import kotlin.reflect.KClass

/**
 * A Angle extractor extract Angles from given names. The name should have follow conventional format
 *
 * <AUTHOR>
 */
@Singleton
class AngleExtractor constructor(private val evaluator: Evaluator) : ParameterExtractor {

    override var id: String = javaClass.simpleName

    @Throws(ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(doc: GeoDoc, pk: String, ctIdx: Int, clazz: KClass<out T>?): ConstructionResult<T> {
        val shapeName = evaluator.extractRootOperator(pk) { extractElementName(it, listOf("Hinh", "Goc")) }
            ?: throw ElementNotExistInDocumentException("Invalid expression: $pk")

        doc.findElementByName(shapeName, Angle::class, ctIdx) ?.let {
            val cr = ConstructionResultImpl<Angle>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        throw ElementNotExistInDocumentException("No Angle with name $shapeName could be found or constructed")
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(Angle::class)
    }
}
