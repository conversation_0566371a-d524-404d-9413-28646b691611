package viclass.editor.geo.impl.cache

import viclass.editor.geo.impl.db.TextMappingDS
import java.util.concurrent.ConcurrentHashMap


/**
 *
 * <AUTHOR>
 */
class MappingTextCache constructor(
    private val dbGateways: TextMappingDS
) {
    private val mapping = ConcurrentHashMap<String, ConcurrentHashMap<String, String>>()

    suspend fun reload() {
        val mapping = this.mapping

        dbGateways.fetch().apply { mapping.clear() }.forEach { m ->
            m.text.forEach { put(it.key, m.id, it.value) }
        }
    }

    fun put(lang: String, id: String, text: String) {
        val l = mapping.getOrPut(lang) { ConcurrentHashMap() }
        l[id] = text
    }

    fun get(lang: String): Map<String, String> {
        return mapping.getOrDefault(lang, mapOf())
    }
}
