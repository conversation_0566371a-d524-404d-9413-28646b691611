package viclass.editor.geo.impl.koin

import com.google.common.reflect.ClassPath
import com.mongodb.MongoClientSettings
import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoDatabase
import com.typesafe.config.ConfigBeanFactory
import com.typesafe.config.ConfigFactory
import com.typesafe.config.ConfigParseOptions
import common.libs.codec.DoubleArrayCodec
import common.libs.codec.EnumCodecProvider
import common.libs.codec.TwoIntArrayCodec
import org.bson.codecs.CollectionCodecProvider
import org.bson.codecs.IterableCodecProvider
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.pojo.PojoCodecProvider
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import org.koin.core.component.KoinComponent
import viclass.editor.geo.dbentity.GeoDocument
import viclass.editor.geo.impl.db.DBConfig
import viclass.editor.geo.impl.db.GeoDocumentDBS
import viclass.editor.geo.impl.db.GeoDocumentDBSImpl
import viclass.editor.geo.impl.db.TextMappingDS
import viclass.editor.geo.impl.pojo.TextMapping
import viclass.editor.geo.server.logger
import java.util.stream.Collectors


@Module
class DatabaseModule: KoinComponent {
    private val config: DBConfig

    constructor(config: DBConfig) {
        this.config = config
    }

    /**
     * Using default config from classpath
     */
    constructor() {
        val conf = ConfigFactory.parseResources(
            "application.conf",
            ConfigParseOptions.defaults().setAllowMissing(false)
        )
        config = ConfigBeanFactory.create(conf.getConfig("db"), DBConfig::class.java)
    }

    @Singleton
    fun provideDBConfig(): DBConfig {
        return config
    }

    @Singleton
    fun provideMongoDatabase(config: DBConfig): MongoDatabase {
        // register codec
        val pojoCodecRegistry = CodecRegistries.fromRegistries(
            MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(
                    EnumCodecProvider(),
                    CollectionCodecProvider(),
                    IterableCodecProvider(),
                    PojoCodecProvider.builder()
                        .automatic(true)
                        .register(
                            "viclass.editor.geo.dbentity",
                            "viclass.editor.geo.dbentity.transformdata",
                            "viclass.editor.geo.dbentity.movement.path",
                            "viclass.editor.geo.dbentity.elementdata",
                            "viclass.editor.geo.dbentity.paramstore",
                            "viclass.editor.geo.dbentity.renderdata",
                        )
                        .register(*loadAllBsonDiscriminatorClasses(listOf(
                            "viclass.editor.geo.dbentity",
                            "viclass.editor.geo.dbentity.transformdata",
                            "viclass.editor.geo.dbentity.movement.path",
                            "viclass.editor.geo.dbentity.elementdata",
                            "viclass.editor.geo.dbentity.paramstore",
                            "viclass.editor.geo.dbentity.renderdata",
                        )).toTypedArray())
                        .build()
                ),
                CodecRegistries.fromCodecs(DoubleArrayCodec()),
                CodecRegistries.fromCodecs(TwoIntArrayCodec()),
        )

        val client = MongoClients.create(config.connection)

        // get database
        return client.getDatabase(config.dbName).withCodecRegistry(pojoCodecRegistry)
    }

    @Throws(Throwable::class)
    private fun loadAllBsonDiscriminatorClasses(packages: List<String>): List<Class<*>> {
        return try {
            ClassPath.from(javaClass.classLoader)
                .allClasses.stream()
                .filter { c: ClassPath.ClassInfo -> packages.contains(c.packageName) }
                .map { c: ClassPath.ClassInfo -> c.load() }
                .filter {  it.isAnnotationPresent(BsonDiscriminator::class.java) }
                .collect(Collectors.toList())
        } catch (t: Throwable) {
            logger.error("Load BsonDiscriminator exception... ", t)
            throw t
        }
    }


    @Singleton
    fun provideGeoDocumentGateway(db : MongoDatabase) : GeoDocumentDBS {
        val col = db.getCollection("geo-documents", GeoDocument::class.java)
        return GeoDocumentDBSImpl(col)
    }

    @Singleton
    fun provideTextMappingDS(db: MongoDatabase): TextMappingDS {
        val col = db.getCollection("mapping.text", TextMapping::class.java)
        return TextMappingDS(col)
    }
}
