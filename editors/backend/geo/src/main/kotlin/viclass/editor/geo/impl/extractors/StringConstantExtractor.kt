package viclass.editor.geo.impl.extractors

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.exceptions.ExtractionFailedException
import viclass.editor.geo.server.logger
import kotlin.reflect.KClass

@Singleton
class StringConstantExtractor() : ParameterExtractor {
    override var id: String = javaClass.simpleName

    @Throws(ExtractionFailedException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : ExtractionResult<out Any>> extract(doc: GeoDoc, store: ParamStore, ctIdx: Int, clazz: KClass<out Element>?): T {
        try {
            if (store is ParamStoreValue) {
                return StringExtraction(store.value) as T

            } else if (store is ParamStoreArray) {
                return StringListExtraction(store.values) as T
            }
        } catch (t: Throwable) {
            logger.error("unknown exception occurred... ", t)
            throw ExtractionFailedException(t)
        }

        throw ExtractionFailedException("Param store type not supported")
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(String::class)
    }
}
