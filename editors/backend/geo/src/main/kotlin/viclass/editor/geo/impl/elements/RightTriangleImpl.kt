package viclass.editor.geo.impl.elements

import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.RightTriangle
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import kotlin.math.abs
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
class RightTriangleImpl constructor(
    doc: GeoDoc, name: String?,
    override val rightPoint: Point,
    point2: Point,
    point3: Point
) : RightTriangle, TriangleImpl(doc, name, rightPoint, point2, point3) {
    override val clazz: KClass<out RightTriangle> = RightTriangle::class

    override fun validate() {
        val l1 = LineSegmentImpl(doc, "${rightPoint.name}${point2.name}", rightPoint, point2)
        val l2 = LineSegmentImpl(doc, "${rightPoint.name}${point3.name}", rightPoint, point3)
        if (abs(l1.parallelVector.dot(l2.parallelVector)) > DEFAULT_TOLERANCE)
            throw InvalidElementException("$name is not a right triangle")
    }

    override fun mergeFrom(other: Element) {
        if (other !is RightTriangle) return
        super<TriangleImpl>.mergeFrom(other)
    }
}
