package viclass.editor.geo.impl.doc

import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionParams
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.dbentity.ElConstruction
import viclass.editor.geo.entity.ConstructorTemplate

/**
 * An implementation of the Construction interface
 */
class ConstructionImpl constructor(
    override var entity: ElConstruction,
    override var template: ConstructorTemplate,
    override var params : List<ConstructionParams>
) : Construction {
}
