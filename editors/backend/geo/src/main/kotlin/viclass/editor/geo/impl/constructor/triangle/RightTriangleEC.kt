package viclass.editor.geo.impl.constructor.triangle

import common.libs.logger.Logging
import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithLengthTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aName
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.lengthAssignment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.nameWithValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.RightTriangle
import viclass.editor.geo.elements.Triangle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.InvalidElementNameException
import viclass.editor.geo.extractable.NameForLength
import viclass.editor.geo.extractable.ValueExpression
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.RightTriangleImpl
import viclass.editor.geo.impl.elements.createVectorByEl
import viclass.editor.geo.impl.transformer.PointOnLineWithLengthTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.math.cos
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class RightTriangleEC : ElementConstructor<RightTriangle>, Logging {
    override fun outputType(): KClass<RightTriangle> {
        return RightTriangle::class
    }

    private enum class CGS {
        FromPoints,
        HypotenuseAndAdjacentAngle, HypotenuseAndAdjacentLength,
        AdjacentSideAndAngle, AdjacentSideAndHypotenuseLength,
        AdjacentSideLengths, AdjacentSideAndAdjacentSideLength, AdjacentSideLengthAndHypotenuseLength
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromPoints.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("Points"),
                        "tpl-Points"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aName]!!,
                        listOf("Point"),
                        "tpl-AtPoint"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.HypotenuseAndAdjacentAngle.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-Hypotenuse"
                    )
                    .constraintOptional(
                        1, ConstraintParamDefManager.instance()[nameWithValue]!!,
                        listOf(0),
                        listOf("NameOfPoint", "Degree"),
                        "tpl-AdjacentAngle"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.AdjacentSideAndAngle.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-AdjacentSide"
                    )
                    .constraintOptional(
                        1, ConstraintParamDefManager.instance()[nameWithValue]!!,
                        listOf(0),
                        listOf("NameOfPoint", "Degree"),
                        "tpl-AdjacentAngle"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.HypotenuseAndAdjacentLength.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-Hypotenuse"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf("Expression"),
                        "tpl-AdjacentSideLength"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.AdjacentSideAndHypotenuseLength.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-AdjacentSide"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf("Expression"),
                        "tpl-HypotenuseLength"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.AdjacentSideAndAdjacentSideLength.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-AdjacentSide"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf("Expression"),
                        "tpl-AdjacentSideLength"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.AdjacentSideLengths.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf("Expression"),
                        "tpl-AdjacentSideLength"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf("Expression"),
                        "tpl-AdjacentSideLength"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.AdjacentSideLengthAndHypotenuseLength.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf("Expression"),
                        "tpl-AdjacentSideLength"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf("Expression"),
                        "tpl-HypotenuseLength"
                    )
                    .build(),
            )
            .elTypes(RightTriangle::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<RightTriangle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 2)
                constructFromPoints(doc, inputName, c)
            }

            CGS.HypotenuseAndAdjacentAngle -> {
                Validations.validateNumConstraints(c, 1)
                constructFromNameWithHypotenuseAndAdjacentAngle(doc, inputName, c)
            }

            CGS.HypotenuseAndAdjacentLength -> {
                Validations.validateNumConstraints(c, 2)
                constructFromNameWithHypotenuseAndAdjacentLength(doc, inputName, c)
            }

            CGS.AdjacentSideAndHypotenuseLength -> {
                Validations.validateNumConstraints(c, 2)
                constructFromNameWithAdjacentSideAndHypotenuseLength(doc, inputName, c)
            }

            CGS.AdjacentSideLengths -> {
                Validations.validateNumConstraints(c, 2)
                constructFromNameWithAdjacentSideLengths(doc, inputName, c)
            }

            CGS.AdjacentSideAndAdjacentSideLength -> {
                Validations.validateNumConstraints(c, 2)
                constructFromNameAdjacentSideAndAdjacentSideLength(doc, inputName, c)
            }

            CGS.AdjacentSideLengthAndHypotenuseLength -> {
                Validations.validateNumConstraints(c, 2)
                constructFromNameWithAdjacentSideLengthAndHypotenuseLength(doc, inputName, c)
            }

            CGS.AdjacentSideAndAngle -> {
                Validations.validateNumConstraints(c, 1)
                constructFromNameWithAdjacentSideAndAngle(doc, inputName, c)
            }
        }
    }

    private fun constructFromNameWithAdjacentSideLengths(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<RightTriangle> {
        TODO("Not yet implemented")
    }

    private fun constructFromNameWithAdjacentSideLengthAndHypotenuseLength(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<RightTriangle> {
        TODO("Not yet implemented")
    }

    private fun constructFromNameWithAdjacentSideAndAngle(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<RightTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var adjacentAngle: Double? = null
        var adjacentVertexName: String? = null
        var thResult: Int? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                }

                nameWithValue -> {
                    adjacentVertexName =
                        extractFirstPossible<StringExtraction>(doc, ParamKind.PK_Name, p, c.ctIdx).result

                    adjacentAngle =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx).result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                } else {
                    adjacentAngle =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx).result
                }
            }
        }


        if (adjacentAngle == null || adjacentAngle!! <= 0 || 90.0 <= adjacentAngle!!)
            throw ConstructionException("Adjacent angle $adjacentAngle is not valid")

        val line: LineSegment = lineSegmentExtractionResult!!.result()!!
        val p2 = line.p1
        val p3 = line.p2

        val sideVertexNames = line.vertices().map { it.name!! }

        if (adjacentVertexName == null || !sideVertexNames.contains(adjacentVertexName))
            throw ConstructionException("Adjacent vertex name $adjacentVertexName is not valid")

        val rightVertex: Point
        val adjacentVertex: Point
        if (p2.name == adjacentVertexName) {
            rightVertex = p3
            adjacentVertex = p2
        } else {
            rightVertex = p2
            adjacentVertex = p3
        }

        val hypotenuseLength = line.length() / cos(radian(adjacentAngle!!))

        val points = RightTriangles.findAdjacentVertexOfRightTriangleWhenKnowARightSideAndHypotenuseLength(
            rightVertex,
            adjacentVertex,
            hypotenuseLength
        )

        val v = if (points.size == 1) points[0]
        else if (thResult == null) points[0]
        else Orders.pointsBaseLineReference(line, points[0], points[1])[thResult!!]

        var pName = generatePointName(doc, *sideVertexNames.toTypedArray())

        val name: String = inputName ?: "${pName}${line.name}"

        val names = NamePattern.extractPointName(Triangle::class, name)

        if (!names.containsAll(sideVertexNames))
            throw ConstructionException("Name of triangle not match")

        pName = (names - sideVertexNames.toSet()).first()

        val p = PointImpl(doc, pName, v)

        val cr = ConstructionResultImpl<RightTriangle>()
        cr.setResult(RightTriangleImpl(doc, name, p, p2, p3))
        if (!lineSegmentExtractionResult!!.newly) cr.mergeAsDependency(lineSegmentExtractionResult!!)
        else {
            cr.addDependency(p2, listOf(), true)
            cr.addDependency(p3, listOf(), true)
        }
        cr.addDependency(p, listOf(), true)

        return cr
    }

    private fun constructFromNameAdjacentSideAndAdjacentSideLength(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResultImpl<RightTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var nl: NameForLength? = null
        var le: ValueExpression? = null
        var thResult: Int? = null
        var line: LineSegment? = null
        var p1: Point? = null
        var p2: Point? = null
        var parallelVector: Vector3D? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                    line = lineSegmentExtractionResult!!.result()!!
                    val lineNameSubmit = line!!.name!!
                    val pointsName = NamePattern.extractPointName(LineSegment::class, lineNameSubmit)
                    val points = pointsName.map { n -> doc.findElementByName(n, Point::class, c.ctIdx) }
                    p1 = points[0]!!
                    p2 = points[1]!!

                    parallelVector = createVectorByEl(doc, p1!!, p2!!)
                }

                lengthAssignment -> {
                    nl = extractFirstPossible<ExtractableWithConstructions<NameForLength>>(
                        doc, ParamKind.PK_Name, p, c.ctIdx
                    ).result

                    le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc, ParamKind.PK_Expr, p, c.ctIdx
                    ).result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                } else {
                    le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc, ParamKind.PK_Expr, p, c.ctIdx
                    ).result
                }
            }
        }

        val sideVertexNames = line!!.vertices().map { it.name!! }

        val rightVertexName: String?
        val pName: String?
        val length = le!!.value

        if (nl != null) {
            pName = nl!!.unknown
            if (nl!!.reference != null)
                rightVertexName = nl!!.reference!!.name
            else
                rightVertexName = listOf(p1!!.name!!, p2!!.name!!).firstOrNull { it != pName }
        } else {
            if (inputName != null) {
                val vertexNames = NamePattern.extractPointName(Triangle::class, inputName)
                pName = vertexNames.subtract(setOf(p1!!.name!!, p2!!.name!!)).firstOrNull()
            } else {
                pName = generatePointName(doc, arrayListOf(p1!!.name!!, p2!!.name!!))
            }
            rightVertexName = p2!!.name
        }

        if (rightVertexName == p2!!.name!!) {
            p1 = p2.apply { p2 = p1 }
        }

        val name = inputName ?: "${p1!!.name!!}${p2!!.name!!}${pName!!}"

        val names = NamePattern.extractPointName(Triangle::class, name)

        if (!names.containsAll(sideVertexNames))
            throw ConstructionException("Name of triangle not match")

        val points =
            RightTriangles.computeRightTriangleAdjacentVertices(p1!!, p2!!, length)

        val v = if (points.size == 1) points[0]
        else if (thResult == null) points[0]
        else Orders.pointByParallelVector(parallelVector!!, p1!!.coordinates(), points[0], points[1])[thResult!!]

        val p3 = PointImpl(doc, pName, v)
        val root = p1!!.coordinates().toArray()
        val unitVector = p1!!.coordinates().vectorTo(p3.coordinates()).normalize().toArray()
        p3.transformer = TransformMapping.fromClazz(PointOnLineWithLengthTransformer::class)
        p3.transformData = PointOnLineWithLengthTransformData(
            lengthParamIdx = 1,
            lengthParamKind = ParamKind.PK_Expr,
            nthParamIdx = 2,
            root = root,
            unitVector = unitVector
        )
        p3.movementPath = MovementLinePath(root, unitVector)

        val cr = ConstructionResultImpl<RightTriangle>()
        cr.setResult(RightTriangleImpl(doc, name, p1!!, p2!!, p3))
        if (!lineSegmentExtractionResult!!.newly) cr.mergeAsDependency(lineSegmentExtractionResult!!)
        else {
            cr.addDependency(p1!!, listOf(), true)
            cr.addDependency(p2!!, listOf(), true)
        }
        cr.addDependency(p3, listOf(), true)

        return cr
    }

    private fun constructFromNameWithAdjacentSideAndHypotenuseLength(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<RightTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var nl: NameForLength? = null
        var le: ValueExpression? = null
        var thResult: Int? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                }

                lengthAssignment -> {
                    nl = extractFirstPossible<ExtractableWithConstructions<NameForLength>>(
                        doc, ParamKind.PK_Name, p, c.ctIdx
                    ).result

                    le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc, ParamKind.PK_Expr, p, c.ctIdx
                    ).result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                } else {
                    le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc, ParamKind.PK_Expr, p, c.ctIdx
                    ).result
                }
            }
        }

        val line: LineSegment = lineSegmentExtractionResult!!.result()!!
        var p1 = line.p1
        var p2 = line.p2

        val sideVertexNames = line.vertices().map { it.name!! }

        val sideVertexName: String?
        val pName: String?
        val length = le!!.value

        if (length <= line.length())
            throw ConstructionException("Hypotenuse length need to > adjacent side length")

        if (nl != null) {
            pName = nl!!.unknown
            if (nl!!.reference != null)
                sideVertexName = nl!!.reference!!.name
            else
                sideVertexName = listOf(p1.name!!, p2.name!!).firstOrNull { it != pName }
        } else {
            if (inputName != null) {
                val vertexNames = NamePattern.extractPointName(Triangle::class, inputName)
                pName = vertexNames.subtract(setOf(p1.name!!, p2.name!!)).firstOrNull()
            } else {
                pName = generatePointName(doc, arrayListOf(p1.name!!, p2.name!!))
            }
            sideVertexName = p2.name
        }

        if (sideVertexName == p1.name) {
            p1 = p2.apply { p2 = p1 }
        }

        val name = inputName ?: "${p1.name!!}${p2.name!!}${pName!!}"

        val names = NamePattern.extractPointName(Triangle::class, name)

        if (!names.containsAll(sideVertexNames)) throw ConstructionException("Name of triangle not match")

        val points =
            RightTriangles.findAdjacentVertexOfRightTriangleWhenKnowARightSideAndHypotenuseLength(p1, p2, length)

        val v = if (points.size == 1) points[0]
        else if (thResult == null) points[0]
        else Orders.pointsBaseLineReference(line, points[0], points[1])[thResult!!]

        val p3 = PointImpl(doc, pName, v)

        val cr = ConstructionResultImpl<RightTriangle>()
        cr.setResult(RightTriangleImpl(doc, name, p1, p2, p3))
        if (!lineSegmentExtractionResult!!.newly) cr.mergeAsDependency(lineSegmentExtractionResult!!)
        else {
            cr.addDependency(p1, listOf(), true)
            cr.addDependency(p2, listOf(), true)
        }
        cr.addDependency(p3, listOf(), true)

        return cr
    }

    private fun constructFromNameWithHypotenuseAndAdjacentLength(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<RightTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var nl: NameForLength? = null
        var le: ValueExpression? = null
        var thResult: Int? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                }

                lengthAssignment -> {
                    nl = extractFirstPossible<ExtractableWithConstructions<NameForLength>>(
                        doc, ParamKind.PK_Name, p, c.ctIdx
                    ).result

                    le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc, ParamKind.PK_Expr, p, c.ctIdx
                    ).result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                } else {
                    le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc, ParamKind.PK_Expr, p, c.ctIdx
                    ).result
                }
            }
        }

        val line: LineSegment = lineSegmentExtractionResult!!.result()!!
        val p2 = line.p1
        val p3 = line.p2

        val sideVertexNames = line.vertices().map { it.name!! }

        if (line.length() <= le!!.value)
            throw ConstructionException("Hypotenuse length need to > adjacent side length")

        val rightVertexName = nl?.unknown ?: generatePointName(doc, arrayListOf(p2.name!!, p3.name!!))

        val name: String = inputName ?: "${rightVertexName}${line.name}"

        val names = NamePattern.extractPointName(Triangle::class, name)

        if (!names.containsAll(sideVertexNames))
            throw ConstructionException("Name of triangle not match")

        val adjacentSideName = "$rightVertexName${p2.name!!}"

        val points =
            RightTriangles.computeRightTriangleThirdVertices(adjacentSideName, p2, p3, le!!.value)
        points ?: throw ConstructionException("Cannot construct right triangle")

        val v = if (points.size == 1) points[0]
        else if (thResult == null) points[0]
        else Orders.pointsBaseLineReference(line, points[0], points[1])[thResult!!]

        val rightVertex = PointImpl(doc, rightVertexName, v.x, v.y, v.z)

        val cr = ConstructionResultImpl<RightTriangle>()
        cr.setResult(RightTriangleImpl(doc, name, rightVertex, p2, p3))
        if (!lineSegmentExtractionResult!!.newly) cr.mergeAsDependency(lineSegmentExtractionResult!!)
        else {
            cr.addDependency(p2, listOf(), true)
            cr.addDependency(p3, listOf(), true)
        }
        cr.addDependency(rightVertex, listOf(), true)

        return cr
    }

    private fun constructFromNameWithHypotenuseAndAdjacentAngle(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<RightTriangle> {
        var lineSegmentExtractionResult: ConstructionResult<out LineSegment>? = null
        var adjacentAngle: Double? = null
        var adjacentVertexName: String? = null
        var thResult: Int? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val extraction =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, p, c.ctIdx)
                    lineSegmentExtractionResult = extraction.result
                }

                nameWithValue -> {
                    adjacentVertexName =
                        extractFirstPossible<StringExtraction>(doc, ParamKind.PK_Name, p, c.ctIdx).result

                    adjacentAngle =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx).result
                }

                aValue -> if (p.specs.indexInCG == 2) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                } else {
                    adjacentAngle =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx).result
                }
            }
        }

        if (adjacentAngle == null || adjacentAngle!! <= 0 || 90.0 <= adjacentAngle!!)
            throw ConstructionException("Adjacent angle $adjacentAngle is not valid")

        val line: LineSegment = lineSegmentExtractionResult!!.result()!!
        val p2 = line.p1
        val p3 = line.p2

        val sideVertexNames = line.vertices().map { it.name!! }

        if (adjacentVertexName == null || !listOf(p2.name!!, p3.name!!).contains(adjacentVertexName))
            throw ConstructionException("Adjacent vertex name $adjacentVertexName is not valid")

        var rightVertexName = generatePointName(doc, *sideVertexNames.toTypedArray())

        val name: String = inputName ?: "${rightVertexName}${line.name}"

        val names = NamePattern.extractPointName(Triangle::class, name)

        if (!names.containsAll(sideVertexNames))
            throw ConstructionException("Name of triangle not match")

        rightVertexName = (names - sideVertexNames.toSet()).first()

        val points = RightTriangles.findRightVertexWithHypotenuseAndAdjacentAngle(
            adjacentVertexName!!,
            line,
            radian(adjacentAngle!!)
        )
            ?: throw ConstructionException("Cannot construct right triangle")

        val v = if (points.size == 1) points[0]
        else if (thResult == null) points[0]
        else Orders.pointsBaseLineReference(line, points[0], points[1])[thResult!!]

        val rightVertex = PointImpl(doc, rightVertexName, v.x, v.y, v.z)

        val cr = ConstructionResultImpl<RightTriangle>()
        cr.setResult(RightTriangleImpl(doc, name, rightVertex, p2, p3))
        if (!lineSegmentExtractionResult!!.newly) cr.mergeAsDependency(lineSegmentExtractionResult!!)
        else {
            cr.addDependency(p2, listOf(), true)
            cr.addDependency(p3, listOf(), true)
        }
        cr.addDependency(rightVertex, listOf(), true)

        return cr
    }

    private fun constructFromPoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<RightTriangle> {
        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx)
        val rightPointName = extractFirstPossible<StringExtraction>(doc, PK_Name, c.params[1], c.ctIdx).result
        inputName?.let {
            val pointNames = NamePattern.extractPointName(Triangle::class, it).sortedBy { it }
            if (ext.result.map { it.result()?.name }.sortedBy { it } != pointNames)
                throw InvalidElementNameException("input name is not match with input points")
        }
        val points = ext.result.map { it.result()!! }
        val rightPoint = points.firstOrNull { it.name == rightPointName } ?: throw ConstructionException("Right Point not found")
        val basePoint = points.filter { it.name != rightPointName }
        val triangle = RightTriangleImpl(doc, inputName ?: points.map { it.name }.joinToString(""), rightPoint, basePoint[0], basePoint[1])
        val cr = ConstructionResultImpl<RightTriangle>()
        cr.addDependencies(points, true)
        cr.setResult(triangle)
        return cr
    }
}
