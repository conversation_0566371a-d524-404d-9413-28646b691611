package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.MoveAngleTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.transformer.Transformer

/**
 * Transformer for updating an angle parameter based on a new position.
 * The parameter store holds the angle value.
 */
class MoveAngleTransformer() : Transformer<MoveAngleTransformData> {
    override fun apply(
        doc: GeoDoc, c: Construction, transformData: MoveAngleTransformData, pos: Vector3D
    ) {
        // Center of rotation
        val centerVec = Vector3D.of(transformData.root)
        // Original source point
        val sourceVec = Vector3D.of(transformData.source)
        // Reference vector along the x-axis from the center
        val refVector = centerVec.vectorTo(Vector3D.of(centerVec.x + 10, centerVec.y, centerVec.z))
        // Vector from center to source
        val centerToSource = centerVec.vectorTo(sourceVec)
        // Vector from center to new position
        val centerToPos = centerVec.vectorTo(pos)
        // Angle from reference to source
        val angleSource = refVector.angleTo(centerToSource)
        // Angle from reference to new position
        val angleNew = refVector.angleTo(centerToPos)
        // Change in angle
        val angleDelta = angleNew - angleSource
        // Retrieve the parameter store for the angle
        val paramStore = c.params[transformData.targetParamIdx].specs.getParam(ParamKind.PK_Value) as ParamStoreValue
        // Update the angle value
        val currentAngle = paramStore.value.toDouble()
        paramStore.value = (currentAngle + angleDelta).toString()
    }
}
