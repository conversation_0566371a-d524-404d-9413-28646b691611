package viclass.editor.geo.impl.constructor.point

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPolygon
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.anElement
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.reflect.KClass

@Singleton
class CenterPointEC : ElementConstructor<Point> {

    private enum class CGS {
        UseAnElement, UsePointList
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this).cgs(
            ConstraintGroupBuilder.create().name(CGS.UseAnElement.name).constraint(
                0, ConstraintParamDefManager.instance()[aPolygon]!!, listOf("NameOfPolygon"), "tpl-CenterPointOfPolygon"
            ).hints("CenterPointOfPolygon").build(),
            ConstraintGroupBuilder.create().name(CGS.UsePointList.name).constraint(
                0, ConstraintParamDefManager.instance()[aPoint]!!, listOf("NameOfPoints"), "tpl-CenterPointOfPoints"
            ).hints("CenterPointOfPoints").build()
        ).elTypes(Point::class).build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.UseAnElement -> {
                Validations.validateNumConstraints(c, 1)
                constructUsingElement(doc, inputName, c, c.params[0])
            }

            CGS.UsePointList -> {
                Validations.validateNumConstraints(c, 1)
                constructFromPointList(doc, inputName, c, c.params[0])
            }
        }
    }

    private fun constructFromPointList(
        doc: GeoDoc, inputName: String?, c: Construction, params: ConstructionParams
    ): ConstructionResult<Point> {
        val er = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, params, c.ctIdx)
        val points: List<Point> = er.result.mapNotNull { it.result() }

        val name = inputName ?: generatePointName(doc)

        return calculatePoint(doc, name, points)
    }

    private fun constructUsingElement(
        doc: GeoDoc, inputName: String?, c: Construction, params: ConstructionParams
    ): ConstructionResult<Point> {
        val er = extractFirstPossible<ElementExtraction<Polygon>>(doc, PK_Name, params, c.ctIdx)
        val result: ConstructionResult<out Element> = er.result

        val vertices: List<Point> = result.result()!!.vertices();

        val name = inputName ?: generatePointName(doc)

        val cr = calculatePoint(doc, name, vertices)
        cr.mergeAsDependency(result)
        return cr
    }

    private fun calculatePoint(doc: GeoDoc, name: String?, vertices: List<Point>): ConstructionResult<Point> {
        val c = ConstructionResultImpl<Point>()
        var x = 0.0
        var y = 0.0
        var z = 0.0
        for (p in vertices) {
            x += p.x
            y += p.y
            z += p.z
            c.addDependency(p, listOf(), true)
        }
        x /= vertices.size.toDouble()
        y /= vertices.size.toDouble()
        z /= vertices.size.toDouble()
        val p = PointImpl(doc, name, x, y, z)
        p.name = name
        c.setResult(p)
        return c
    }

    override fun outputType(): KClass<Point> {
        return Point::class
    }
}
