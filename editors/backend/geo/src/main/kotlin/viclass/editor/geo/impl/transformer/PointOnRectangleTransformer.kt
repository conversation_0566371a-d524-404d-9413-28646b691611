package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnPolygonTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.impl.constructor.adjustRectanglePoints
import viclass.editor.geo.impl.constructor.crossProduct
import viclass.editor.geo.impl.constructor.extractRectangleFromPointsParams
import viclass.editor.geo.impl.constructor.isValidRectangle
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.extractors.PointWithCoordsExtractor.Companion.formatPoint
import viclass.editor.geo.transformer.Transformer

/**
 * Update the position of a point constrained on a rectangle (supports 3D).
 * Store the new position value [x, y, z] as string in ParamStore.
 */
class PointOnRectangleTransformer : Transformer<PointOnPolygonTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: PointOnPolygonTransformData, pos: Vector3D) {
        val (points, isClockwise, ratio) = extractRectangleFromPointsParams(doc, c)
        updatePoint(points, transformData.idxInPointParam, doc, pos)

        if (!isValidRectangle(points)) points.adjustRectanglePoints(
            doc,
            isClockwise,
            ratio,
            transformData.idxInPointParam
        )

        if (isValidRectangle(points)) {
            val p0 = points[0].coordinates()
            val p1 = points[1].coordinates()
            val p2 = points[2].coordinates()

            // Update the clockwise
            val newClockwise = p0.vectorTo(p1).crossProduct(p0.vectorTo(p2)) < 0.0
            (c.params[1].specs.getParam(PK_Value) as ParamStoreValue).value = newClockwise.toString()

            // Update the ratio
            val newRatio = p0.vectorTo(p1).norm() / p1.vectorTo(p2).norm()
            (c.params[2].specs.getParam(PK_Value) as ParamStoreValue).value = newRatio.toString()
        }

        // Update the points in the construction
        (c.params[0].specs.getParam(PK_Value) as ParamStoreArray).values = points.map { p ->
            formatPoint(p.name, p.x, p.y, p.z)
        }
    }

    private fun updatePoint(points: MutableList<Point>, idx: Int, doc: GeoDoc, pos: Vector3D) {
        points[idx] = PointImpl(points[idx].doc, points[idx].name, pos.x, pos.y, pos.z)
    }
}

