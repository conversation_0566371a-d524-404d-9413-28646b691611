package viclass.editor.geo.impl.constructor.line

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.createVectorByEl
import kotlin.reflect.KClass

@Singleton
class ThroughPointEC constructor(val extractorManager: ParamExtractorManager) : ElementConstructor<LineVi> {

    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }

    private enum class CGS {
        ThroughTwoPoints
    }

    override fun template(): ConstructorTemplate {
        val cg2 = ConstraintGroupBuilder.create()
            .name(CGS.ThroughTwoPoints.name)
            .hints("LineThroughTwoPoints")
            .constraint(
                0, ConstraintParamDefManager.instance()[aPoint]!!,
                listOf("NameOfPoint", "NameOfPoint"),
                "tpl-ThroughTwoPoints"
            )
            .build()

        return ConstructorTemplateBuilder.create(this)
            .cgs(cg2)
            .elTypes(LineVi::class, LineSegment::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineVi> {
        Validations.validateNumConstraints(c, 1)

        return when (CGS.valueOf(c.cgName)) {
            CGS.ThroughTwoPoints -> {
                Validations.validateNumConstraints(c, 1)
                constructThroughTwoPoints(doc, inputName, c, c.params[0])
            }
        }
    }

    private fun constructThroughTwoPoints(doc: GeoDoc, name: String?, c: Construction, cp: ConstructionParams): ConstructionResultImpl<LineVi> {
        val er = extractFirstPossible<ElementListExtraction<Point>>(doc, ParamKind.PK_Name, cp, c.ctIdx)
        val extResult = er.result
        val p1 = extResult[0].result()!!
        val p2 = extResult[1].result()!!

        val cr = ConstructionResultImpl<LineVi>()

        val lineName = if (name.isNullOrBlank()) "${p1.name}${p2.name}" else name

        if (NamePattern.isNameValid(LineSegment::class, lineName)) {
            val line = LineSegmentImpl(doc, lineName, p1, p2)
            cr.setResult(line)
        } else {
            val line = LineImpl(doc, lineName, p1, createVectorByEl(doc, p1, p2), p2)
            cr.setResult(line)
        }

        cr.addDependency(p1, emptyList(), true)
        cr.addDependency(p2, emptyList(), true)
        return cr
    }

}
