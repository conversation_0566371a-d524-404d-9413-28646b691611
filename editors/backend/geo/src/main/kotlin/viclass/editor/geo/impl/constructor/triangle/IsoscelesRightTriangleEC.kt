package viclass.editor.geo.impl.constructor.triangle

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementFreePath
import viclass.editor.geo.dbentity.transformdata.FreePointTransformData
import viclass.editor.geo.dbentity.transformdata.MoveOrderTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.IsoscelesRightTriangle
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.IsoscelesRightTriangleImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.FreePointTransformer
import viclass.editor.geo.impl.transformer.MoveOrderTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass

/**
 * IsoscelesRightTriangleEC is responsible for constructing an isosceles right triangle.
 * <AUTHOR>
 */
@Singleton
class IsoscelesRightTriangleEC : ElementConstructor<IsoscelesRightTriangle>, Logging {

    override fun outputType(): KClass<IsoscelesRightTriangle> {
        return IsoscelesRightTriangle::class
    }

    private enum class CGS { FromTwoPosition }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromTwoPosition.name)
                    .numDim(3)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
            )
            .elTypes(IsoscelesRightTriangle::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<IsoscelesRightTriangle> =
        when (CGS.valueOf(c.cgName)) {
            CGS.FromTwoPosition -> {
                Validations.validateNumConstraints(c, 2)
                constructFromTwoPosition(doc, inputName, c)
            }
        }

    /**
     * Construct an isosceles right triangle from two positions (defining one leg, with the second position as the apex).
     */
    private fun constructFromTwoPosition(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<IsoscelesRightTriangle> {
        val ext1 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[0], c.ctIdx)
        val ext2 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[1], c.ctIdx)
        val ext3 = if (c.params.size > 2)
            extractFirstPossible<NumberExtraction<Int>>(doc, PK_Value, c.params[2], c.ctIdx)
        else null
        val nth: Int = ext3?.result ?: 1
        val sig: Int = if (nth == 2) 1 else -1

        val pointCoords1 = ext1.result
        val pointCoords2 = ext2.result

        val pointsName: List<String> = if (!inputName.isNullOrBlank()) {
            NamePattern.extractPointName(Polygon::class, inputName)
        } else {

            (0..2).map { generatePointName(doc, ArrayList<String>()) }
        }
        if (pointsName.size < 3) throw ConstructionException("Cần ít nhất 3 tên điểm để tạo tam giác.")

        val p_a = PointImpl(doc, pointsName[0], pointCoords1[0], pointCoords1[1], pointCoords1.getOrElse(2) { 0.0 })
        val p_b_apex =
            PointImpl(doc, pointsName[1], pointCoords2[0], pointCoords2[1], pointCoords2.getOrElse(2) { 0.0 })

        val v_a = p_a.coordinates()
        val v_b_apex = p_b_apex.coordinates()

        val v_c = v_a.rotate(Math.PI / 2 * sig, v_b_apex)
        val p_c = v_c.toPoint(doc, pointsName[2])

        listOf(p_a to 0, p_b_apex to 1).forEach { (p, idx) ->
            p.transformData = FreePointTransformData(idx)
            p.transformer = TransformMapping.fromClazz(FreePointTransformer::class)
            p.movementPath = MovementFreePath()
        }

        p_c.transformData =
            MoveOrderTransformData(p_b_apex.coordinates().toArray(), p_c.coordinates().toArray(), listOf(0, 1))
        p_c.transformer = TransformMapping.fromClazz(MoveOrderTransformer::class)
        p_c.movementPath = MovementFreePath()

        val triangle = IsoscelesRightTriangleImpl(doc, pointsName.joinToString(""), p_b_apex, p_a, p_c)

        return ConstructionResultImpl<IsoscelesRightTriangle>().apply {
            setResult(triangle)
            addDependencies(listOf(p_a, p_b_apex), true)
            addDependency(p_c, listOf(p_a, p_b_apex), true)
        }
    }
}
