package viclass.editor.geo.impl.constructor.line

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aCircle
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ConstructionResultException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.*
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class TangentOfCirclesEC : ElementConstructor<LineVi> {
    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }

    private enum class CGS {
        TangentOf2Circles
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this).cgs(
            ConstraintGroupBuilder.create().name(CGS.TangentOf2Circles.name).constraint(
                0,
                ConstraintParamDefManager.instance()[aCircle]!!,
                listOf("NameOfCircle", "NameOfCircle"),
                "tpl-TangentOf2Circles"
            ).constraint(
                1, ConstraintParamDefManager.instance()[aValue]!!,
                listOf("NameOfOrder"),
                "tpl-Order"
            ).build(),
        ).elTypes(LineVi::class).build()
    }

    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.TangentOf2Circles -> {
                Validations.validateNumConstraints(c, 2)
                constructTangentOf2Circles(doc, c)
            }
        }
    }

    @Throws(ConstructionResultException::class)
    private fun constructTangentOf2Circles(
        doc: GeoDoc, c: Construction
    ): ConstructionResult<LineVi> {
        val result = ConstructionResultImpl<LineVi>()
        val circles = mutableListOf<Circle>()

        val extractedResults = extractFirstPossible<ElementListExtraction<Circle>>(
            doc, ParamKind.PK_Name, c.params[0], c.ctIdx
        )

        val orderRedult = extractFirstPossible<NumberExtraction<Int>>(
            doc, ParamKind.PK_Value, c.params[1], c.ctIdx
        )
        val order = orderRedult.result

        extractedResults.result.map {
            circles.add(it.result() ?: throw ConstructionException("Cannot find circle"))
            result.mergeAsDependency(it)
        }

        if (circles.size != 2) {
            throw ConstructionException("Must have 2 circles")
        }

        val tangents = mutableListOf<LineVi>()
        val c1 = circles[0].centerPoint.coordinates()
        val c2 = circles[1].centerPoint.coordinates()
        val r1 = circles[0].radius
        val r2 = circles[1].radius
        val dist = c1.distance(c2)

        // Trường hợp hai đường tròn chồng lên nhau, chỉ có tiếp tuyến ngoài
        if (dist < r1 + r2 && dist > abs(r1 - r2)) {
            if (order !in 1..2) {
                throw ConstructionException("Order must be 1 or 2")
            }
            tangents.addAll(findTangents(doc, c1, r1, c2, r2))
        }
        // Trường hợp hai đường tròn không chồng lên nhau, tìm cả tiếp tuyến trong và ngoài
        else if (dist > r1 + r2) {
            if (order !in 1..4) {
                throw ConstructionException("Order must be 1 or 4")
            }

            tangents.addAll(findTangents(doc, c1, r1, c2, r2))
            tangents.addAll(findTangents(doc, c1, r1, c2, r2, true))
        }

        if (tangents.isEmpty()) {
            throw ConstructionException("Cannot construct tangent")
        }

        val targetTangent = tangents[order - 1]
        val p1 = targetTangent.p1
        val p2 = targetTangent.p2!!

        p1.name = generatePointName(doc)
        p2.name = generatePointName(doc, p1.name!!)

        result.setResult(targetTangent)
        result.addDependencies(circles, true)
        result.addDependency(p1, circles, true)
        result.addDependency(p2, circles, true)

        return result
    }

    private fun findTangents(
        doc: GeoDoc,
        c1: Vector3D,
        r1: Double,
        c2: Vector3D,
        r2: Double,
        isInternal: Boolean = false
    ): List<LineVi> {
        val tangents = mutableListOf<LineVi>()

        val angleBetweenCenters = atan2((c2.y - c1.y), (c2.x - c1.x))
        val angleOffset1 = acos((if (isInternal) (r1 + r2) else (r1 - r2)) / c1.distance(c2))
        val angleOffset2 = -angleOffset1

        // Tính điểm tiếp tuyến cho cả hai đường tròn
        for (angleOffset in listOf(angleOffset1, angleOffset2)) {
            var angle = angleBetweenCenters + angleOffset

            val p1 = PointImpl(
                doc, "", c1.x + r1 * cos(angle), c1.y + r1 * sin(angle), 0.0
            )

            if (isInternal) {
                angle += PI
            }

            val p2 = PointImpl(
                doc, "", c2.x + r2 * cos(angle), c2.y + r2 * sin(angle), 0.0
            )

            tangents.add(
                LineImpl(
                    doc, p1.name + p2.name, p1, p1.coordinates().vectorTo(p2.coordinates()), p2
                )
            )
        }

        return tangents
    }
}
