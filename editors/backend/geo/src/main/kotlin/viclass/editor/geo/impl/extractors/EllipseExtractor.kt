package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import kotlin.reflect.KClass

/**
 * A ellipse extractor extract ellipses from given names. The name should have follow conventional format
 *
 * <AUTHOR>
 */
@Singleton
class EllipseExtractor constructor(private val evaluator: Evaluator) : ParameterExtractor {

    override var id: String = javaClass.simpleName

    @Throws(ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(doc: GeoDoc, pk: String, ctIdx: Int, clazz: KClass<out T>?): ConstructionResult<T> {
        val shapeName = evaluator.extractRootOperator(pk) { extractElementName(it, listOf("Hinh")) }
            ?: throw ElementNotExistInDocumentException("Invalid expression: $pk")

        doc.findElementByName(shapeName, Ellipse::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Ellipse>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        val matched = doc.elements
            .filterIsInstance<Ellipse>()
            .filter {
                NamePattern.isNameValid(Ellipse::class, shapeName) && NamePattern.extractPointName(Ellipse::class, shapeName).toSet() == NamePattern.extractPointName(
                    Ellipse::class,
                    "${it.f1.name}${it.f2.name}"
                ).toSet() &&
                        it.valid &&
                        it.usable &&
                        it.deleted != true
            }

        return when (matched.size) {
            1 -> {
                val cr = ConstructionResultImpl<Ellipse>()
                cr.setResult(matched.first())
                cr.newly = false
                cr as ConstructionResult<T>
            }

            0 -> throw ElementNotExistInDocumentException("No ellipse with name $shapeName could be found or constructed")

            else -> throw ElementNotExistInDocumentException(
                "Cannot proceed: multiple ellipse found with the same center point name '$shapeName'. Expected only one."
            )
        }

    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(Ellipse::class)
    }
}
