package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import net.objecthunter.exp4j.Expression
import net.objecthunter.exp4j.ExpressionBuilder
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.exceptions.ExtractionFailedException
import viclass.editor.geo.server.logger
import kotlin.reflect.KClass

@Singleton
class NumberConstantExtractor(private val evaluator: Evaluator) : ParameterExtractor {
    override var id: String = javaClass.simpleName

    @Throws(ExtractionFailedException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : ExtractionResult<out Any>> extract(doc: GeoDoc, store: ParamStore, ctIdx: Int, clazz: KClass<out Element>?): T {
        try {
            if (store is ParamStoreValue) {
                val strValue = store.value

                if (strValue.possiblyMathJSON()) {
                    try {
                        return NumberExtraction(evaluator.evaluate(strValue)) as T
                    } catch (_: Throwable) { }
                }

                val expression: Expression = ExpressionBuilder(strValue).build()
                val value = expression.evaluate()

                return NumberExtraction(value) as T

            } else if (store is ParamStoreArray) {

                val strListValue = store.values
                val result = strListValue.map {strValue ->
                    if (strValue.possiblyMathJSON()) {
                        try {
                            return NumberExtraction(evaluator.evaluate(strValue)) as T
                        } catch (_: Throwable) { }
                    }

                    val expression: Expression = ExpressionBuilder(strValue).build()
                    expression.evaluate()
                }

                return NumberListExtraction(result) as T
            }
        } catch (t: Throwable) {
            logger.error("unknown exception... ", t)
            throw ExtractionFailedException(t)
        }

        throw ExtractionFailedException("Param store type not supported")
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(Double::class, String::class)
    }
}
