package viclass.editor.geo.impl.constructor.triangle

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.lengthAssignment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.nameWithValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Triangle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.extractable.NameForLength
import viclass.editor.geo.extractable.ValueExpression
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.TriangleImpl
import viclass.editor.geo.impl.extractors.TriangleExtractor
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class TriangleBaseEC constructor(
    val extractorManager: ParamExtractorManager
) : ElementConstructor<Triangle>, Logging {

    override fun outputType(): KClass<Triangle> {
        return Triangle::class
    }

    enum class CGS {
        ByPointsName, Points,
        LineSegments, LineSegmentAndPoint,
        LineSegmentAndAngle, LineSegmentAndLengthAndAngle,
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.Points.name)
                    .hints("TriangleFromThreePoints")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfPoint", "NameOfPoint", "NameOfPoint"),
                        "tpl-FromThreePoints"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.LineSegments.name)
                    .hints("TriangleFromTwoLineSegment")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment", "NameOfLineSegment"),
                        "tpl-FromTwoLineSegments"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.LineSegmentAndPoint.name)
                    .hints("TriangleFromLineSegmentAndPoint")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-FromLineSegment"
                    ).constraint(
                        1, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-FromPoint"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.LineSegmentAndAngle.name)
                    .hints("TriangleFromLineSegmentAndAngle")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-FromLineSegment"
                    ).constraintDepends(
                        1, ConstraintParamDefManager.instance()[nameWithValue]!!,
                        listOf(0),
                        listOf("NameOfPoint", "Degree"),
                        "tpl-AdjacentAngle"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.LineSegmentAndLengthAndAngle.name)
                    .hints("TriangleFromLineSegmentAndLengthAndAngle")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-FromLineSegment"
                    ).constraintDepends(
                        1, ConstraintParamDefManager.instance()[nameWithValue]!!,
                        listOf(0),
                        listOf("NameOfPoint", "Degree"),
                        "tpl-AdjacentAngle"
                    ).constraintDepends(
                        2, ConstraintParamDefManager.instance()[lengthAssignment]!!,
                        listOf(0),
                        listOf("NameOfLineSegment", "Expression"),
                        "tpl-AdjacentSideLength"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1, 2),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
            )
            .elTypes(Triangle::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Triangle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ByPointsName -> {
                inputName ?: throw ConstructionException("missing name")
                Validations.validateNumConstraints(c, 0)
                constructFromName(doc, inputName, c)
            }

            CGS.Points -> {
                Validations.validateNumConstraints(c, 1)
                constructFromPoints(doc, inputName, c, c.params[0])
            }

            CGS.LineSegments -> {
                Validations.validateNumConstraints(c, 1)
                constructFromLineSegments(doc, inputName, c, c.params[0])
            }

            CGS.LineSegmentAndPoint -> {
                Validations.validateNumConstraints(c, 2)
                constructFromLineSegmentPoint(doc, inputName, c, c.params)
            }

            CGS.LineSegmentAndAngle -> {
                Validations.validateNumConstraints(c, 2)
                constructFromLineSegmentAngle(doc, inputName, c, c.params)
            }

            CGS.LineSegmentAndLengthAndAngle -> {
                Validations.validateNumConstraints(c, 3)
                constructFromLineSegmentAngle(doc, inputName, c, c.params)
            }
        }
    }

    private fun constructFromName(doc: GeoDoc, inputName: String, c: Construction): ConstructionResult<Triangle> {
        val extractor = extractorManager[TriangleExtractor::class.simpleName!!]
        val result = extractor.extractConstructionResult<Triangle>(doc, inputName, c.ctIdx)
        return result
    }

    private fun constructFromPoints(
        doc: GeoDoc, name: String?, c: Construction, constructionParams: ConstructionParams
    ): ConstructionResultImpl<Triangle> {
        val extraction =
            extractFirstPossible<ElementListExtraction<Point>>(doc, ParamKind.PK_Name, constructionParams, c.ctIdx)
        val extractedResult = extraction.result

        val p1 = extractedResult[0].result()!!
        val p2 = extractedResult[1].result()!!
        val p3 = extractedResult[2].result()!!

        val cr = ConstructionResultImpl<Triangle>()
        cr.setResult(TriangleImpl(doc, name, p1, p2, p3))
        cr.addDependency(p1, listOf(), true)
        cr.addDependency(p2, listOf(), true)
        cr.addDependency(p3, listOf(), true)

        return cr
    }

    @Throws(RuntimeException::class)
    private fun constructFromLineSegments(
        doc: GeoDoc, name: String?, c: Construction, constructionParams: ConstructionParams
    ): ConstructionResultImpl<Triangle> {
        val extraction =
            extractFirstPossible<ElementListExtraction<LineSegment>>(
                doc,
                ParamKind.PK_Name,
                constructionParams,
                c.ctIdx
            )
        val extractedResult = extraction.result
        val l1 = extractedResult[0].result()!!
        val l2 = extractedResult[1].result()!!
        val points = setOf(l1.p1, l1.p2, l2.p1, l2.p2)
        if (points.size == 3) {
            val p1 = points.elementAt(0)
            val p2 = points.elementAt(1)
            val p3 = points.elementAt(2)

            val cr = ConstructionResultImpl<Triangle>()

            cr.setResult(TriangleImpl(doc, name, p1, p2, p3))
            cr.addDependency(l1, listOf(), true)
            cr.addDependency(l2, listOf(), true)

            return cr
        }
        throw ConstructionException("Wrong line segment to construct triangle")
    }

    private fun constructFromLineSegmentPoint(
        doc: GeoDoc, name: String?, c: Construction, constructionParams: List<ConstructionParams>
    ): ConstructionResultImpl<Triangle> {

        var line: ConstructionResult<out LineSegment>? = null
        var point: ConstructionResult<out Point>? = null

        for (p in constructionParams) {
            when (p.paramDef.id) {
                aLineSegment -> {
                    line = p.extractor(ParamKind.PK_Name)[0]
                        .extract<ElementExtraction<LineSegment>>(
                            doc,
                            ParamKind.PK_Name,
                            aLineSegment,
                            p.specs,
                            c.ctIdx
                        ).result
                }

                aPoint -> {
                    point = p.extractor(ParamKind.PK_Name)[0]
                        .extract<ElementExtraction<Point>>(doc, ParamKind.PK_Name, aPoint, p.specs, c.ctIdx).result
                }
            }
        }
        if (line == null || point == null) {
            throw ConstructionException("Not enough parameters to construct the triangle by line segment and point")
        }
        val p1 = line.result()!!.p1
        val p2 = line.result()!!.p2
        val p3 = point.result()!!

        val cr = ConstructionResultImpl<Triangle>()
        cr.setResult(TriangleImpl(doc, name, p1, p2, p3))
        cr.addDependency(line.result()!!, listOf(), true)
        cr.addDependency(point.result()!!, listOf(), true)

        return cr
    }

    private fun constructFromLineSegmentAngle(
        doc: GeoDoc, inputName: String?, c: Construction, params: List<ConstructionParams>
    ): ConstructionResultImpl<Triangle> {

        var adjacentVertexName: String? = null
        var adjacentAngle: Double? = null
        var lineC: ConstructionResult<out LineSegment>? = null
        var nl: NameForLength? = null
        var le: ValueExpression? = null
        var thResult: Int? = null

        for (p in params) {
            when (p.paramDef.id) {
                aLineSegment -> {
                    lineC = p.extractor(ParamKind.PK_Name)[0]
                        .extract<ElementExtraction<LineSegment>>(
                            doc,
                            ParamKind.PK_Name,
                            aLineSegment,
                            p.specs,
                            c.ctIdx
                        ).result
                }

                nameWithValue -> {
                    adjacentVertexName = extractFirstPossible<StringExtraction>(doc, ParamKind.PK_Name, p, c.ctIdx)
                        .result

                    adjacentAngle = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                        .result
                }

                lengthAssignment -> {
                    nl = extractFirstPossible<ExtractableWithConstructions<NameForLength>>(
                        doc,
                        ParamKind.PK_Name,
                        p,
                        c.ctIdx
                    )
                        .result

                    le = extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                        doc,
                        ParamKind.PK_Expr,
                        p,
                        c.ctIdx
                    )
                        .result
                }

                aValue -> if (p.specs.indexInCG in listOf(2, 3)) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Int>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    thResult = extractedResult.result - 1
                }
            }
        }

        if (lineC == null || adjacentAngle == null || adjacentVertexName == null) {
            throw ConstructionException("Not enough parameters to construct the triangle by line segment and point")
        }

        val line = lineC.result()!!

        var targetName: String? = null

        if (nl != null) {
            if (nl.reference?.name != adjacentVertexName)
                throw ConstructionException("Adjacent vertex name of angle is not match")
            targetName = nl.unknown
        }

        val excludedName = ArrayList<String>()
        excludedName.addAll(line.vertices().map { it.name!! })
        excludedName.add(adjacentVertexName)
        nl?.reference?.name?.let { excludedName.add(it) }
        targetName = targetName ?: generatePointName(doc, excludedName)

        val sideLength = le?.value ?: throw ConstructionException("missing side length")

        val p1 = line.p1
        val p2 = line.p2

        val points = Triangles.findVertexWhenKnownASideAAngleALength(
            doc,
            p1,
            p2,
            sideLength,
            adjacentVertexName,
            radian(adjacentAngle)
        )
            ?: throw ConstructionException("Cannot find target vertex")

        val v3 = if (points.size == 1) points[0]
        else if (thResult == null) points[0]
        else Orders.pointsBaseLineReference(line, points[0], points[1])[thResult]

        val p3 = PointImpl(doc, targetName, v3.x, v3.y, v3.z)

        var name = "${p1.name!!}${p2.name!!}${p3.name!!}"

        if (inputName != null) {
            if (!NamePattern.isSame(Triangle::class, name, inputName))
                throw ConstructionException("Triangle name is not match")
            name = inputName
        }

        val cr = ConstructionResultImpl<Triangle>()
        cr.setResult(TriangleImpl(doc, name, p1, p2, p3))
        cr.addDependency(lineC.result()!!, listOf(), true)
        nl?.constructions()?.forEach { if (!cr.newly) cr.mergeAsDependency(it) }
        cr.addDependency(p1, listOf(), true)
        cr.addDependency(p2, listOf(), true)
        cr.addDependency(p3, listOf(), true)

        return cr
    }
}
