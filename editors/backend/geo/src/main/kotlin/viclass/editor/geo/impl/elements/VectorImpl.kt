package viclass.editor.geo.impl.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.VectorVi
import viclass.editor.geo.math.MFunc

/**
 *
 * <AUTHOR>
 */
class VectorImpl(
    doc: GeoDoc, name: String?,
    private var _p1: Point, private var _p2: Point,
    parallelVector: Vector3D? = null
) : VectorVi, LineSegmentImpl(doc, name, _p1, _p2, parallelVector) {
    override val clazz = VectorVi::class

    override val p1: Point
        get() = _p1
    override val p2: Point
        get() = _p2

    override fun root(): Point {
        return p1
    }

    override fun end(): Point {
        return p2
    }

    override fun parallelVector(): Vector3D {
        return parallelVector
    }

    override fun vertices(): List<Point> {
        return listOf(p1, p2)
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(VectorVi::class, name)) return false
        return NamePattern.extractPointName(VectorVi::class, this.name!!).toSet() ==
                NamePattern.extractPointName(VectorVi::class, name).toSet()
    }

    override fun parametricFunc(dim: Dimension): MFunc {
        TODO("Not yet implemented")
    }

    override fun mergeFrom(other: Element) {
        if (other !is VectorVi) return
        super<LineSegmentImpl>.mergeFrom(other)

    }
}
