package viclass.editor.geo.impl.constructor.symmetry

import kotlin.reflect.KClass
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementExtraction
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.PolygonImpl

/**
 *
 * <AUTHOR>
 */
@Singleton
class SymmetryThroughPointEC constructor() : ElementConstructor<Element> {
    private enum class CGS {
        Point,
        Line,
        Circle,
        Ellipse,
        CircularSector,
        Polygon,
        Vector,
        Ray
    }

    @Suppress("UNCHECKED_CAST")
    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Element> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.Point -> {
                Validations.validateNumConstraints(c, 2)
                constructVertex(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.Line, CGS.Vector, CGS.Ray -> {
                Validations.validateNumConstraints(c, 2)
                constructLineLike(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.Circle -> {
                Validations.validateNumConstraints(c, 2)
                constructCircle(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.Ellipse -> {
                Validations.validateNumConstraints(c, 2)
                constructEllipse(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.CircularSector -> {
                Validations.validateNumConstraints(c, 2)
                constructCircularSector(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.Polygon -> {
                Validations.validateNumConstraints(c, 2)
                constructPolygon(doc, inputName, c) as ConstructionResult<Element>
            }
        }
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create().name(CGS.Point.name).constraintDefault().build(),
            )
            .elTypes(Element::class)
            .build()
    }

    override fun outputType(): KClass<Element> {
        return Element::class
    }

    private fun constructVertex(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        // Symmetry element (point) is always the first parameter, target is second
        val symExr: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val targetExr: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val symPoint = symExr.result.result()!!
        val targetPoint = targetExr.result.result()!!

        val symmetricPoint =
            symmetricPoint(
                doubleArrayOf(targetPoint.coordinates().x, targetPoint.coordinates().y),
                doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y),
            )

        val name = inputName ?: generatePointName(doc)
        val cr: ConstructionResultImpl<Point> = createSymmetricPoint(doc, symmetricPoint, name)
        cr.addDependency(symPoint, emptyList(), true)
        cr.addDependency(targetPoint, emptyList(), true)

        return cr
    }

    // Gộp logic của constructLine, constructVector, constructRay vào đây
    private fun constructLineLike(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<LineVi> {
        val cr: ConstructionResultImpl<LineVi>
        // Symmetry element (point) is always the first parameter, target is second
        val symExr: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val targetExr: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val symPoint = symExr.result.result()!!
        var targetLine = targetExr.result.result()!!

        val pointNames =
            if (inputName != null) NamePattern.extractPointName(targetLine.clazz, inputName)
            else {
                val result = arrayListOf<String>()
                when (targetLine) {
                    is LineSegment, is VectorVi -> {
                        result.add(generatePointName(doc))
                        result.add(generatePointName(doc, result))
                    }
                    is Ray -> {
                        result.add(generatePointName(doc))
                        if (targetLine.p2 != null) result.add(generatePointName(doc, result))
                    }
                    else -> {
                        result.add(generatePointName(doc))
                        if (targetLine.p2 != null) result.add(generatePointName(doc, result))
                    }
                }
                result
            }

        when (targetLine.clazz) {
            LineSegment::class -> {
                targetLine = targetLine as LineSegment
                val p1 = targetLine.p1
                val p2 = targetLine.p2

                val symmetricPoint1 =
                    symmetricPoint(
                        doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
                        doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y),
                    )
                val symmetricPoint2 =
                    symmetricPoint(
                        doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
                        doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
                    )
                cr = createSymmetricLineSegment(doc, symmetricPoint1, symmetricPoint2, pointNames[0], pointNames[1])
            }
            VectorVi::class -> {
                targetLine = targetLine as VectorVi
                val p1 = targetLine.p1
                val p2 = targetLine.p2

                val symmetricPoint1 =
                    symmetricPoint(
                        doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
                        doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
                    )
                val symmetricPoint2 =
                    symmetricPoint(
                        doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
                        doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
                    )

                cr = createSymmetricVector(doc, symmetricPoint1, symmetricPoint2, pointNames[0], pointNames[1])
            }
            Ray::class -> {
                targetLine = targetLine as Ray
                val p1 = targetLine.p1
                val p2 = targetLine.p2

                var symmetricPoint2: DoubleArray? = null
                val symmetricPoint1 =
                    symmetricPoint(
                        doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
                        doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
                    )

                if (p2 != null) {
                    symmetricPoint2 =
                        symmetricPoint(
                            doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
                            doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
                        )
                }

                cr =
                    createSymmetricRay(
                        doc,
                        targetLine,
                        symmetricPoint1,
                        symmetricPoint2,
                        pointNames[0],
                        if (p2 != null) pointNames[1] else null
                    )
            }
            else -> {
                val p1 = targetLine.p1
                val p2 = targetLine.p2

                var symmetricPoint2: DoubleArray? = null
                val symmetricPoint1 =
                    symmetricPoint(
                        doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
                        doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y),
                    )

                if (p2 != null) {
                    symmetricPoint2 =
                        symmetricPoint(
                            doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
                            doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y),
                        )
                }
                cr =
                    createSymmetricLineVi(
                        doc,
                        targetLine,
                        symmetricPoint1,
                        symmetricPoint2,
                        pointNames[0],
                        if (p2 != null) pointNames[1] else null
                    )
            }
        }

        cr.addDependency(symPoint, emptyList(), true)
        cr.addDependency(targetLine, emptyList(), true)
        return cr
    }

    private fun constructCircle(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Circle> {
        val symExr: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val targetExr: ElementExtraction<Circle> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val symPoint = symExr.result.result()!!
        val targetCircle = targetExr.result.result()!!

        val symmetricCenter =
            symmetricPoint(
                doubleArrayOf(targetCircle.centerPoint.coordinates().x, targetCircle.centerPoint.coordinates().y),
                doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
            )

        val name = inputName ?: generatePointName(doc)
        val cr: ConstructionResultImpl<Circle> = createSymmetricCircle(doc, targetCircle.radius, symmetricCenter, name)
        cr.addDependency(symPoint, emptyList(), true)
        cr.addDependency(targetCircle, emptyList(), true)
        return cr
    }

    private fun constructEllipse(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Ellipse> {
        val symExr: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val targetExr: ElementExtraction<Ellipse> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val symPoint = symExr.result.result()!!
        val targetEllipse = targetExr.result.result()!!

        val p1 = targetEllipse.f1
        val p2 = targetEllipse.f2

        val symmetricF1 =
            symmetricPoint(
                doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
                doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
            )
        val symmetricF2 =
            symmetricPoint(
                doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
                doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
            )

        val pointNames: List<String> =
            if (inputName != null) {
                NamePattern.extractPointName(Ellipse::class, inputName)
            } else {
                val p1Name = generatePointName(doc)
                val p2Name = generatePointName(doc, p1Name)
                listOf(p1Name, p2Name)
            }

        val cr: ConstructionResultImpl<Ellipse> =
            createSymmetricEllipse(
                doc,
                targetEllipse.a,
                targetEllipse.b,
                symmetricF1,
                symmetricF2,
                pointNames[0],
                pointNames[1]
            )
        cr.addDependency(symPoint, emptyList(), true)
        cr.addDependency(targetEllipse, emptyList(), true)

        return cr
    }

    private fun constructCircularSector(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<CircularSector> {
        val symExr: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val targetExr: ElementExtraction<CircularSector> =
            extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val symPoint = symExr.result.result()!!
        val targetSector = targetExr.result.result()!!

        val p1 = targetSector.centerPoint
        val p2 = targetSector.endPoint
        val p3 = targetSector.startPoint

        val symmetricPoint1 =
            symmetricPoint(
                doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
                doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
            )
        val symmetricPoint2 =
            symmetricPoint(
                doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
                doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
            )

        val symmetricPoint3 =
            symmetricPoint(
                doubleArrayOf(p3.coordinates().x, p3.coordinates().y),
                doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
            )

        val pointNames =
            if (inputName != null) NamePattern.extractPointName(CircularSector::class, inputName)
            else listOf(p1.name, p2.name, p3.name)
        val cr: ConstructionResultImpl<CircularSector> =
            createSymmetricCircularSector(
                doc,
                symmetricPoint1,
                symmetricPoint2,
                symmetricPoint3,
                pointNames[0],
                pointNames[1],
                pointNames[2]
            )
        cr.addDependency(symPoint, emptyList(), true)
        cr.addDependency(targetSector, emptyList(), true)

        return cr
    }

    private fun constructPolygon(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Polygon> {
        val cr = ConstructionResultImpl<Polygon>()
        val symExr: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val targetExr: ElementExtraction<Polygon> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val symPoint = symExr.result.result()!!
        val targetPolygon = targetExr.result.result()!!

        val pointNames =
            if (inputName != null) NamePattern.extractPointName(Polygon::class, inputName)
            else {
                val result = arrayListOf<String>()
                targetPolygon.vertices().forEach { result.add(generatePointName(doc, result)) }
                result
            }

        val newPoints =
            targetPolygon.vertices().mapIndexed { index, vertex ->
                val symmetricPoint1 =
                    symmetricPoint(
                        doubleArrayOf(vertex.coordinates().x, vertex.coordinates().y),
                        doubleArrayOf(symPoint.coordinates().x, symPoint.coordinates().y)
                    )
                PointImpl(doc, pointNames[index], symmetricPoint1[0], symmetricPoint1[1])
            }

        val newPolygon = PolygonImpl(doc, c.elName!!, newPoints)

        cr.setResult(newPolygon)

        newPoints.forEach { cr.addDependency(it, emptyList(), true) }

        cr.addDependency(symPoint, emptyList(), true)
        cr.addDependency(targetPolygon, emptyList(), true)

        return cr
    }
}
