package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.elements.TriangleImpl
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class TriangleExtractor(private val evaluator: Evaluator) : ParameterExtractor {

    override var id: String = javaClass.simpleName

    @Throws(ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(
        doc: GeoDoc,
        pk: String,
        ctIdx: Int,
        clazz: KClass<out T>?
    ): ConstructionResult<T> {
        val shapeName = evaluator.extractRootOperator(pk) { extractElementName(it, listOf("Hinh", "TamGiac")) }
            ?: throw ElementNotExistInDocumentException("Invalid expression: $pk")

        doc.findElementByName(shapeName, Triangle::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Triangle>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        NamePattern[Triangle::class]!![0].find(shapeName)?.let { matchResult ->
            if (matchResult.groups.size == 4) {  // a triangle must have three matching group, each group represent a point

                val f: (Int) -> Point = { i ->
                    val pName = matchResult.groupValues[i]
                    doc.findElementByName(pName, Point::class, ctIdx)
                        ?: throw ElementNotExistInDocumentException("Not found point $pName")
                }

                val p1 = f(1)
                val p2 = f(2)
                val p3 = f(3)

                val triangle = TriangleImpl(doc, shapeName, p1, p2, p3)
                val cr = ConstructionResultImpl<Triangle>()
                cr.setResult(triangle)
                cr.addDependency(p1, listOf(), true)
                cr.addDependency(p2, listOf(), true)
                cr.addDependency(p3, listOf(), true)

                return cr as ConstructionResult<T>
            }
        }

        throw ElementNotExistInDocumentException("No triangle with name $shapeName could be found or constructed")

    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(
            Triangle::class, EquilateralTriangle::class, IsoscelesTriangle::class, RightTriangle::class,
            IsoscelesRightTriangle::class
        )
    }
}
