package viclass.editor.geo.impl.constructor.line

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Ray
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.extractors.LineExtractor
import kotlin.reflect.KClass

@Singleton
class RayEC constructor(val extractorManager: ParamExtractorManager) : ElementConstructor<Ray> {

    override fun outputType(): KClass<Ray> {
        return Ray::class
    }

    private enum class CGS {
        ByPointsName
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.ByPointsName.name)
                    .constraintDefault()
                    .build(),
            )
            .elTypes(Ray::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Ray> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ByPointsName -> {
                Validations.validateNumConstraints(c, 0)
                constructFromRayName(doc, inputName!!, c)
            }
        }
    }

    private fun constructFromRayName(
        doc: GeoDoc, inputName: String, c: Construction,
    ): ConstructionResult<Ray> {
        val extractor = extractorManager[LineExtractor::class.simpleName!!]
        return extractor.extractConstructionResult(doc, inputName, c.ctIdx, Ray::class)
    }
}
