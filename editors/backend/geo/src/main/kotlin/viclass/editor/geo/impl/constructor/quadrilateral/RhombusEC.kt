package viclass.editor.geo.impl.constructor.quadrilateral

import org.apache.commons.geometry.euclidean.threed.line.Lines3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementCirclePath
import viclass.editor.geo.dbentity.movement.path.MovementFreePath
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.FreePointTransformData
import viclass.editor.geo.dbentity.transformdata.MoveAngleTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.exceptions.InvalidElementNameException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.RhombusImpl
import viclass.editor.geo.impl.transformer.FreePointTransformer
import viclass.editor.geo.impl.transformer.MoveAngleTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class RhombusEC : ElementConstructor<Rhombus> {

    override fun outputType(): KClass<Rhombus> {
        return Rhombus::class
    }

    private enum class CGS {
        LineSegmentAndAngle, FromTwoPositionAndAngle, FromPoints
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromPoints.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("Points"),
                        "tpl-Points"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.FromTwoPositionAndAngle.name)
                    .numDim(3)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraint(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value"),
                        "tpl-AngleRadian"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.LineSegmentAndAngle.name)
                    .invisible()
                    .constraint(0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-FromLineSegment"
                    )
                    .constraint(1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value"),
                        "tpl-AngleRadian"
                    )
                    .build(),
            )
            .elTypes(Rhombus::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Rhombus> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 1)
                constructFromPoints(doc, inputName, c)
            }
            CGS.FromTwoPositionAndAngle -> {
                Validations.validateNumConstraints(c, 3)
                constructFromTwoPositionAndAngle(doc, inputName!!, c)
            }
            CGS.LineSegmentAndAngle -> {
                Validations.validateNumConstraints(c, 2)
                constructFromLineSegmentAndAngle(doc, inputName!!, c)
            }
        }
    }

    private fun constructFromPoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Rhombus> {
        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx)
        inputName?.let {
            val pointNames = NamePattern.extractPointName(Trapezoid::class, it).sortedBy { it }
            if (ext.result.map { it.result()?.name }.sortedBy { it } != pointNames) throw InvalidElementNameException("input name is not match with input points")
        }
        val points = ext.result.map { it.result()!! }
        val rhombus = RhombusImpl(doc, inputName ?: points.map { it.name }.joinToString(""), points[0], points[1], points[2], points[3])
        val cr = ConstructionResultImpl<Rhombus>()
        cr.addDependencies(points, true)
        cr.setResult(rhombus)
        return cr;
    }

    private fun constructFromTwoPositionAndAngle(
        doc: GeoDoc, inputName: String, c: Construction
    ): ConstructionResult<Rhombus> {
        val ext1 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[0], c.ctIdx)
        val ext2 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[1], c.ctIdx)
        val ext3 = extractFirstPossible<NumberExtraction<Double>>(doc, PK_Value, c.params[2], c.ctIdx)

        val pointsName: List<String> = if (inputName.isNotBlank()) {
            NamePattern.extractPointName(Polygon::class, inputName)
        } else {
            val excluded = ArrayList<String>()
            (0..4).map { generatePointName(doc, excluded) }
        }

        val p1 = PointImpl(doc, pointsName[0], ext1.result[0], ext1.result[1], .0)
        val p2 = PointImpl(doc, pointsName[1], ext2.result[0], ext2.result[1], .0)
        val angle = ext3.result

        val v1 = p1.coordinates()
        val v2 = p2.coordinates()

        val names = NamePattern.extractPointName(Quadrilateral::class, inputName)

        val v4 = v2.rotate(angle, v1)

        val l43 = Lines3D.fromPointAndDirection(v4, v1.vectorTo(v2), DEFAULT_PRECISION)
        val l23 = Lines3D.fromPointAndDirection(v2, v1.vectorTo(v4), DEFAULT_PRECISION)
        val v3 = l43.intersection(l23)

        val p3 = PointImpl(doc, names[2], v3)
        val p4 = PointImpl(doc, names[3], v4)

        val vC = Points.calculateCenterPoint(p1, p3)

        p1.transformData = FreePointTransformData(0)
        p1.transformer = TransformMapping.fromClazz(FreePointTransformer::class)
        p1.movementPath = MovementFreePath()

        p2.transformData = FreePointTransformData(1)
        p2.transformer = TransformMapping.fromClazz(FreePointTransformer::class)
        p2.movementPath = MovementFreePath()

        p3.transformer = TransformMapping.fromClazz(MoveAngleTransformer::class)
        p3.transformData = MoveAngleTransformData(
            root = v2.toArray(),
            source = v3.toArray(),
            targetParamIdx = 2
        )
        p3.movementPath = MovementCirclePath(v2.toArray(), Distances.of(v1, v2))

        p4.transformer = TransformMapping.fromClazz(MoveAngleTransformer::class)
        p4.transformData = MoveAngleTransformData(
            root = v1.toArray(),
            source = v4.toArray(),
            targetParamIdx = 2
        )
        p4.movementPath = MovementCirclePath(v1.toArray(), Distances.of(v1, v2))

        val cr = ConstructionResultImpl<Rhombus>()
        cr.setResult(RhombusImpl(doc, inputName, p1, p2, p3, p4))
        cr.addDependency(p1, listOf(), true)
        cr.addDependency(p2, listOf(), true)
        cr.addDependency(p3, listOf(), true)
        cr.addDependency(p4, listOf(), true)

        return cr
    }

    private fun constructFromLineSegmentAndAngle(
        doc: GeoDoc, inputName: String, c: Construction
    ): ConstructionResult<Rhombus> {
        var p1: Point? = null
        var p2: Point? = null
        var angle: Double? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val store: ParamStoreValue = ((p.specs.getParam(ParamKind.PK_Name)
                        ?: throw InvalidElementNameException("Parameter with name ${ParamKind.PK_Name} doesn't have any value"))
                            as ParamStoreValue)
                    val lineNameSubmit = store.value
                    val pointsName = NamePattern.extractPointName(LineSegment::class, lineNameSubmit)
                    val points = pointsName.map { n -> doc.findElementByName(n, Point::class, c.ctIdx) }
                    p1 = points[0]
                    p2 = points[1]
                }
                aValue -> if (p.specs.indexInCG == 1) {
                    val extractedResult = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    angle = extractedResult.result
                }
            }
        }

        p1 ?: throw ElementNotExistInDocumentException("not found p1")
        p2 ?: throw ElementNotExistInDocumentException("not found p2")
        angle ?: throw ConstructionException("missing angle")

        val names = NamePattern.extractPointName(Quadrilateral::class, inputName)

        val v1 = p1!!.coordinates()
        val v2 = p2!!.coordinates()

        val v4 = v2.rotate(angle!!, v1)

        val l43 = Lines3D.fromPointAndDirection(v4, v1.vectorTo(v2), DEFAULT_PRECISION)
        val l23 = Lines3D.fromPointAndDirection(v2, v1.vectorTo(v4), DEFAULT_PRECISION)
        val v3 = l43.intersection(l23)

        val p3 = PointImpl(doc, names[2], v3)
        val p4 = PointImpl(doc, names[3], v4)

        p3.transformer = TransformMapping.fromClazz(MoveAngleTransformer::class)
        p3.transformData = MoveAngleTransformData(
            root = v2.toArray(),
            source = v3.toArray(),
            targetParamIdx = 1
        )
        p3.movementPath = MovementCirclePath(v2.toArray(), Distances.of(v1, v2))

        p4.transformer = TransformMapping.fromClazz(MoveAngleTransformer::class)
        p4.transformData = MoveAngleTransformData(
            root = v1.toArray(),
            source = v4.toArray(),
            targetParamIdx = 1
        )
        p4.movementPath = MovementCirclePath(v1.toArray(), Distances.of(v1, v2))

        val cr = ConstructionResultImpl<Rhombus>()
        cr.setResult(RhombusImpl(doc, inputName, p1!!, p2!!, p3, p4))
        cr.addDependency(p1!!, listOf(), true)
        cr.addDependency(p2!!, listOf(), true)
        cr.addDependency(p3, listOf(), true)
        cr.addDependency(p4, listOf(), true)

        return cr
    }
}
