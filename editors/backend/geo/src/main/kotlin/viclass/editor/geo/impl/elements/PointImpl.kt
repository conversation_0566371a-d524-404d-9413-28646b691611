package viclass.editor.geo.impl.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.NamePattern
import viclass.editor.geo.dbentity.movement.path.MovementPath
import viclass.editor.geo.dbentity.transformdata.TransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dim
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.transformer.Transformer
import kotlin.reflect.KClass

class PointImpl constructor(
    override var doc: GeoDoc, override var name: String?,
    private var _x: Double, private var _y: Double, private var _z: Double,
) : Point {
    // Class implements the Point interface and defines basic point properties
    override val clazz: KClass<Point> = Point::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true

    // Getter methods for coordinate values
    override val x: Double
        get() = _x
    override val y: Double
        get() = _y
    override val z: Double
        get() = _z

    /**
     * Initialize the point in 2D
     * @param x X-coordinate value
     * @param y Y-coordinate value
     */
    constructor(doc: GeoDoc, x: Double, y: Double) : this(doc, null, x, y, 0.0) // Create unnamed 2D point
    constructor(doc: GeoDoc, name: String?, x: Double, y: Double) : this(doc, name, x, y, 0.0) // Create named 2D point

    /**
     * Initialize the point in 3D
     * @param x X-coordinate value
     * @param y Y-coordinate value
     * @param z Z-coordinate value
     */
    constructor(doc: GeoDoc, x: Double, y: Double, z: Double) : this(doc, null, x, y, z) {
        // Force z = 0 if document only supports 2 dimensions
        if (doc.numDim == 3) this._z = z else this._z = 0.0 
    }

    // Constructor that takes a Vector3D object to initialize point coordinates
    constructor(doc: GeoDoc, name: String?, coord: Vector3D) : this(doc, name, coord.x, coord.y, coord.z)

    /**
     * Returns the coordinate value for a specific dimension
     * @param dim The dimension (X, Y, or Z)
     * @return The coordinate value for the specified dimension
     * @throws InvalidElementException if dimension is not supported
     */
    override fun coord(dim: Dim): Double {
        return when {
            dim === Dim.X -> x
            dim === Dim.Y -> y
            dim === Dim.Z -> z
            else -> throw InvalidElementException("Dimension not supported")
        }
    }

    /**
     * Returns a list containing this point as its only vertex
     * @return A list containing this point
     */
    override fun vertices(): List<Point> {
        return listOf(this)
    }

    // Transform data for geometric transformations
    override var transformData: TransformData? = null
        get() = field
        set(value) { field = value}

    // Path data for movement animations
    override var movementPath: MovementPath? = null
        get() = field
        set(value) { field = value }

    // Transformer that applies transformations to this point
    override var transformer: Transformer<TransformData>? = null
        get() = field
        set(value) { field = value }

    /**
     * Checks if the provided name matches this point's name
     * @param name The name to check against
     * @return true if names match and the provided name is valid for a Point
     */
    override fun isNameMatch(name: String): Boolean {
        // First validate that the name follows Point naming pattern
        if (!NamePattern.isNameValid(Point::class, name)) return false

        // Then check if names match
        return name == this.name
    }

    /**
     * Returns the point's coordinates as a Vector3D object
     * @return A Vector3D representation of this point's coordinates
     */
    override fun coordinates(): Vector3D {
        return Vector3D.of(x, y, z)
    }

    /**
     * Merges properties from another element into this point
     * @param other The source element to merge from
     */
    override fun mergeFrom(other: Element) {
        // Only merge if the other element is a Point
        if (other !is Point) return

        // Merge common element properties from parent class
        super.mergeFrom(other)

        // Copy coordinate values from the other point
        _x = other.x
        _y = other.y
        _z = other.z
    }
}
