package viclass.editor.geo.impl.elements

import io.ktor.util.reflect.*
import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.dbentity.*
import viclass.editor.geo.dbentity.elementdata.*
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.exceptions.DeserializerException


/**
 *
 * <AUTHOR>
 */

typealias Deserializer = (GeoDoc, GeometryData) -> Element

val deserializers = mapOf<String, Deserializer>(
    PointGeometry::class.simpleName!! to ::deserializePoint,

    LineGeometry::class.simpleName!! to ::deserializeLine,
    LineSegmentGeometry::class.simpleName!! to ::deserializeLineSegment,
    VectorGeometry::class.simpleName!! to ::deserializeVector,
    RayGeometry::class.simpleName!! to ::deserializeRay,

    TriangleGeometry::class.simpleName!! to ::deserializeTriangle,
    RightTriangleGeometry::class.simpleName!! to ::deserializeTriangle,
    IsoscelesTriangleGeometry::class.simpleName!! to ::deserializeTriangle,
    IsoscelesRightTriangleGeometry::class.simpleName!! to ::deserializeTriangle,
    EquilateralTriangleGeometry::class.simpleName!! to ::deserializeTriangle,

    QuadrilateralGeometry::class.simpleName!! to ::deserializeQuadrilateral,
    ParallelogramGeometry::class.simpleName!! to ::deserializeQuadrilateral,
    RectangleGeometry::class.simpleName!! to ::deserializeQuadrilateral,
    SquareGeometry::class.simpleName!! to ::deserializeQuadrilateral,
    TrapezoidGeometry::class.simpleName!! to ::deserializeQuadrilateral,
    RhombusGeometry::class.simpleName!! to ::deserializeQuadrilateral,

    PolygonGeometry::class.simpleName!! to ::deserializePolygon,
    RegularPolygonGeometry::class.simpleName!! to ::deserializePolygon,

    CircleGeometry::class.simpleName!! to ::deserializeCircle,
    EllipseGeometry::class.simpleName!! to ::deserializeEllipse,
    SemicircleGeometry::class.simpleName!! to ::deserializeSemicircle,
    CircularSectorGeometry::class.simpleName!! to ::deserializeCircularSector,

    AngleGeometry::class.simpleName!! to ::deserializeAngle,
)

@Throws(DeserializerException::class)
fun deserializePoint(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is PointGeometry -> {
        PointImpl(geoDoc, data.x, data.y, data.z)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Point")
}

@Throws(DeserializerException::class)
fun deserializeLine(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is LineGeometry -> {
        val throughPointIdx = data.spIdx
        val p2Idx = data.epIdx
        val v = Vector3D.of(data.pVX, data.pVY, data.pvZ)
        val throughPoint = geoDoc.elements[throughPointIdx]
        val p2: Point? = p2Idx?.let { geoDoc.elements[it] as Point }

        if (!throughPoint.instanceOf(Point::class)) throw DeserializerException("The through point element is not a Point")

        LineImpl(geoDoc, null, throughPoint as Point, v, p2)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Line")
}

@Throws(DeserializerException::class)
fun deserializeLineSegment(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is LineSegmentGeometry -> {
        val sel = geoDoc.elements[data.spIdx]
        val eel = geoDoc.elements[data.epIdx]

        if (!sel.instanceOf(Point::class)) throw DeserializerException("The start point element is not a Point")
        if (!eel.instanceOf(Point::class)) throw DeserializerException("The end point element is not a Point")

        LineSegmentImpl(geoDoc, null, sel as Point, eel as Point)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Line Segment")
}

@Throws(DeserializerException::class)
fun deserializeVector(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is VectorGeometry -> {
        val sel = geoDoc.elements[data.spIdx]
        val eel = geoDoc.elements[data.epIdx]

        if (!sel.instanceOf(Point::class)) throw DeserializerException("The start point element is not a Point")
        if (!eel.instanceOf(Point::class)) throw DeserializerException("The end point element is not a Point")

        VectorImpl(geoDoc, null, sel as Point, eel as Point)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Vector")
}

@Throws(DeserializerException::class)
fun deserializeRay(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is RayGeometry -> {
        val throughPointIdx = data.spIdx
        val v = Vector3D.of(data.pVX, data.pVY, data.pvZ)
        val el = geoDoc.elements[throughPointIdx]

        val ep = data.epIdx?.let { geoDoc.elements[it] }

        if (!el.instanceOf(Point::class)) throw DeserializerException("The through point element is not a Point")

        RayImpl(geoDoc, null, el as Point, v, ep as Point?)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Ray")
}

@Throws(DeserializerException::class)
fun deserializeTriangle(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is TriangleGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")

        TriangleImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point)
    }

    is RightTriangleGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")

        RightTriangleImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point)
    }

    is IsoscelesTriangleGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")

        IsoscelesTriangleImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point)
    }

    is IsoscelesRightTriangleGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")

        IsoscelesRightTriangleImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point)
    }

    is EquilateralTriangleGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")

        EquilateralTriangleImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Triangle")
}

@Throws(DeserializerException::class)
fun deserializeQuadrilateral(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is SquareGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]
        val p4 = geoDoc.elements[data.p4Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")
        if (!p4.instanceOf(Point::class)) throw DeserializerException("The point 4 element is not a Point")

        SquareImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point, p4 as Point)
    }

    is RectangleGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]
        val p4 = geoDoc.elements[data.p4Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")
        if (!p4.instanceOf(Point::class)) throw DeserializerException("The point 4 element is not a Point")

        RectangleImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point, p4 as Point)
    }

    is ParallelogramGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]
        val p4 = geoDoc.elements[data.p4Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")
        if (!p4.instanceOf(Point::class)) throw DeserializerException("The point 4 element is not a Point")

        ParallelogramImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point, p4 as Point)
    }

    is RhombusGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]
        val p4 = geoDoc.elements[data.p4Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")
        if (!p4.instanceOf(Point::class)) throw DeserializerException("The point 4 element is not a Point")

        RhombusImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point, p4 as Point)
    }

    is TrapezoidGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]
        val p4 = geoDoc.elements[data.p4Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")
        if (!p4.instanceOf(Point::class)) throw DeserializerException("The point 4 element is not a Point")

        TrapezoidImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point, p4 as Point)
    }

    is QuadrilateralGeometry -> {
        val p1 = geoDoc.elements[data.p1Idx]
        val p2 = geoDoc.elements[data.p2Idx]
        val p3 = geoDoc.elements[data.p3Idx]
        val p4 = geoDoc.elements[data.p4Idx]

        if (!p1.instanceOf(Point::class)) throw DeserializerException("The point 1 element is not a Point")
        if (!p2.instanceOf(Point::class)) throw DeserializerException("The point 2 element is not a Point")
        if (!p3.instanceOf(Point::class)) throw DeserializerException("The point 3 element is not a Point")
        if (!p4.instanceOf(Point::class)) throw DeserializerException("The point 4 element is not a Point")

        QuadrilateralImpl(geoDoc, null, p1 as Point, p2 as Point, p3 as Point, p4 as Point)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Quadrilateral")
}

@Throws(DeserializerException::class)
fun deserializePolygon(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is RegularPolygonGeometry -> {
        val points = data.pIdxes.map {
            val p = geoDoc.elements[it]
            if (!p.instanceOf(Point::class)) throw DeserializerException("The element is not a Point")
            return@map p as Point
        }

        RegularPolygonImpl(geoDoc, null, points)
    }

    is PolygonGeometry -> {
        val points = data.pIdxes.map {
            val p = geoDoc.elements[it]
            if (!p.instanceOf(Point::class)) throw DeserializerException("The element is not a Point")
            return@map p as Point
        }

        PolygonImpl(geoDoc, null, points)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Polygon")
}

@Throws(DeserializerException::class)
fun deserializeCircle(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is CircleGeometry -> {
        val o = geoDoc.elements[data.centerIdx]

        if (!o.instanceOf(Point::class)) throw DeserializerException("The center element is not a Point")

        CircleImpl(geoDoc, null, o as Point, data.radius)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Circle")
}

@Throws(DeserializerException::class)
fun deserializeAngle(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is AngleGeometry -> {
        val anglePoint = geoDoc.elements[data.anglePointIdx]
        val lineStart = geoDoc.elements[data.lineStartIdx]
        val lineEnd = geoDoc.elements[data.lineEndIdx]

        if (!anglePoint.instanceOf(Point::class) || !lineStart.instanceOf(LineVi::class) || !lineEnd.instanceOf(LineVi::class))
            throw DeserializerException("The points are not a Point")

        AngleImpl(
            geoDoc,
            null,
            anglePoint as Point,
            lineStart as LineVi,
            data.directionStart,
            lineEnd as LineVi,
            data.directionEnd
        )
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Angle")
}

@Throws(DeserializerException::class)
fun deserializeEllipse(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is EllipseGeometry -> {
        val f1Point = geoDoc.elements[data.f1PointIdx]
        val f2Point = geoDoc.elements[data.f2PointIdx]

        EllipseImpl(geoDoc, null, f1Point as Point, f2Point as Point, data.a, data.b)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Ellipse")
}

@Throws(DeserializerException::class)
fun deserializeSemicircle(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is SemicircleGeometry -> {
        val centerPoint = geoDoc.elements[data.centerPointIdx]
        val startPoint = geoDoc.elements[data.startPointIdx]
        val endPoint = geoDoc.elements[data.endPointIdx]

        SemicircleImpl(geoDoc, null, centerPoint as Point, startPoint as Point, endPoint as Point)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a Semicircle")
}

@Throws(DeserializerException::class)
fun deserializeCircularSector(geoDoc: GeoDoc, data: GeometryData): Element = when (data) {
    is CircularSectorGeometry -> {
        val centerPoint = geoDoc.elements[data.centerPointIdx]
        val startPoint = geoDoc.elements[data.startPointIdx]
        val endPoint = geoDoc.elements[data.endPointIdx]

        CircularSectorImpl(geoDoc, null, centerPoint as Point, startPoint as Point, endPoint as Point)
    }

    else -> throw DeserializerException("Trying to deserialize data which is not a CircularSector")
}
