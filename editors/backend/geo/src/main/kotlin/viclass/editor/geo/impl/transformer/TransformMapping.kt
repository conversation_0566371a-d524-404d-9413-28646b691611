package viclass.editor.geo.impl.transformer

import org.reflections.Reflections
import viclass.editor.geo.dbentity.transformdata.TransformData
import viclass.editor.geo.transformer.Transformer
import kotlin.reflect.KClass
import kotlin.reflect.full.createInstance

object TransformMapping {
    private val transformMapping: Map<String, Transformer<TransformData>>
    init {
        val reflections = Reflections(this::class.java.`package`.name)
        val subTypes: List<Transformer<TransformData>> = reflections.getSubTypesOf(Transformer::class.java)
            .map { it.kotlin.createInstance() as Transformer<TransformData> }
        transformMapping = subTypes.associateBy { it::class.java.name }
    }

    fun fromClazz(clazz: KClass<out Transformer<out TransformData>>): Transformer<TransformData>? {
        return transformMapping[clazz.qualifiedName]
    }

    fun fromName(className: String): Transformer<TransformData>? {
        return transformMapping[className]
    }
}