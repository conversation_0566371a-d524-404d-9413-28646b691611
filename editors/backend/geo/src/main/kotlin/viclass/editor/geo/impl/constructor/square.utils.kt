package viclass.editor.geo.impl.constructor

import viclass.editor.geo.constructor.BooleanExtraction
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ElementListExtraction
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.Points.isNewPoint
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.abs
import kotlin.math.sqrt

fun extractSquareFromPointsParams(
    doc: GeoDoc, c: Construction
): Pair<MutableList<Point>, Boolean> {
    val targetParamIdx = 0
    val isClockwiseIdx = 1

    val points = extractFirstPossible<ElementListExtraction<Point>>(
        doc, ParamKind.PK_Value, c.params[targetParamIdx], c.ctIdx
    ).result.map { it.result() ?: throw IllegalArgumentException("Invalid point extraction") }.toMutableList()
    require(points.size == 4) { "Input must contain exactly 4 point names" }

    val isClockwise = extractFirstPossible<BooleanExtraction>(
        doc, ParamKind.PK_Value, c.params[isClockwiseIdx], c.ctIdx
    ).result

    return Pair(points, isClockwise)
}

fun isValidSquare(points: List<Point>): Boolean {
    if (points.size != 4) return false
    val dists = listOf(
        0 to 1, 0 to 2, 0 to 3, 1 to 2, 1 to 3, 2 to 3
    ).map { (i, j) ->
        val dx = points[i].x - points[j].x
        val dy = points[i].y - points[j].y
        dx * dx + dy * dy
    }.sorted()
    if (dists.any { it < DEFAULT_TOLERANCE }) return false
    val side = dists[0]
    val diagonal = dists[4]
    return dists.take(4).all { abs(it - side) <= DEFAULT_TOLERANCE } && dists.drop(4)
        .all { abs(it - diagonal) <= DEFAULT_TOLERANCE } && abs(diagonal - 2 * side) <= DEFAULT_TOLERANCE
}

fun MutableList<Point>.adjustSquarePoints(
    doc: GeoDoc, isClockwise: Boolean, movedIdx: Int? = null
) {
    val ps = map { it.coordinates() }.toMutableList()
    val fixed = (0..3).filter { !isNewPoint(doc, this[it]) }
    if (fixed.size > 2) return

    val startIdx = fixed.firstOrNull() ?: movedIdx ?: return
    val endIdx = fixed.find { it != startIdx } ?: movedIdx?.takeIf { it != startIdx } ?: (startIdx + 2).boundIndex(3)

    if (startIdx.isAdjacentIndex(endIdx, 3)) {
        val (aIdx, bIdx) = if (startIdx.nextIndex(3) == endIdx) startIdx to endIdx else endIdx to startIdx
        val a = ps[aIdx]
        val b = ps[bIdx]

        // Adjacent vertices
        val ab = a.vectorTo(b)
        val ac = (if (isClockwise) ab.rotateRight45() else ab.rotateLeft45()) * sqrt(2.0)
        val ad = if (isClockwise) ab.rotateRight90() else ab.rotateLeft90()

        val cIdx = (aIdx + 2).boundIndex(3)
        val dIdx = (aIdx + 3).boundIndex(3)

        ps[cIdx] = a + ac
        ps[dIdx] = a + ad
    } else {
        val (aIdx, cIdx) = if (startIdx < endIdx) startIdx to endIdx else endIdx to startIdx
        val a = ps[aIdx]
        val c = ps[cIdx]

        // Opposite vertices
        val ac = a.vectorTo(c)
        val ab = (if (isClockwise) ac.rotateLeft45() else ac.rotateRight45()) / sqrt(2.0)
        val ad = (if (isClockwise) ac.rotateRight45() else ac.rotateLeft45()) / sqrt(2.0)

        val bIdx2 = (aIdx + 1).boundIndex(3)
        val dIdx2 = (aIdx - 1).boundIndex(3)

        ps[bIdx2] = a + ab
        ps[dIdx2] = a + ad
    }

    for (i in 0..3) {
        this[i] = PointImpl(
            this[i].doc, this[i].name, ps[i].x, ps[i].y, ps[i].z
        ).apply {
            transformData = this@adjustSquarePoints[i].transformData
            transformer = this@adjustSquarePoints[i].transformer
            movementPath = this@adjustSquarePoints[i].movementPath
        }
    }
}