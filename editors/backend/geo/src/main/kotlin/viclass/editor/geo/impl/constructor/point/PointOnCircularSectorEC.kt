package viclass.editor.geo.impl.constructor.point

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementSectorPath
import viclass.editor.geo.dbentity.transformdata.PointOnArcWithAngleTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aCircularSector
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.CircularSector
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.transformer.PointOnArcWithAngleTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class PointOnCircularSectorEC : ElementConstructor<Point> {
    private enum class CGS {
        OnCircularSectorWithDegree, OnCircularSectorWithRadian
    }

    override fun outputType(): KClass<Point> {
        return Point::class
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.OnCircularSectorWithDegree.name)
                    .hints("PointOnCircularSectorWithDegree")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aCircularSector]!!,
                        listOf("NameOfCircularSector"),
                        "tpl-OnCircularSector"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Degree"),
                        "tpl-AngleDegree"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnCircularSectorWithRadian.name)
                    .hints("PointOnCircularSectorWithRadian")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aCircularSector]!!,
                        listOf("NameOfCircularSector"),
                        "tpl-OnCircularSector"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Radian"),
                        "tpl-AngleRadian"
                    )
                    .build(),
            )
            .elTypes(Point::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        return when(CGS.valueOf(c.cgName)) {
            CGS.OnCircularSectorWithDegree, CGS.OnCircularSectorWithRadian -> {
                Validations.validateNumConstraints(c, 2)
                constructOnCircularSectorWithAngle(doc, inputName, c)
            }
        }
    }

    private fun constructOnCircularSectorWithAngle(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val result = ConstructionResultImpl<Point>()

        var sector: CircularSector? = null
        var angle: Double? = null

        c.params.forEach {
            when (it.paramDef.id) {
                aCircularSector -> {
                    val extractedCircleResult = extractFirstPossible<ElementExtraction<CircularSector>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    sector = extractedCircleResult.result.result()!!
                    result.mergeAsDependency(extractedCircleResult.result)
                }

                aValue -> {
                    val extractedRangeResult = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, it, c.ctIdx)
                    angle = extractedRangeResult.result
                }
            }
        }

        if (sector == null || angle == null) {
            throw ConstructionException("missing circle or angle")
        }

        if(c.cgName == CGS.OnCircularSectorWithDegree.name) angle = radian(angle!!)

        val name = inputName ?: generatePointName(doc)
        val point = CircularSectors.calculatePointOnCircularSectorWithRadian(doc, name, sector!!, angle!!)
            ?: throw ConstructionException("angle out of bound")

        point.transformData = PointOnArcWithAngleTransformData(
            1,
            ParamKind.PK_Value,
            sector!!.centerPoint.coordinates().toArray(),
            sector!!.startPoint.coordinates().toArray(),
            degree = c.cgName == CGS.OnCircularSectorWithDegree.name
        )

        point.transformer = TransformMapping.fromClazz(PointOnArcWithAngleTransformer::class)
        point.movementPath = MovementSectorPath(
            sector!!.centerPoint.coordinates().toArray(),
            sector!!.startPoint.coordinates().toArray(),
            sector!!.endPoint.coordinates().toArray(),
        )

        result.setResult(point)
        result.addDependency(sector!!, listOf(), true)

        return result
    }
}
