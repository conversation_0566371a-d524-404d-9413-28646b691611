package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.elements.*
import kotlin.reflect.KClass

/**
 * A line extractor extract lines from given names. The name should have follow conventional format
 *
 * - AB => A line segment ended with two point A and B, AB or BA are considered to be equal line segment
 *
 *
 */
@Singleton
class LineExtractor constructor(private val evaluator: Evaluator) : ParameterExtractor {

    override var id: String = javaClass.simpleName

    @Throws(ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(doc: GeoDoc, pk: String, ctIdx: Int, clazz: KClass<out T>?): ConstructionResult<T> {
        val shapeName = evaluator.extractRootOperator(pk) { extractElementName(it, listOf("Hinh", "Duong", "Doan", "Tia", "ChieuDai")) }
            ?: throw ElementNotExistInDocumentException("Invalid expression: $pk")

        clazz?.let {
            doc.findElementByName(shapeName, it, ctIdx)?.let {
                val cr = ConstructionResultImpl<Element>()
                cr.setResult(it)
                cr.newly = false
                return cr as ConstructionResult<T>
            }
        }

        doc.findElementByName(shapeName, LineSegment::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Element>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        doc.findElementByName(shapeName, VectorVi::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Element>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        doc.findElementByName(shapeName, Ray::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Element>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        doc.findElementByName(shapeName, LineVi::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Element>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        // if no existing element is a matching line
        // the remaining case is that this lineName is a LineSegmentName (because case like ray Ax, or line a, the name must
        // have been match exactly with the doc.findElementByName

        NamePattern[LineSegment::class]!![0].find(shapeName)?.let { matchResult ->
            if (matchResult.groups.size == 3) {  // a line segment must have two matching group, each group represent a point
                val f: (Int) -> Point = { i ->
                    val pName = matchResult.groupValues[i]
                    val point = doc.findElementByName(pName, Point::class, ctIdx)
                    point ?: throw ElementNotExistInDocumentException("Not found point $pName")
                }

                val p1 = f(1)
                val p2 = f(2)

                if (clazz == Ray::class) {
                    val line = RayImpl(doc, shapeName, p1, p1.coordinates().vectorTo(p2.coordinates()), p2)
                    val cr = ConstructionResultImpl<Ray>()
                    cr.setResult(line)
                    cr.addDependency(p1, listOf(), true)
                    cr.addDependency(p2, listOf(), true)
                    return cr as ConstructionResult<T>
                }

                if (clazz == VectorVi::class) {
                    val line = VectorImpl(doc, shapeName, p1, p2, p1.coordinates().vectorTo(p2.coordinates()))
                    val cr = ConstructionResultImpl<VectorVi>()
                    cr.setResult(line)
                    cr.addDependency(p1, listOf(), true)
                    cr.addDependency(p2, listOf(), true)
                    return cr as ConstructionResult<T>
                }

                if (clazz == LineSegment::class) {
                    val line = LineSegmentImpl(doc, shapeName, p1, p2)
                    val cr = ConstructionResultImpl<LineSegment>()
                    cr.setResult(line)
                    cr.addDependency(p1, listOf(), true)
                    cr.addDependency(p2, listOf(), true)
                    return cr as ConstructionResult<T>
                }

                if (clazz == LineVi::class) {
                    val line = LineImpl(doc, shapeName, p1, createVectorByEl(doc,p1, p2), p2)
                    val cr = ConstructionResultImpl<LineVi>()
                    cr.setResult(line)
                    cr.addDependency(p1, listOf(), true)
                    cr.addDependency(p2, listOf(), true)
                    return cr as ConstructionResult<T>
                }

                val line = LineSegmentImpl(doc, shapeName, p1, p2)
                val cr = ConstructionResultImpl<LineSegment>()
                cr.setResult(line)
                cr.addDependency(p1, listOf(), true)
                cr.addDependency(p2, listOf(), true)
                return cr as ConstructionResult<T>
            }
        }

        throw ElementNotExistInDocumentException("No line with name $shapeName could be found or constructed")
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(LineVi::class, LineSegment::class, Ray::class, VectorVi::class)
    }
}
