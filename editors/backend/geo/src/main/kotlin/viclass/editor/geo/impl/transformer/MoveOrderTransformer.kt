package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.transformdata.MoveOrderTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.impl.constructor.rotate
import viclass.editor.geo.transformer.Transformer

/**
 * Param store is a position = \[x, y, z\]
 * Move the target points in order around a center based on the new position (pos).
 * - center: rotation center
 * - source: original starting position
 * - targetParamIdx: list of indices of the point parameters to move
 */
class MoveOrderTransformer() : Transformer<MoveOrderTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: MoveOrderTransformData, pos: Vector3D) {
        val center = Vector3D.of(transformData.center) // Rotation center
        val source = Vector3D.of(transformData.source) // Original position
        val vectorC0 = center.vectorTo(Vector3D.of(center.x + 10, center.y, center.z)) // Reference vector along Ox axis
        val vectorCS = center.vectorTo(source) // Vector from center to source
        val vectorCPos = center.vectorTo(pos) // Vector from center to new position
        val angleP = vectorC0.angleTo(vectorCS) // Angle from Ox axis to source
        val anglePos = vectorC0.angleTo(vectorCPos) // Angle from Ox axis to pos
        val angleDelta = anglePos - angleP // Angle difference needed to rotate
        val unitVectorPos = vectorCPos.normalize() // Unit vector toward pos
        val k = vectorCPos.x / unitVectorPos.x // Magnitude of vector from center to pos

        transformData.targetParamIdx.forEach { paramIdx ->
            val ps = c.params[paramIdx].specs.getParam(ParamKind.PK_Value) as ParamStoreArray
            // Get current position of the point
            val p =
                Vector3D.of(ps.values[0].toDouble(), ps.values[1].toDouble(), ps.values.getOrNull(2)?.toDouble() ?: .0)
            val rotateP1 = p.rotate(angleDelta, center) // Rotate point around center
            val unitVectorNewP1 = center.vectorTo(rotateP1).normalize() // New unit vector after rotation
            val p1New = unitVectorNewP1.multiply(k).add(center) // Calculate new position according to new radius
            ps.values = p1New.toArray().map { it.toString() } // Update with new values
        }
    }
}
