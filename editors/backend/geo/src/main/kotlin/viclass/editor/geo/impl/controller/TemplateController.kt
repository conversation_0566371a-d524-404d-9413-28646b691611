package viclass.editor.geo.impl.controller

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import viclass.editor.geo.entity.ConstraintTemplate
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.impl.cache.MappingTextCache
import viclass.editor.geo.impl.koin.CONSTRUCTOR_TEMPLATES
import viclass.editor.geo.models.request.FetchTemplateRequest
import viclass.editor.geo.models.response.ConstraintTemplateModel
import viclass.editor.geo.utils.removeAccent

/**
 *
 * <AUTHOR>
 */
@Singleton(binds = [TemplateController::class])
class TemplateController constructor(
    @Named(CONSTRUCTOR_TEMPLATES) private val ctpl: Map<String, ConstructorTemplate>,
    private val mappingTextCache: MappingTextCache,
) : Logging {

    suspend fun reloadMappingTextCache() = withContext(Dispatchers.IO) {
        mappingTextCache.reload()
    }

    suspend fun getStringById(call: ApplicationCall) {
        val lang = call.request.headers["lang"] ?: "en"
        val strIds: List<String> = call.receive()
        val mapping = mappingTextCache.get(lang)
        val res = strIds.associateWith { mapping.getOrDefault(it, "_") }
        return call.respond(res)
    }

    //TODO using regex is not optimal. Searching keyword should be outsource to a DB or specialized searching tool
    suspend fun fetch(call: ApplicationCall) {
        try {
            val lang = call.request.headers["lang"] ?: "en"
            val request: FetchTemplateRequest = call.receive()

            val mapping = mappingTextCache.get(lang)

            val r = ctpl.values.asSequence().map { constructor ->
                if (constructor.constructor.template().elTypes.map { it.simpleName!! }.contains(request.objType))
                    constructor.cgs.filter { !it.invisible }.map { ConstraintTemplateModel(constructor.id) to it }.toList()
                else listOf()
            }.flatten().map { (res, cg) ->
                res.cgId = cg.name
                res.numConstraint = cg.params.size
                var params = mutableListOf(*cg.params.toTypedArray(), *cg.optionalParams.toTypedArray())
                val tplStrs = params.map { it.tplStrLangIds }.flatten()
                val reqTplStrs = request.selectedTpl.map { it.template }
                if ((cg.numDim == null || request.numDim == cg.numDim!!) &&
                    (reqTplStrs.isEmpty() || tplStrs.isEmpty() || tplStrs.size >= reqTplStrs.size && tplStrs.containsAll(reqTplStrs))
                ) {
                    params = mutableListOf(*cg.params.toTypedArray())
                    val optionalParams = mutableListOf(*cg.optionalParams.toTypedArray())
                    val selectedParams = mutableListOf<ConstraintTemplate>()

                    reqTplStrs.forEach { rq->
                        var idx = params.indexOfFirst { it.tplStrLangIds.contains(rq) }
                        if (idx > -1) selectedParams.add(params.removeAt(idx))
                        idx = optionalParams.indexOfFirst { it.tplStrLangIds.contains(rq) }
                        if (idx > -1) selectedParams.add(optionalParams.removeAt(idx))
                    }

                    optionalParams.removeIf { c ->
                        if (c.hidden) true
                        else if (c.dependencies.isEmpty())  false
                        else !selectedParams.map { it.indexInCG }.containsAll(c.dependencies)
                    }

                    params.removeIf { c ->
                        if (c.hidden) true
                        else if (c.dependencies.isEmpty())  false
                        else !selectedParams.map { it.indexInCG }.containsAll(c.dependencies)
                    }

                    mutableListOf(*params.toTypedArray(), *optionalParams.toTypedArray()).map { res.clone() to it }

                } else emptyList()
            }.flatten().map { (res, template) ->
                res.indexInCG = template.indexInCG
                res.paramDefId = template.paramDefId
                res.optional = template.optional
                if (template.tplStrLangIds.isNotEmpty()) {
                    template.tplStrLangIds.map {
                        val clone = res.clone()
                        clone.tplDescription = template.tplDescription.map { mapping.getOrDefault(it, "_") }
                        clone to it
                    }
                } else listOf(res.clone() to "")
            }.flatten().map { (res, tplStrId) ->
                if (tplStrId.isBlank()) return@map res
                val tplString = mapping[tplStrId]
                if (tplString != null) {
                    res.tplStringId = tplStrId
                    res.tplString = tplString
                    res.constraintString = tplString
                    val rgx = Regex("(\\{.*?})|(\\[.*?])")
                    res.tplDescription.forEach {
                        res.constraintString = res.constraintString.replaceFirst(rgx, it)
                    }
                    res.constraintString = res.constraintString.removeAccent()
                    res
                } else null
            }.filterNotNull().filter { res ->
                if (request.keywords.isBlank() || res.tplStringId.isBlank()) {
                    res.noMatch = 1
                    return@filter true
                }

                val c = request.keywords.split("\\s+".toRegex()).count {
                    it.isNotBlank() && res.constraintString.contains(it.removeAccent(), ignoreCase = true)
                }

                res.noMatch = c
                c > 0
            }.sortedByDescending {
                it.noMatch
            }.groupBy { it.constraintString }

            if (request.keywords.isBlank())
                call.respond(r)
            else
                call.respond(r.toList().take(10).toMap())

        } catch (t: Throwable) {
            logger.error("fetch constraint template exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }
}
