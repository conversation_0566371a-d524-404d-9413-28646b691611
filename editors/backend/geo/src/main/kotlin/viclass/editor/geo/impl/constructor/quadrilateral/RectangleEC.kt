package viclass.editor.geo.impl.constructor.quadrilateral

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.dbentity.movement.path.MovementCirclePath
import viclass.editor.geo.dbentity.movement.path.MovementFreePath
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.transformdata.PointOnPolygonTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Rectangle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.constructor.Points.isNewPoint
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.RectangleImpl
import viclass.editor.geo.impl.transformer.PointOnRectangleTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class RectangleEC : ElementConstructor<Rectangle> {
    override fun outputType(): KClass<Rectangle> {
        return Rectangle::class
    }

    private enum class CGS { FromPoints }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this).cgs(
            ConstraintGroupBuilder.create().name(CGS.FromPoints.name).constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPointWithCoords]!!,
                emptyList(),
                "tpl-Points"
            )
                // clockwise
                .constraint(
                    1, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aBoolean]!!, emptyList(), ""
                )
                // first length / second length
                .constraint(
                    2, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aValue]!!, emptyList(), ""
                ).build()
        ).elTypes(Rectangle::class).build()
    }

    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<Rectangle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 3)
                constructFromPoints(doc, inputName, c)
            }
        }
    }

    private fun constructFromPoints(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<Rectangle> {
        val (points, isClockwise, ratio) = extractRectangleFromPointsParams(doc, c)

        // Get or generate input name and validate
        var generatedName = inputName ?: points.joinToString("") { it.name ?: "" }
        val inputNameSet = NamePattern.extractPointName(Rectangle::class, generatedName).toSet()
        require(inputNameSet.size == 4) { "Input name must contain exactly 4 point names" }
        generatedName = inputNameSet.joinToString("")

        if (!isValidRectangle(points)) {
            points.adjustRectanglePoints(doc, isClockwise, ratio)
        }
        if (!isValidSquare(points))
            throw ConstructionException("4 điểm không thỏa mãn hình chữ nhật")

        val fixedPointIndices = points.mapIndexed { idx, p -> idx }.filter { !isNewPoint(doc, points[it]) }
        when (fixedPointIndices.size) {
            0, 1 -> {
                points.forEachIndexed { idx, pt ->
                    if (isNewPoint(doc, pt)) {
                        pt.transformData = PointOnPolygonTransformData(idx)
                        pt.transformer = TransformMapping.fromClazz(PointOnRectangleTransformer::class)
                        pt.movementPath = MovementFreePath()
                        pt.usable = true
                    }
                }
            }

            2 -> {
                val fi1 = fixedPointIndices.first()
                val fi2 = fixedPointIndices.last()
                val f1 = points[fi1].coordinates()
                val f2 = points[fi2].coordinates()

                if (fi1.isAdjacentIndex(fi2, 3)) {
                    val v = f1.vectorTo(f2).rotateRight90()
                    points.forEachIndexed { idx, pt ->
                        if (isNewPoint(doc, pt)) {
                            pt.transformData = PointOnPolygonTransformData(idx)
                            pt.transformer = TransformMapping.fromClazz(PointOnRectangleTransformer::class)
                            pt.movementPath = MovementLinePath(pt.coordinates().toArray(), v.toArray())
                            pt.usable = true
                        }
                    }
                } else {
                    val center = f1.add(f1.vectorTo(f2) * 0.5)
                    val r = f1.vectorTo(center).norm()
                    points.forEachIndexed { idx, pt ->
                        if (isNewPoint(doc, pt)) {
                            pt.transformData = PointOnPolygonTransformData(idx)
                            pt.transformer = TransformMapping.fromClazz(PointOnRectangleTransformer::class)
                            pt.movementPath = MovementCirclePath(center.toArray(), r)
                            pt.usable = true
                        }
                    }
                }
            }

            else -> {
                // More than 2 fixed points, no transformer needed
            }
        }

        val rectangle = RectangleImpl(doc, generatedName, points[0], points[1], points[2], points[3])
        // Optionally set transformData, transformer, movementPath for rectangle itself if needed
        return ConstructionResultImpl<Rectangle>().apply {
            addDependencies(points, true)
            setResult(rectangle)
        }
    }
}
