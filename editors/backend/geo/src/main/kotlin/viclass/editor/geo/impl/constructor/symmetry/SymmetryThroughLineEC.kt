package viclass.editor.geo.impl.constructor.symmetry

import kotlin.reflect.KClass
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementExtraction
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.PolygonImpl

/**
 *
 * <AUTHOR>
 */
@Singleton
class SymmetryThroughLineEC() : ElementConstructor<Element> {
    private enum class CGS {
        Point,
        Line,
        Circle,
        Ellipse,
        CircularSector,
        Polygon,
        Vector,
        Ray
    }

    @Suppress("UNCHECKED_CAST")
    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Element> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.Point -> {
                Validations.validateNumConstraints(c, 2)
                constructVertex(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.Line, CGS.Vector, CGS.Ray -> {
                Validations.validateNumConstraints(c, 2)
                constructLine(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.Circle -> {
                Validations.validateNumConstraints(c, 2)
                constructCircle(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.Ellipse -> {
                Validations.validateNumConstraints(c, 2)
                constructEllipse(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.CircularSector -> {
                Validations.validateNumConstraints(c, 2)
                constructCircularSector(doc, inputName, c) as ConstructionResult<Element>
            }
            CGS.Polygon -> {
                Validations.validateNumConstraints(c, 2)
                constructPolygon(doc, inputName, c) as ConstructionResult<Element>
            }
        }
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create().name(CGS.Point.name).constraintDefault().build(),
            )
            .elTypes(Element::class)
            .build()
    }

    override fun outputType(): KClass<Element> {
        return Element::class
    }

    private fun constructVertex(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val line = exr1.result.result()!!
        val point = exr2.result.result()!!

        val symmetricPoint =
            symmetricPointByLine(
                doubleArrayOf(point.coordinates().x, point.coordinates().y),
                doubleArrayOf(line.p1.coordinates().x, line.p1.coordinates().y),
                doubleArrayOf(line.parallelVector.x, line.parallelVector.y)
            )

        val cr: ConstructionResultImpl<Point> = createSymmetricPoint(doc, symmetricPoint, inputName)
        cr.addDependency(line, emptyList(), true)
        cr.addDependency(point, emptyList(), true)

        return cr
    }

    private fun constructLine(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<LineVi> {
        val cr: ConstructionResultImpl<LineVi>

        val symExr: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val targetExr: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val symLine = symExr.result.result()!!
        var targetLine = targetExr.result.result()!!

        val pointNames =
            if (inputName != null) NamePattern.extractPointName(LineSegment::class, inputName)
            else {
                val result = arrayListOf<String>()
                result.add(generatePointName(doc))
                result.add(generatePointName(doc, result))
                result
            }

        when (targetLine.clazz) {
            LineSegment::class -> {
                targetLine = targetLine as LineSegment

                val newPoints: List<DoubleArray> =
                    arrayListOf(targetLine.p1, targetLine.p2).map { vertex ->
                        symmetricPointByLine(
                            doubleArrayOf(vertex.coordinates().x, vertex.coordinates().y),
                            doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                            doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
                        )
                    }
                cr = createSymmetricLineSegment(doc, newPoints[0], newPoints[1], pointNames[0], pointNames[1])
            }
            VectorVi::class -> {
                targetLine = targetLine as VectorVi

                val symmetricPoint1 =
                    symmetricPointByLine(
                        doubleArrayOf(targetLine.p1.coordinates().x, targetLine.p1.coordinates().y),
                        doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                        doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
                    )
                val symmetricPoint2 =
                    symmetricPointByLine(
                        doubleArrayOf(targetLine.p2.coordinates().x, targetLine.p2.coordinates().y),
                        doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                        doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
                    )

                cr = createSymmetricVector(doc, symmetricPoint1, symmetricPoint2, pointNames[0], pointNames[1])
            }
            Ray::class -> {
                targetLine = targetLine as Ray

                val symmetricPoint1 =
                    symmetricPointByLine(
                        doubleArrayOf(targetLine.p1.coordinates().x, targetLine.p1.coordinates().y),
                        doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                        doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
                    )

                val symmetricPoint2 =
                    if (targetLine.p2 != null)
                        symmetricPointByLine(
                            doubleArrayOf(targetLine.p2!!.coordinates().x, targetLine.p2!!.coordinates().y),
                            doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                            doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
                        )
                    else null

                cr =
                    createSymmetricRay(
                        doc,
                        targetLine,
                        symmetricPoint1,
                        symmetricPoint2,
                        pointNames[0],
                        if (targetLine.p2 != null) pointNames[1] else null
                    )
            }
            else -> {
                val symmetricPoint1 =
                    symmetricPointByLine(
                        doubleArrayOf(targetLine.p1.coordinates().x, targetLine.p1.coordinates().y),
                        doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                        doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
                    )
                val symmetricPoint2 =
                    if (targetLine.p2 != null)
                        symmetricPointByLine(
                            doubleArrayOf(targetLine.p2!!.coordinates().x, targetLine.p2!!.coordinates().y),
                            doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                            doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
                        )
                    else null
                cr =
                    createSymmetricLineVi(
                        doc,
                        targetLine,
                        symmetricPoint1,
                        symmetricPoint2,
                        pointNames[0],
                        pointNames[1]
                    )
            }
        }

        cr.addDependency(symLine, emptyList(), true)
        cr.addDependency(targetLine, emptyList(), true)
        return cr
    }

    private fun constructCircle(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Circle> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Circle> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val symLine = exr1.result.result()!!
        val targetCircle = exr2.result.result()!!

        val symmetricCenter =
            symmetricPointByLine(
                doubleArrayOf(targetCircle.centerPoint.coordinates().x, targetCircle.centerPoint.coordinates().y),
                doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
            )

        val name = inputName ?: generatePointName(doc)
        val cr: ConstructionResultImpl<Circle> = createSymmetricCircle(doc, targetCircle.radius, symmetricCenter, name)
        cr.addDependency(symLine, emptyList(), true)
        cr.addDependency(targetCircle, emptyList(), true)
        return cr
    }

    private fun constructEllipse(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Ellipse> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Ellipse> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val symLine = exr1.result.result()!!
        val targetEllipse = exr2.result.result()!!

        val p1 = targetEllipse.f1
        val p2 = targetEllipse.f2

        val symmetricF1 =
            symmetricPointByLine(
                doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
                doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
            )
        val symmetricF2 =
            symmetricPointByLine(
                doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
                doubleArrayOf(symLine.p1.coordinates().x, symLine.p1.coordinates().y),
                doubleArrayOf(symLine.parallelVector.x, symLine.parallelVector.y)
            )

        val pointNames: List<String> =
            if (inputName != null) {
                NamePattern.extractPointName(Ellipse::class, inputName)
            } else {
                val p1Name = generatePointName(doc)
                val p2Name = generatePointName(doc, p1Name)
                listOf(p1Name, p2Name)
            }

        val cr: ConstructionResultImpl<Ellipse> =
            createSymmetricEllipse(
                doc,
                targetEllipse.a,
                targetEllipse.b,
                symmetricF1,
                symmetricF2,
                pointNames[0],
                pointNames[1]
            )
        cr.addDependency(symLine, emptyList(), true)
        cr.addDependency(targetEllipse, emptyList(), true)

        return cr
    }

    private fun constructCircularSector(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<CircularSector> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<CircularSector> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val line1 = exr1.result.result()!!
        val circulaSector = exr2.result.result()!!

        val p1 = circulaSector.centerPoint
        val p2 = circulaSector.endPoint
        val p3 = circulaSector.startPoint

        val symmetricPoint1 =
            symmetricPointByLine(
                doubleArrayOf(p1.coordinates().x, p1.coordinates().y),
                doubleArrayOf(line1.p1.coordinates().x, line1.p1.coordinates().y),
                doubleArrayOf(line1.parallelVector.x, line1.parallelVector.y)
            )
        val symmetricPoint2 =
            symmetricPointByLine(
                doubleArrayOf(p2.coordinates().x, p2.coordinates().y),
                doubleArrayOf(line1.p1.coordinates().x, line1.p1.coordinates().y),
                doubleArrayOf(line1.parallelVector.x, line1.parallelVector.y)
            )

        val symmetricPoint3 =
            symmetricPointByLine(
                doubleArrayOf(p3.coordinates().x, p3.coordinates().y),
                doubleArrayOf(line1.p1.coordinates().x, line1.p1.coordinates().y),
                doubleArrayOf(line1.parallelVector.x, line1.parallelVector.y)
            )

        val pointNames =
            if (inputName != null) NamePattern.extractPointName(CircularSector::class, inputName)
            else listOf(p1.name, p2.name, p3.name)
        val cr: ConstructionResultImpl<CircularSector> =
            createSymmetricCircularSector(
                doc,
                symmetricPoint1,
                symmetricPoint2,
                symmetricPoint3,
                pointNames[0],
                pointNames[1],
                pointNames[2]
            )
        cr.addDependency(line1, emptyList(), true)
        cr.addDependency(circulaSector, emptyList(), true)

        return cr
    }

    private fun constructPolygon(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Polygon> {
        val cr = ConstructionResultImpl<Polygon>()

        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Polygon> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)

        val line1 = exr1.result.result()!!
        val polygon = exr2.result.result()!!

        val pointNames =
            if (inputName != null) NamePattern.extractPointName(Polygon::class, inputName)
            else {
                val result = arrayListOf<String>()
                polygon.vertices().forEach { result.add(generatePointName(doc, result)) }
                result
            }

        val newPoints =
            polygon.vertices().mapIndexed { index, vertex ->
                val symmetricPoint1 =
                    symmetricPointByLine(
                        doubleArrayOf(vertex.coordinates().x, vertex.coordinates().y),
                        doubleArrayOf(line1.p1.coordinates().x, line1.p1.coordinates().y),
                        doubleArrayOf(line1.parallelVector.x, line1.parallelVector.y)
                    )
                PointImpl(doc, pointNames[index], symmetricPoint1[0], symmetricPoint1[1])
            }

        val newPolygon = PolygonImpl(doc, c.elName!!, newPoints)
        cr.setResult(newPolygon)

        newPoints.forEach { cr.addDependency(it, emptyList(), true) }

        cr.addDependency(line1, emptyList(), true)
        cr.addDependency(polygon, emptyList(), true)

        return cr
    }
}
