package viclass.editor.geo.impl.constructor.point

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.anEllipse
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class PointOnEllipseEC : ElementConstructor<Point> {
    private enum class CGS {
        OnEllipse, OnEllipseWithDegree, OnEllipseWithRadian
    }

    override fun outputType(): KClass<Point> {
        return Point::class
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.OnEllipse.name)
                    .hints("PointOnEllipse")
                    .hints("Point")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[anEllipse]!!,
                        listOf("NameOfEllipse"),
                        "tpl-OnEllipse"
                    )
                    .constraintOptional(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Degree", "Degree"),
                        "tpl-RangeDegree"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Radian", "Radian"),
                        "tpl-RangeRadian"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnEllipseWithDegree.name)
                    .hints("PointOnEllipseWithDegree")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[anEllipse]!!,
                        listOf("NameOfEllipse"),
                        "tpl-OnEllipse"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Degree"),
                        "tpl-AngleDegree"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnEllipseWithRadian.name)
                    .hints("PointOnEllipseWithRadian")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[anEllipse]!!,
                        listOf("NameOfEllipse"),
                        "tpl-OnEllipse"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Radian"),
                        "tpl-AngleRadian"
                    )
                    .build(),
            )
            .elTypes(Point::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        return when(CGS.valueOf(c.cgName)) {
            CGS.OnEllipse -> {
                Validations.validateNumConstraints(c, 1)
                constructOnEllipse(doc, inputName, c)
            }

            CGS.OnEllipseWithDegree, CGS.OnEllipseWithRadian -> {
                Validations.validateNumConstraints(c, 2)
                constructOnEllipseWithAngle(doc, inputName, c)
            }
        }
    }

    private fun constructOnEllipse(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val result = ConstructionResultImpl<Point>()

        var ellipse: Ellipse? = null
        var angle: Double? = null

        c.params.forEach {
            when (it.paramDef.id) {
                anEllipse -> {
                    val extractedEllipseResult = extractFirstPossible<ElementExtraction<Ellipse>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    ellipse = extractedEllipseResult.result.result()!!
                    result.mergeAsDependency(extractedEllipseResult.result)
                }

                aValue -> if (it.specs.indexInCG == 1) {
                    val extractedRangeResult = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, it, c.ctIdx)
                    angle = radian(extractedRangeResult.result)
                } else if (it.specs.indexInCG == 2) {
                    val extractedRangeResult = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, it, c.ctIdx)
                    angle = extractedRangeResult.result
                }
            }
        }

        val name = inputName ?: generatePointName(doc, ellipse!!.name!!)
        angle ?: throw ConstructionException("missing angle value")
        val point = Ellipses.calculatePointOnEllipseWithRadian(doc, name, ellipse!!, angle!!)

        result.setResult(point)

        return result
    }

    private fun constructOnEllipseWithAngle(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val result = ConstructionResultImpl<Point>()

        var ellipse: Ellipse? = null
        var angle: Double? = null

        c.params.forEach {
            when (it.paramDef.id) {
                anEllipse -> {
                    val extractedEllipseResult = extractFirstPossible<ElementExtraction<Ellipse>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    ellipse = extractedEllipseResult.result.result()!!
                    result.mergeAsDependency(extractedEllipseResult.result)
                }

                aValue -> {
                    val extractedRangeResult = extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, it, c.ctIdx)
                    angle = extractedRangeResult.result
                }
            }
        }

        if (ellipse == null || angle == null) {
            throw ConstructionException("missing ellipse or angle")
        }

        if(c.cgName == CGS.OnEllipseWithDegree.name) angle = radian(angle!!)

        val name = inputName ?: generatePointName(doc)

        val point = Ellipses.calculatePointOnEllipseWithRadian(doc, name, ellipse!!, angle!!)

        result.setResult(point)

        return result
    }
}
