package viclass.editor.geo.impl.elements

import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.CircularSector
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.math.MFunc
import kotlin.math.PI
import kotlin.math.abs
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
open class CircularSectorImpl(
    override val doc: GeoDoc,
    override var name: String?,
    private var _centerPoint: Point,
    private var _startPoint: Point,
    private var _endPoint: Point,
) : CircularSector {
    override val clazz: KClass<out CircularSector> = CircularSector::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true

    override val centerPoint: Point
        get() = _centerPoint
    override val startPoint: Point
        get() = _startPoint
    override val endPoint: Point
        get() = _endPoint

    override val radius: Double
        get() = Distances.of(startPoint, centerPoint)

    override fun validate() {
        val d1 = Distances.of(startPoint, centerPoint)
        val d2 = Distances.of(startPoint, centerPoint)
        if (abs(d1 - d2) > DEFAULT_TOLERANCE) throw InvalidElementException("$name is not a circular sector")
    }

    override fun parametricFunc(dim: Dimension): MFunc {
        TODO("Not yet implemented")
    }

    override fun vertices(): List<Point> {
        return listOf(startPoint, endPoint)
    }

    override fun area(): Double {
        val angle = createVectorByEl(doc, centerPoint, startPoint)
            .angleTo(createVectorByEl(doc, centerPoint, endPoint))
        return radius * radius * PI * angle / (2 * PI)
    }

    override fun length(): Double {
        val angle = createVectorByEl(doc, centerPoint, startPoint)
            .angleTo(createVectorByEl(doc, centerPoint, endPoint))
        return 2 * radius * PI * angle / (2 * PI)
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(CircularSector::class, name) || this.name == null) return false
        return NamePattern.extractPointName(CircularSector::class, this.name!!).toSet() ==
                NamePattern.extractPointName(CircularSector::class, name).toSet()
    }

    override fun mergeFrom(other: Element) {
        if (other !is CircularSector) return
        super.mergeFrom(other)

    }
}
