package viclass.editor.geo.impl.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.locationtech.jts.geom.Coordinate
import org.locationtech.jts.geom.GeometryFactory
import org.locationtech.jts.geom.PrecisionModel
import org.locationtech.jts.triangulate.DelaunayTriangulationBuilder
import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
open class PolygonImpl constructor(
    override val doc: GeoDoc, override var name: String?,
    private var _points: List<Point>,
) : Polygon {
    override val clazz: KClass<out Polygon> = Polygon::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true

    val points: List<Point>
        get() = _points

    override fun vertices(): List<Point> {
        return points.drop(0)
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Polygon::class, name)) return false
        val a = NamePattern.extractPointName(Polygon::class, this.name!!)
        val b = NamePattern.extractPointName(Polygon::class, name)
        if (a.size != b.size || a.isEmpty()) return false
        fun match(x: List<String>, y: List<String>) =
            (x + x).windowed(x.size).any { it == y } || (x.reversed() + x.reversed()).windowed(x.size).any { it == y }
        return match(a, b)
    }

    /**
     * Calculates the perimeter of the shape defined by the list of points.
     *
     * The perimeter is the total distance around the shape, calculated by summing
     * the distances between consecutive points. If the shape has fewer than 2 points,
     * the perimeter is considered to be 0.0.
     *
     * @return The perimeter of the shape as a Double.
     */
    override fun perimeter(): Double {
        return if (points.size < 2) 0.0 // Return 0 if there are less than 2 points (no perimeter)
        else points.indices.sumOf { // Sum the distances between consecutive points
            Distances.of(
                points[it],
                points[(it + 1) % points.size]
            ) // Calculate distance between current and next point
        }
    }

    private fun calculateArea(vertices3D: Collection<Vector3D>): Double {
        val precisionModel = PrecisionModel(DEFAULT_TOLERANCE)
        val geomFactory = GeometryFactory(precisionModel)
        val coordinates: MutableList<Coordinate> = ArrayList()

        vertices3D.forEach {
            coordinates.add(Coordinate(it.x, it.y))
        }

        // Perform Delaunay triangulation
        val builder = DelaunayTriangulationBuilder()
        builder.setSites(geomFactory.createMultiPointFromCoords(coordinates.toTypedArray()))

        val triangles = builder.getTriangles(geomFactory)

        // Calculate the area of the triangulated polygon
        var totalArea = 0.0
        for (i in 0 until triangles.numGeometries) {
            totalArea += triangles.getGeometryN(i).area
        }

        return totalArea
    }

    override fun area(): Double {
        return calculateArea(points.map { it.coordinates() })
    }

    override fun mergeFrom(other: Element) {
        if (other !is PolygonImpl) return
        super.mergeFrom(other)
    }
}
