package viclass.editor.geo.impl.db

import com.mongodb.client.model.Filters
import com.mongodb.client.model.Filters.eq
import com.mongodb.client.model.FindOneAndUpdateOptions
import com.mongodb.client.model.ReturnDocument
import com.mongodb.client.model.Updates
import com.mongodb.client.result.DeleteResult
import com.mongodb.client.result.InsertOneResult
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.reactive.awaitFirst
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.withContext
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.DocRenderProp
import viclass.editor.geo.dbentity.GeoDocument
import viclass.editor.geo.dbentity.RenderProp

interface GeoDocumentDBS {

    suspend fun insert(document: GeoDocument): InsertOneResult

    suspend fun replace(document: GeoDocument): UpdateResult

    suspend fun fetch(id: String): GeoDocument?

    suspend fun updateDocSize(docId: String, width: Double, height: Double): UpdateResult

    suspend fun updateDocState(
        docId: String,
        docRenderProp: DocRenderProp
    ): GeoDocument?

    suspend fun updateDocDefaultElRenderProps(
        docId: String,
        docDefaultElRenderProp: DocDefaultElRenderProp
    ): GeoDocument?

    suspend fun deleteDoc(docId: String): DeleteResult

    suspend fun insertDocuments(documents: List<GeoDocument>): List<String>
}

infix fun String.isEqualTo(value: Any): Bson {
    return eq(this, value)
}

class GeoDocumentDBSImpl constructor(
    private var col: MongoCollection<GeoDocument>
) : GeoDocumentDBS, Logging {
    override suspend fun insert(document: GeoDocument): InsertOneResult {
        return col.insertOne(document).awaitFirst()
    }

    override suspend fun replace(document: GeoDocument): UpdateResult {
        val filter = Filters.and(
            eq("_id", ObjectId(document.id)),
        )
        return col.replaceOne(filter, document).awaitFirst()
    }

    override suspend fun fetch(id: String): GeoDocument? {
        return col.find("_id" isEqualTo ObjectId(id)).awaitFirstOrNull()
    }

    override suspend fun updateDocSize(docId: String, width: Double, height: Double): UpdateResult {
        val updates = listOf(
            Updates.set("${GeoDocument::docRenderProp.name}.${DocRenderProp::canvasWidth.name}", width),
            Updates.set("${GeoDocument::docRenderProp.name}.${DocRenderProp::canvasHeight.name}", height),
        )
        return col.updateOne("_id" isEqualTo ObjectId(docId), updates).awaitFirst()
    }

    override suspend fun updateDocState(
        docId: String,
        docRenderProp: DocRenderProp
    ): GeoDocument? {
        val updates = listOf(
            Updates.set(
                "${GeoDocument::docRenderProp.name}.${DocRenderProp::screenUnit.name}",
                docRenderProp.screenUnit
            ),
            Updates.set(
                "${GeoDocument::docRenderProp.name}.${DocRenderProp::canvasWidth.name}",
                docRenderProp.canvasWidth
            ),
            Updates.set(
                "${GeoDocument::docRenderProp.name}.${DocRenderProp::canvasHeight.name}",
                docRenderProp.canvasHeight
            ),
            Updates.set("${GeoDocument::docRenderProp.name}.${DocRenderProp::scale.name}", docRenderProp.scale),
            Updates.set(
                "${GeoDocument::docRenderProp.name}.${DocRenderProp::translation.name}",
                docRenderProp.translation
            ),
            Updates.set("${GeoDocument::docRenderProp.name}.${DocRenderProp::rotation.name}", docRenderProp.rotation),
            Updates.set("${GeoDocument::docRenderProp.name}.${DocRenderProp::axis.name}", docRenderProp.axis),
            Updates.set("${GeoDocument::docRenderProp.name}.${DocRenderProp::grid.name}", docRenderProp.grid),
            Updates.set(
                "${GeoDocument::docRenderProp.name}.${DocRenderProp::detailGrid.name}",
                docRenderProp.detailGrid
            ),
            Updates.set("${GeoDocument::docRenderProp.name}.${DocRenderProp::snapMode.name}", docRenderProp.snapMode),
            Updates.set(
                "${GeoDocument::docRenderProp.name}.${DocRenderProp::snapToExistingPoints.name}",
                docRenderProp.snapToExistingPoints
            ),
            Updates.set(
                "${GeoDocument::docRenderProp.name}.${DocRenderProp::snapToGrid.name}",
                docRenderProp.snapToGrid
            ),
            Updates.set(
                "${GeoDocument::docRenderProp.name}.${DocRenderProp::namingMode.name}",
                docRenderProp.namingMode
            ),
        )
        val option = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        return col.findOneAndUpdate("_id" isEqualTo ObjectId(docId), updates, option).awaitFirst()
    }

    override suspend fun updateDocDefaultElRenderProps(
        docId: String,
        docDefaultElRenderProp: DocDefaultElRenderProp
    ): GeoDocument? {
        val updates = listOf(
            Updates.set(
                "${GeoDocument::docDefaultElRenderProp.name}.${RenderProp::color.name}",
                docDefaultElRenderProp.color
            ),
            Updates.set(
                "${GeoDocument::docDefaultElRenderProp.name}.${RenderProp::lineColor.name}",
                docDefaultElRenderProp.lineColor
            ),
            Updates.set(
                "${GeoDocument::docDefaultElRenderProp.name}.${RenderProp::pointColor.name}",
                docDefaultElRenderProp.pointColor
            ),
            Updates.set(
                "${GeoDocument::docDefaultElRenderProp.name}.${RenderProp::opacity.name}",
                docDefaultElRenderProp.opacity
            ),
            Updates.set(
                "${GeoDocument::docDefaultElRenderProp.name}.${RenderProp::lineWeight.name}",
                docDefaultElRenderProp.lineWeight
            ),
            Updates.set(
                "${GeoDocument::docDefaultElRenderProp.name}.${RenderProp::strokeStyle.name}",
                docDefaultElRenderProp.strokeStyle
            ),
            Updates.set(
                "${GeoDocument::docDefaultElRenderProp.name}.${RenderProp::spaceFromArcToCorner.name}",
                docDefaultElRenderProp.spaceFromArcToCorner
            ),
        )
        val option = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        return col.findOneAndUpdate("_id" isEqualTo ObjectId(docId), updates, option).awaitFirst()
    }

    override suspend fun deleteDoc(docId: String): DeleteResult {
        return col.deleteOne("_id" isEqualTo ObjectId(docId)).awaitFirst()
    }

    override suspend fun insertDocuments(documents: List<GeoDocument>): List<String> {
        return withContext(Dispatchers.IO) { col.insertMany(documents) }.awaitFirst().insertedIds
            .map {
                it.value.asObjectId().value.toHexString()
            }
    }
}
