package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.CircularSector
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.Semicircle
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import kotlin.reflect.KClass

/**
 * A circular sector extractor extract circular sectors from given names. The name should have follow conventional format
 *
 * <AUTHOR>
 */
@Singleton
class CircularSectorExtractor(private val evaluator: Evaluator) : ParameterExtractor {

    override var id: String = javaClass.simpleName

    @Throws(ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(
        doc: GeoDoc,
        pk: String,
        ctIdx: Int,
        clazz: KClass<out T>?
    ): ConstructionResult<T> {
        val shapeName = evaluator.extractRootOperator(pk) { extractElementName(it, listOf("Hinh")) }
            ?: throw ElementNotExistInDocumentException("Invalid expression: $pk")

        doc.findElementByName(shapeName, CircularSector::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<CircularSector>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        doc.findElementByName(shapeName, Semicircle::class, ctIdx)?.let {
            val cr = ConstructionResultImpl<Semicircle>()
            cr.setResult(it)
            cr.newly = false
            return cr as ConstructionResult<T>
        }

        val matched = doc.elements
            .filterIsInstance<CircularSector>()
            .filter {
                NamePattern.isNameValid(CircularSector::class, shapeName) && NamePattern.extractPointName(CircularSector::class, shapeName).toSet() == NamePattern.extractPointName(
                    CircularSector::class,
                    "${it.startPoint.name}${it.centerPoint.name}${it.endPoint.name}"
                ).toSet() &&
                        it.valid &&
                        it.usable &&
                        it.deleted != true
            }

        if (matched.size == 1) {
            val cr = ConstructionResultImpl<CircularSector>()
            cr.setResult(matched.first())
            cr.newly = false
            cr as ConstructionResult<T>
            return cr
        }

        if (matched.size >= 2) {
            throw ElementNotExistInDocumentException(
                "Cannot proceed: multiple circles found with the same center point name '$shapeName'. Expected only one."
            )
        }

        val matched2 = doc.elements
            .filterIsInstance<Semicircle>()
            .filter {
                NamePattern.isNameValid(Semicircle::class, shapeName) && NamePattern.extractPointName(Semicircle::class, shapeName).toSet() == NamePattern.extractPointName(
                    Semicircle::class,
                    "${it.startPoint.name}${it.centerPoint.name}${it.endPoint.name}"
                ).toSet() &&
                        it.valid &&
                        it.usable &&
                        it.deleted != true
            }

        if (matched2.size == 1) {
            val cr = ConstructionResultImpl<Semicircle>()
            cr.setResult(matched2.first())
            cr.newly = false
            cr as ConstructionResult<T>
            return cr
        }

        if (matched2.size >= 2) {
            throw ElementNotExistInDocumentException(
                "Cannot proceed: multiple circular sector found with the same center point name '$shapeName'. Expected only one."
            )
        }

        throw ElementNotExistInDocumentException("No Circular Sector with name $shapeName could be found or constructed")
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(CircularSector::class, Semicircle::class)
    }
}
