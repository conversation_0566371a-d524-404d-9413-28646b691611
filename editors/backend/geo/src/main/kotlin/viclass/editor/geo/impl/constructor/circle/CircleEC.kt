package viclass.editor.geo.impl.constructor.circle

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.extractable.ValueExpression
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.CircleImpl
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class CircleEC : ElementConstructor<Circle>, Logging {

    override fun outputType(): KClass<Circle> {
        return Circle::class
    }

    private enum class CGS {
        CenterAndRadius, CenterAndRadiusLineSegment, DiameterLineSegment
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.CenterAndRadius.name)
                    .hints()
                    .constraint(
                        0, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-CenterCircle"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[ConstraintParamDefManager.lengthAssignment]!!,
                        listOf("Value"),
                        "tpl-Radius"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.CenterAndRadiusLineSegment.name)
                    .hints()
                    .constraint(
                        0, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-CenterCircle"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLineSegment]!!,
                        listOf("LineSegment"),
                        "tpl-RadiusLineSegment"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.DiameterLineSegment.name)
                    .hints()
                    .constraint(
                        0, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-DiameterLineSegment"
                    )
                    .build(),
            )
            .elTypes(Circle::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Circle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.CenterAndRadius -> {
                Validations.validateNumConstraints(c, 2)
                constructFromCenterRadius(doc, inputName, c)
            }

            CGS.CenterAndRadiusLineSegment -> {
                Validations.validateNumConstraints(c, 2)
                constructFromCenterAndLineSegmentRadius(doc, inputName, c)
            }

            CGS.DiameterLineSegment -> {
                Validations.validateNumConstraints(c, 1)
                constructDiameterLineSegment(doc, inputName, c)
            }
        }
    }

    private fun constructFromCenterRadius(doc: GeoDoc, name: String?, c: Construction): ConstructionResultImpl<Circle> {
        val cr = ConstructionResultImpl<Circle>()
        var center: Point? = null
        var radius: Double? = null
        var constructions: List<ConstructionResult<out Element>>? = null

        c.params.forEach {
            when (it.paramDef.id) {
                ConstraintParamDefManager.aPoint -> {
                    val extractionCenter =
                        extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    center = extractionCenter.result.result()
                    cr.mergeAsDependency(extractionCenter.result)
                }

                ConstraintParamDefManager.lengthAssignment -> {
                    val extractionRadius =
                        extractFirstPossible<ExtractableWithConstructions<ValueExpression>>(
                            doc,
                            ParamKind.PK_Expr,
                            it,
                            c.ctIdx
                        )
                    radius = extractionRadius.result.value
                    constructions = extractionRadius.result.constructions()
                }
            }
        }

        if (center == null || radius == null) {
            logger.error("cannot parse center or radius")
            throw ElementNotExistInDocumentException("Not enough parameters to construct the circle")
        }

        cr.setResult(CircleImpl(doc, name ?: center.name, center, radius))
        constructions!!.forEach { cr.mergeAsDependency(it) }

        return cr
    }

    private fun constructFromCenterAndLineSegmentRadius(
        doc: GeoDoc,
        name: String?,
        c: Construction
    ): ConstructionResultImpl<Circle> {
        val cr = ConstructionResultImpl<Circle>()
        var center: Point? = null
        var lineSegment: LineSegment? = null

        c.params.forEach {
            when (it.paramDef.id) {
                ConstraintParamDefManager.aPoint -> {
                    val extractionCenter =
                        extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    center = extractionCenter.result.result()
                    cr.mergeAsDependency(extractionCenter.result)
                }

                ConstraintParamDefManager.aLineSegment -> {
                    val extractionRadius =
                        extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    lineSegment = extractionRadius.result.result()
                }
            }
        }
        center ?: throw ElementNotExistInDocumentException("missing center point")
        lineSegment ?: throw ElementNotExistInDocumentException("missing line segment")

        val radius = lineSegment.length()
        cr.setResult(CircleImpl(doc, generateLowercaseName(doc,arrayListOf()), center, radius))
        cr.addDependency(lineSegment.p1, listOf(), true)
        cr.addDependency(lineSegment.p2, listOf(), true)
        return cr

    }

    private fun constructDiameterLineSegment(
        doc: GeoDoc, name: String?, c: Construction
    ): ConstructionResultImpl<Circle> {
        val extractionLineSegment =
            extractFirstPossible<ElementExtraction<LineSegment>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)

        val lineSegment = extractionLineSegment.result.result()!!

        var centerName: String? = null
        if (name != null) {
            NamePattern[Circle::class]!![0].find(name)?.let {
                centerName = it.groupValues[1]
            }
        }

        if (centerName == null) {
            centerName = generateCircleName(doc)
        }

        val center = Points.calculateCenterPoint(doc, centerName, lineSegment.p1, lineSegment.p2)

        val cr = ConstructionResultImpl<Circle>()
        cr.setResult(CircleImpl(doc, name ?: centerName, center, lineSegment.length() / 2))
        cr.addDependency(center, listOf(), true)
        cr.mergeAsDependency(extractionLineSegment.result)

        return cr
    }
}
