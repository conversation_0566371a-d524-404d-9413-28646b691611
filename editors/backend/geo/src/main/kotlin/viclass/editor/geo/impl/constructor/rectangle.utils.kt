package viclass.editor.geo.impl.constructor

import viclass.editor.geo.constructor.BooleanExtraction
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ElementListExtraction
import viclass.editor.geo.constructor.NumberExtraction
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.Points.isNewPoint
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.abs
import kotlin.math.atan
import kotlin.math.cos

fun extractRectangleFromPointsParams(
    doc: GeoDoc, c: Construction
): Triple<MutableList<Point>, Boolean, Double> {
    val targetParamIdx = 0
    val isClockwiseIdx = 1
    val ratioIdx = 2

    val points = extractFirstPossible<ElementListExtraction<Point>>(
        doc, ParamKind.PK_Value, c.params[targetParamIdx], c.ctIdx
    ).result.map { it.result() ?: throw IllegalArgumentException("Invalid point extraction") }.toMutableList()
    require(points.size == 4) { "Input must contain exactly 4 point names" }

    val isClockwise = extractFirstPossible<BooleanExtraction>(
        doc, ParamKind.PK_Value, c.params[isClockwiseIdx], c.ctIdx
    ).result

    val ratio = extractFirstPossible<NumberExtraction<Double>>(
        doc, ParamKind.PK_Value, c.params[ratioIdx], c.ctIdx
    ).result

    return Triple(points, isClockwise, ratio)
}

fun isValidRectangle(points: List<Point>): Boolean {
    if (points.size != 4) return false
    val dists = List(4) { i ->
        val j = (i + 1) % 4
        val dx = points[i].x - points[j].x
        val dy = points[i].y - points[j].y
        dx * dx + dy * dy
    }
    val oppDists = listOf(
        (points[0].x - points[2].x) * (points[0].x - points[2].x) + (points[0].y - points[2].y) * (points[0].y - points[2].y),
        (points[1].x - points[3].x) * (points[1].x - points[3].x) + (points[1].y - points[3].y) * (points[1].y - points[3].y)
    )
    // Check all sides are positive and opposite sides are equal
    if (dists.any { it < DEFAULT_TOLERANCE }) return false
    if (abs(dists[0] - dists[2]) > DEFAULT_TOLERANCE) return false
    if (abs(dists[1] - dists[3]) > DEFAULT_TOLERANCE) return false
    // Check diagonals are equal
    if (abs(oppDists[0] - oppDists[1]) > DEFAULT_TOLERANCE) return false
    // Check angles are right (dot product of adjacent sides is zero)
    for (i in 0..3) {
        val j = (i + 1) % 4
        val k = (i + 2) % 4
        val v1x = points[j].x - points[i].x
        val v1y = points[j].y - points[i].y
        val v2x = points[k].x - points[j].x
        val v2y = points[k].y - points[j].y
        val dot = v1x * v2x + v1y * v2y
        if (abs(dot) > DEFAULT_TOLERANCE) return false
    }
    return true
}

fun MutableList<Point>.adjustRectanglePoints(doc: GeoDoc, isClockwise: Boolean, ratio: Double, movedIdx: Int? = null) {
    val ps = map { it.coordinates() }.toMutableList()
    val fixed = (0..3).filter { !isNewPoint(doc, this[it]) }.toMutableList()
    if (fixed.size > 2) return

    // The moved point position is always correct because of the limited movement path so don't need to update it
    if (movedIdx != null) fixed.add(movedIdx)

    // Ensure we have at least two fixed points to guarantee a rotation axis
    // If only one point is fixed, we can use the opposite point as a reference
    if (fixed.size == 1) fixed.add((fixed.first() + 2).boundIndex(3))

    val needToUpdateIdx = (0..3).filter { it !in fixed }
    when (needToUpdateIdx.size) {
        1 -> {
            val idx = needToUpdateIdx.first() // Index of the point that needs to be updated

            val prev = ps[idx.prevIndex(3)]
            val next = ps[idx.nextIndex(3)]
            val opp = ps[(idx + 2).boundIndex(3)]

            ps[idx] = prev.add(opp.vectorTo(next))
        }

        2 -> {
            if (needToUpdateIdx.first().isAdjacentIndex(needToUpdateIdx.last(), 3)) {
                // Adjacent points

                // Always follow the isClockwise direction
                val (fi1, fi2) = if (fixed.first().nextIndex(3) == fixed.last()) fixed.first() to fixed.last()
                else fixed.last() to fixed.first()
                val f1ToF2 = ps[fi1].vectorTo(ps[fi2])

                if (fi1 == 0 || fi1 == 2) {
                    // f1ToF2 is the first or third side
                    val f1toP1 = (if (isClockwise) f1ToF2.rotateRight90() else f1ToF2.rotateLeft90()) / ratio
                    ps[fi1.prevIndex(3)] = ps[fi1].add(f1toP1)
                    ps[fi2.nextIndex(3)] = ps[fi2].add(f1toP1)
                } else {
                    // f1ToF2 is the second or fourth side
                    val f1toP1 = (if (isClockwise) f1ToF2.rotateRight90() else f1ToF2.rotateLeft90()) * ratio
                    ps[fi2.nextIndex(3)] = ps[fi2].add(f1toP1)
                    ps[fi1.prevIndex(3)] = ps[fi1].add(f1toP1)
                }
            } else {
                // Always follow the isClockwise direction
                val (fi1, fi2) = fixed.first() to fixed.last()
                val f1ToF2 = ps[fi1].vectorTo(ps[fi2])

                // Opposite points
                val ratio =
                    if (fi1 == 0 || fi1 == 2) 1 / ratio else ratio // Adjust ratio based on the fixed point position
                val f1ToNext =
                    (if (isClockwise) f1ToF2.rotateLeft(atan(ratio)) else f1ToF2.rotateRight(atan(ratio))) * cos(
                        atan(ratio)
                    )
                ps[fi1.nextIndex(3)] = ps[fi1].add(f1ToNext)
                ps[fi1.prevIndex(3)] = ps[fi2].add(f1ToNext.negate())
            }
        }

        else -> return
    }

    for (i in 0..3) {
        this[i] = PointImpl(
            this[i].doc, this[i].name, ps[i].x, ps[i].y, ps[i].z
        ).apply {
            transformData = this@adjustRectanglePoints[i].transformData
            transformer = this@adjustRectanglePoints[i].transformer
            movementPath = this@adjustRectanglePoints[i].movementPath
        }
    }
}