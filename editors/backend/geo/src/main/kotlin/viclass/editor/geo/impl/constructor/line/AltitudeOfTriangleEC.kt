package viclass.editor.geo.impl.constructor.line

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementExtraction
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Triangle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.extractors.LineExtractor
import viclass.editor.geo.impl.extractors.PointExtractor
import kotlin.reflect.KClass

@Singleton
class AltitudeOfTriangleEC(private val extractorManager: ParamExtractorManager) : ElementConstructor<LineVi> {

    private enum class CGS {
        AltitudeLineSegment, AltitudeLine
    }

    override fun template(): ConstructorTemplate {
        val cg1 = ConstraintGroupBuilder.create()
            .name(CGS.AltitudeLineSegment.name)
            .hints("hint-AltitudeOfTriangle")
            .constraint(
                0, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aTriangle]!!,
                listOf("NameOfTriangle"),
                "tpl-AltitudeOfTriangle"
            )
            .build()

        val cg2 = ConstraintGroupBuilder.create()
            .name(CGS.AltitudeLine.name)
            .hints("hint-AltitudeOfTriangleThroughVertex")
            .constraint(
                0, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aTriangle]!!,
                listOf("NameOfTriangle"),
                "tpl-AltitudeOfTriangle"
            )
            .constraint(
                1, ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            )
            .build()

        return ConstructorTemplateBuilder.create(this)
            .cgs(cg1, cg2)
            .elTypes(LineVi::class, LineSegment::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.AltitudeLineSegment -> {
                Validations.validateNumConstraints(c, 1)
                constructAltitudeLineSegment(doc, inputName, c)
            }

            CGS.AltitudeLine -> {
                Validations.validateNumConstraints(c, 2)
                constructAltitudeLine(doc, inputName, c)
            }
        }
    }

    private fun constructAltitudeLine(doc: GeoDoc, name: String?, c: Construction): ConstructionResult<LineVi> {
        val triangleER = extractFirstPossible<ElementExtraction<Triangle>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val triangle = triangleER.result.result()!!

        val pointER = extractFirstPossible<ElementExtraction<Point>>(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val fromVertex = pointER.result.result()!!

        if (!triangle.name!!.contains(fromVertex.name!!))
            throw ConstructionException("Vertex ${fromVertex.name} is not belong to triangle ${triangle.name}")

        val toLineName: String = triangle.name!!.replace(fromVertex.name!!, "")

        val extractorLineSegment = extractorManager[LineExtractor::class.simpleName!!]
        val toLineCR: ConstructionResult<LineVi> =
            extractorLineSegment.extractConstructionResult(doc, toLineName, c.ctIdx)
        val toLine = toLineCR.result()!!

        // calculate altitude line
        val altitudeVector = calculatePerpendicularVector(fromVertex.coordinates(), toLine.parallelVector)
        val altitudeLine = LineImpl(doc, name, fromVertex, altitudeVector)

        val altitudeV = Intersections.of(altitudeLine, toLine)
            ?: throw ConstructionException("there no altitude point")

        val cr = ConstructionResultImpl<LineVi>()
        if (c.entity.elType == LineSegment::class.simpleName) {
            val pn = name?.replace(fromVertex.name!!, "") ?: generatePointName(doc)
            val altitudePoint = PointImpl(doc, pn, altitudeV)
            val lineSegment = LineSegmentImpl(doc, name, fromVertex, altitudePoint)
            cr.setResult(lineSegment)
            cr.addDependency(altitudePoint, listOf(), true)
        } else if (c.entity.elType == LineVi::class.simpleName) {
            cr.setResult(altitudeLine)
        }
        cr.mergeAsDependency(triangleER.result)
        cr.addDependency(toLine, listOf(), true)
        cr.addDependency(fromVertex, listOf(), true)

        return cr
    }

    private fun constructAltitudeLineSegment(doc: GeoDoc, name: String?, c: Construction): ConstructionResult<LineVi> {
        val extractionResult =
            extractFirstPossible<ElementExtraction<Triangle>>(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val triangle = extractionResult.result.result()!!

        var fromVertexName: String? = null
        var toLineName: String? = null
        var altitudePointName: String? = null

        for (regex in NamePattern[LineSegment::class]!!) {
            if (regex.matches(name!!)) {
                val matchResult = regex.find(name)!!
                val p1 = matchResult.groups[1]?.value!!
                val p2 = matchResult.groups[2]?.value!!

                if (triangle.name!!.contains(p1) && !triangle.name!!.contains(p2)) {
                    fromVertexName = p1
                    toLineName = triangle.name!!.replace(p1, "")
                    altitudePointName = p2
                    break
                }

                if (!triangle.name!!.contains(p1) && triangle.name!!.contains(p2)) {
                    fromVertexName = p2
                    toLineName = triangle.name!!.replace(p2, "")
                    altitudePointName = p1
                    break
                }

                throw ConstructionException("Altitude line $name is not valid")
            }
        }

        if (fromVertexName == null) throw ConstructionException("Altitude line $name is not valid")
        if (doc.findElementByName(altitudePointName!!, Point::class, c.ctIdx) != null) {
            throw ElementExistInDocumentException("$altitudePointName is already exist")
        }

        val extractorLineSegment = extractorManager[LineExtractor::class.simpleName!!]
        val extractorPoint = extractorManager[PointExtractor::class.simpleName!!]

        val toLineCR: ConstructionResult<LineVi> =
            extractorLineSegment.extractConstructionResult(doc, toLineName!!, c.ctIdx)
        val fromVertexCR: ConstructionResult<Point> =
            extractorPoint.extractConstructionResult(doc, fromVertexName, c.ctIdx)

        val toLine = toLineCR.result()!!
        val fromVertex = fromVertexCR.result()!!

        // calculate altitude line
        val altitudeVector = calculatePerpendicularVector(fromVertex.coordinates(), toLine.parallelVector)
        val altitudeLine = LineImpl(doc, name, fromVertexCR.result()!!, altitudeVector)

        // calculate intersection
        val intersection = altitudeLine.line().intersection(toLine.line())
        val altitudePoint = PointImpl(doc, altitudePointName, intersection.x, intersection.y, intersection.z)

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(LineSegmentImpl(doc, name, fromVertex, altitudePoint))
        cr.mergeAsDependency(extractionResult.result)
        cr.mergeAsDependency(toLineCR)
        cr.mergeAsDependency(fromVertexCR)

        return cr
    }

    private fun calculatePerpendicularVector(point: Vector3D, line: Vector3D): Vector3D {
        val k = line.multiply(point.dot(line) / line.dot(line))

        return point.subtract(k)
    }

    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }
}
