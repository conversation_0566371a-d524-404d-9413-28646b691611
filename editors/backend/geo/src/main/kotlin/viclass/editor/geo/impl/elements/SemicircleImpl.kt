package viclass.editor.geo.impl.elements

import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Semicircle
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.impl.constructor.Points
import viclass.editor.geo.math.MFunc
import kotlin.math.PI
import kotlin.math.abs
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
class SemicircleImpl constructor(
    doc: GeoDoc, name: String?,
    private var _centerPoint: Point,
    private var _startPoint: Point,
    private var _endPoint: Point,
) : CircularSectorImpl(doc, name, _centerPoint, _startPoint, _endPoint), Semicircle {
    override val clazz: KClass<out Semicircle> = Semicircle::class

    override val centerPoint: Point
        get() = _centerPoint
    override val startPoint: Point
        get() = _startPoint
    override val endPoint: Point
        get() = _endPoint

    override fun validate() {
        if(!Points.isBetweenTwoPoints(centerPoint.coordinates(), startPoint.coordinates(), endPoint.coordinates()))
            throw InvalidElementException("$name is not a semicircle")
    }

    override fun parametricFunc(dim: Dimension): MFunc {
        TODO("Not yet implemented")
    }

    override fun vertices(): List<Point> {
        return listOf(startPoint, endPoint)
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Semicircle::class, name)) return false

        return this.name == name
    }

    override fun area(): Double {
        val r = startPoint.coordinates().distance(endPoint.coordinates()) / 2
        return r * r * PI / 2
    }

    override fun length(): Double {
        val r = startPoint.coordinates().distance(endPoint.coordinates()) / 2
        return 2 * r * PI / 2
    }

    override fun mergeFrom(other: Element) {
        if (other !is Semicircle) return
        super<CircularSectorImpl>.mergeFrom(other)

    }
}
