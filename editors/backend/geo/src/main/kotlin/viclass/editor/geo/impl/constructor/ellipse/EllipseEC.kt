package viclass.editor.geo.impl.constructor.ellipse

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementListExtraction
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generateLowercaseName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.EllipseImpl
import kotlin.math.sqrt
import kotlin.reflect.KClass

@Singleton
class EllipseEC() : ElementConstructor<Ellipse>, Logging {
    private enum class CGS { Points }

    override fun outputType(): KClass<Ellipse> {
        return Ellipse::class
    }

    override fun template(): ConstructorTemplate {
        val cg1 =
            ConstraintGroupBuilder.create()
                .name(CGS.Points.name)
                .hints()
                .constraint(
                    0,
                    ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                    listOf("NameOfPoint", "NameOfPoint", "NameOfPoint"),
                    "tpl-EllipseFocusPoints"
                )
                .build()

        return ConstructorTemplateBuilder.create(this).cgs(cg1).elTypes(Ellipse::class).build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Ellipse> {
        return when (val cg = CGS.valueOf(c.cgName)) {
            CGS.Points -> {
                Validations.validateNumConstraints(c, 1)
                constructEllipse(doc, inputName, c, cg)
            }
        }
    }

    private fun constructEllipse(doc: GeoDoc, name: String?, cst: Construction, cg: CGS): ConstructionResult<Ellipse> {
        val pointsER =
            extractFirstPossible<ElementListExtraction<Point>>(doc, ParamKind.PK_Name, cst.params[0], cst.ctIdx)
        val f1 = pointsER.result[0].result() ?: throw ElementNotExistInDocumentException("not found vertex")
        val f2 = pointsER.result[1].result() ?: throw ElementNotExistInDocumentException("not found vertex")
        val f3 = pointsER.result[2].result() ?: throw ElementNotExistInDocumentException("not found vertex")

        // Calculate distance between foci
        val c = Distances.of(f1, f2) / 2

        // Distance between f3 and each focus
        val d1 = sqrt((f3.x - f1.x) * (f3.x - f1.x) + (f3.y - f1.y) * (f3.y - f1.y))
        val d2 = sqrt((f3.x - f2.x) * (f3.x - f2.x) + (f3.y - f2.y) * (f3.y - f2.y))

        // Sum of distances from f3 to foci is 2a
        val a = (d1 + d2) / 2

        // Calculate b using a^2 = b^2 + c^2
        val b = sqrt(a * a - c * c)

        val cr = ConstructionResultImpl<Ellipse>()
        val ellipse = EllipseImpl(doc,  generateLowercaseName(doc,arrayListOf()), f1, f2, a, b)
        cr.setResult(ellipse)
        cr.addDependency(f1, emptyList(), true)
        cr.addDependency(f2, emptyList(), true)
        cr.addDependency(f3, emptyList(), true)

        return cr
    }
}
