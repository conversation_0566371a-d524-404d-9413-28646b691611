package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.geometry.euclidean.threed.line.Line3D
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point


/**
 *
 * <AUTHOR>
 */
object Distances {
    fun of(p1: Point, p2: Point): Double {
        return of(p1.coordinates(), p2.coordinates())
    }

    fun of(v1: Vector3D, v2: Vector3D): Double {
        return v1.distance(v2)
    }

    fun of(p: Point, line: LineVi): Double {
        return of(p.coordinates(), line.line())
    }

    fun of(v1: Vector3D, line: Line3D): Double {
        return line.distance(v1)
    }
}
