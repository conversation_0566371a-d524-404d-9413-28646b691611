package viclass.editor.geo.impl.elements

import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Angle
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.PlaneVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.math.MFunc
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
class PlaneImpl(override var doc: GeoDoc, override var name: String?) : PlaneVi {
    override val clazz: KClass<out PlaneVi> = PlaneVi::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true

    override fun contains(point: Point): Boolean {
        TODO("Not yet implemented")
    }

    override fun vertices(): List<Point> {
        TODO("Not yet implemented")
    }

    override fun isNameMatch(name: String): <PERSON><PERSON>an {
        TODO("Not yet implemented")
    }

    override fun parametricFunc(dim: Dimension): MFunc {
        TODO("Not yet implemented")
    }
}
