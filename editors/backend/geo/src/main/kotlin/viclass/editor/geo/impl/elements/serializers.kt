package viclass.editor.geo.impl.elements

import viclass.editor.geo.dbentity.*
import viclass.editor.geo.dbentity.elementdata.*
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.exceptions.SerializerException

/**
 * Containing functions that convert certain element's geometry to ByteArray
 * The byte array is stored inside the element data later for saving to database
 */

const val version = 1

typealias Serializer = (GeoDoc, Element) -> GeometryData

val serializers = mapOf<String, Serializer>(
    PointImpl::class.simpleName!! to ::serializePoint,

    LineImpl::class.simpleName!! to ::serializeLine,
    LineSegmentImpl::class.simpleName!! to ::serializeLineSegment,
    VectorImpl::class.simpleName!! to ::serializeVector,
    RayImpl::class.simpleName!! to ::serializeRay,

    TriangleImpl::class.simpleName!! to ::serializeTriangle,
    RightTriangleImpl::class.simpleName!! to ::serializeTriangle,
    IsoscelesTriangleImpl::class.simpleName!! to ::serializeTriangle,
    IsoscelesRightTriangleImpl::class.simpleName!! to ::serializeTriangle,
    EquilateralTriangleImpl::class.simpleName!! to ::serializeTriangle,

    QuadrilateralImpl::class.simpleName!! to ::serializeQuadrilateral,
    ParallelogramImpl::class.simpleName!! to ::serializeQuadrilateral,
    RectangleImpl::class.simpleName!! to ::serializeQuadrilateral,
    SquareImpl::class.simpleName!! to ::serializeQuadrilateral,
    RhombusImpl::class.simpleName!! to ::serializeQuadrilateral,
    TrapezoidImpl::class.simpleName!! to ::serializeQuadrilateral,

    PolygonImpl::class.simpleName!! to ::serializePolygon,
    RegularPolygonImpl::class.simpleName!! to ::serializePolygon,

    CircleImpl::class.simpleName!! to ::serializeCircle,
    EllipseImpl::class.simpleName!! to ::serializeEllipse,
    SemicircleImpl::class.simpleName!! to ::serializeSemicircle,
    CircularSectorImpl::class.simpleName!! to ::serializeCircularSector,

    AngleImpl::class.simpleName!! to ::serializeAngle,
)

@Throws(SerializerException::class)
fun serializePoint(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is Point -> {
        PointGeometry(version, e.x, e.y, e.z)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a point")
}

@Throws(SerializerException::class)
fun serializeLine(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is LineVi -> {

        val p1 = e.p1
        val p2 = e.p2
        val v = e.parallelVector

        val throughPointIndex = doc.getIndex(p1)!!
        val p2Index = p2?.let { doc.getIndex(it) }

        LineGeometry(version, throughPointIndex, p2Index, v.x, v.y, v.z)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a line")
}

@Throws(SerializerException::class)
fun serializeLineSegment(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is LineSegment -> {

        val spIdx = doc.getIndex(e.p1)!!
        val epIdx = doc.getIndex(e.p2)!!

        LineSegmentGeometry(version, spIdx, epIdx)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a line segment")
}

@Throws(SerializerException::class)
fun serializeVector(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is VectorVi -> {

        val spIdx = doc.getIndex(e.root())!!
        val epIdx = doc.getIndex(e.end())!!

        VectorGeometry(version, spIdx, epIdx)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a vector")
}

@Throws(SerializerException::class)
fun serializeRay(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is Ray -> {

        val tp = e.root()
        val v = e.parallelVector()

        val throughPointIndex = doc.getIndex(tp)!!
        val endPointIndex = e.end()?.let { doc.getIndex(it) }

        RayGeometry(version, throughPointIndex, v.x, v.y, v.z, endPointIndex)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a ray")
}

@Throws(SerializerException::class)
fun serializeTriangle(doc : GeoDoc, e : Element) : GeometryData {
    e as Triangle
    val p1Idx = doc.getIndex(e.point1)!!
    val p2Idx = doc.getIndex(e.point2)!!
    val p3Idx = doc.getIndex(e.point3)!!

    return when (e) {
        is EquilateralTriangle -> {
            EquilateralTriangleGeometry(version, p1Idx, p2Idx, p3Idx)
        }
        is IsoscelesRightTriangle -> {
            IsoscelesRightTriangleGeometry(version, p1Idx, p2Idx, p3Idx)
        }
        is IsoscelesTriangle -> {
            IsoscelesTriangleGeometry(version, p1Idx, p2Idx, p3Idx)
        }
        is RightTriangle -> {
            RightTriangleGeometry(version, p1Idx, p2Idx, p3Idx)
        }

        else -> {
            TriangleGeometry(version, p1Idx, p2Idx, p3Idx)
        }
    }
}

@Throws(SerializerException::class)
fun serializeQuadrilateral(doc : GeoDoc, e : Element) : GeometryData {
    e as Quadrilateral
    val p1Idx = doc.getIndex(e.point1)!!
    val p2Idx = doc.getIndex(e.point2)!!
    val p3Idx = doc.getIndex(e.point3)!!
    val p4Idx = doc.getIndex(e.point4)!!

    return when (e) {
        is Square -> {
            SquareGeometry(version, p1Idx, p2Idx, p3Idx, p4Idx)
        }

        is Rectangle -> {
            RectangleGeometry(version, p1Idx, p2Idx, p3Idx, p4Idx)
        }

        is Rhombus -> {
            RhombusGeometry(version, p1Idx, p2Idx, p3Idx, p4Idx)
        }

        is Parallelogram -> {
            ParallelogramGeometry(version, p1Idx, p2Idx, p3Idx, p4Idx)
        }

        is Trapezoid -> {
            TrapezoidGeometry(version, p1Idx, p2Idx, p3Idx, p4Idx)
        }

        else -> {
            QuadrilateralGeometry(version, p1Idx, p2Idx, p3Idx, p4Idx)
        }
    }
}

@Throws(SerializerException::class)
fun serializePolygon(doc : GeoDoc, e : Element) : GeometryData {
    e as Polygon

    return when (e) {
        is RegularPolygonImpl -> {
            PolygonGeometry(version, e.vertices().map { doc.getIndex(it)!! })
        }
        is PolygonImpl -> {
            PolygonGeometry(version, e.vertices().map { doc.getIndex(it)!! })
        }
        else -> throw SerializerException("Trying to deserialize data which is not a Polygon")
    }
}

@Throws(SerializerException::class)
fun serializeCircle(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is Circle -> {

        val oIdx = doc.getIndex(e.centerPoint)!!

        CircleGeometry(version, oIdx, e.radius)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a Circle")
}

@Throws(SerializerException::class)
fun serializeAngle(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is Angle -> {

        val anglePointIdx = doc.getIndex(e.anglePoint)!!
        val lineStartIdx = doc.getIndex(e.lineStart)!!
        val lineEndIdx = doc.getIndex(e.lineEnd)!!

        AngleGeometry(version, anglePointIdx, lineStartIdx, e.directionStart, lineEndIdx, e.directionEnd)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a angle")
}

@Throws(SerializerException::class)
fun serializeEllipse(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is Ellipse -> {

        val f1PointIdx = doc.getIndex(e.f1)!!
        val f2PointIdx = doc.getIndex(e.f2)!!

        EllipseGeometry(version, f1PointIdx, f2PointIdx, e.a, e.b)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a ellipse")
}

@Throws(SerializerException::class)
fun serializeSemicircle(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is Semicircle -> {
        val centerPointIdx = doc.getIndex(e.centerPoint)!!
        val startPointIdx = doc.getIndex(e.startPoint)!!
        val endPointIdx = doc.getIndex(e.endPoint)!!

        SemicircleGeometry(version, centerPointIdx, startPointIdx, endPointIdx)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a ellipse")
}

@Throws(SerializerException::class)
fun serializeCircularSector(doc : GeoDoc, e : Element) : GeometryData = when (e) {
    is CircularSector -> {

        val centerPointIdx = doc.getIndex(e.centerPoint)!!
        val startPointIdx = doc.getIndex(e.startPoint)!!
        val endPointIdx = doc.getIndex(e.endPoint)!!

        CircularSectorGeometry(version, centerPointIdx, startPointIdx, endPointIdx)
    }
    else -> throw SerializerException("Trying to serialize an element which is not a ellipse")
}

// TODO implement for other type of elements
