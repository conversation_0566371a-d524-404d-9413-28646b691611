package viclass.editor.geo.impl.constructor.polygon

import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.sin
import kotlin.reflect.KClass
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementFreePath
import viclass.editor.geo.dbentity.transformdata.FreePointTransformData
import viclass.editor.geo.dbentity.transformdata.MoveOrderTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aName
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.elements.RegularPolygon
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.exceptions.InvalidElementNameException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.CircleImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.RegularPolygonImpl
import viclass.editor.geo.impl.transformer.FreePointTransformer
import viclass.editor.geo.impl.transformer.MoveOrderTransformer
import viclass.editor.geo.impl.transformer.TransformMapping

/**
 * <AUTHOR>
 */
@Singleton
class RegularPolygonEC : ElementConstructor<RegularPolygon> {
    // Function returns the output type of this constructor, which is RegularPolygon
    override fun outputType(): KClass<RegularPolygon> {
        return RegularPolygon::class
    }

    // Enum defines constraint groups for constructing a regular polygon
    private enum class CGS {
        FromLineSegment,
        FromTwoPosition,
        FromPoints
    }

    // Function returns the template describing constraint groups and parameters needed to construct
    // a regular polygon
    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                // Constraint group for constructing from a list of points
                ConstraintGroupBuilder.create()
                    .name(CGS.FromPoints.name)
                    .constraint(0, ConstraintParamDefManager.instance()[aPoint]!!, listOf("Points"), "tpl-Points")
                    .build(),
                // Constraint group for constructing from a line segment
                ConstraintGroupBuilder.create()
                    .name(CGS.FromLineSegment.name)
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[aName]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-FromLineSegment"
                    )
                    .constraintDepends(
                        1,
                        ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-NoEdge"
                    )
                    .constraintOptional(
                        2,
                        ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
                // Constraint group for constructing from two positions (coordinates)
                ConstraintGroupBuilder.create()
                    .name(CGS.FromTwoPosition.name)
                    .numDim(3)
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraint(
                        1,
                        ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraintDepends(
                        2,
                        ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-NoEdge"
                    )
                    .constraintOptional(
                        3,
                        ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1, 2),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
            )
            .elTypes(RegularPolygon::class)
            .build()
    }

    // Function to construct a regular polygon based on the selected constraint group
    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<RegularPolygon> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                // Validate the number of constraints
                Validations.validateNumConstraints(c, 1)
                constructFromPoints(doc, inputName, c)
            }
            CGS.FromLineSegment -> {
                Validations.validateNumConstraints(c, 2)
                constructFromLine(doc, inputName, c)
            }
            CGS.FromTwoPosition -> {
                Validations.validateNumConstraints(c, 3)
                constructFromTwoPosition(doc, inputName, c)
            }
        }
    }

    // Function to construct a regular polygon from a list of points
    private fun constructFromPoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<RegularPolygon> {
        // Extract the list of points from the input parameter
        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx)
        inputName?.let {
            // If input name is provided, check if the point names match the input name
            val pointNames = NamePattern.extractPointName(Polygon::class, it).sortedBy { it }
            if (ext.result.map { it.result()?.name }.sortedBy { it } != pointNames)
                throw InvalidElementNameException("input name is not match with input points")
        }
        val points = ext.result.map { it.result()!! }
        // Create the regular polygon object from the points
        val polygon = RegularPolygonImpl(doc, inputName ?: points.map { it.name }.joinToString(""), points)
        val cr = ConstructionResultImpl<RegularPolygon>()
        // Add the points as dependencies
        cr.addDependencies(points, true)
        cr.setResult(polygon)
        return cr
    }

    // Function to construct a regular polygon from two positions (coordinates) and number of edges
    private fun constructFromTwoPosition(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<RegularPolygon> {
        // Extract coordinates of the first point
        val ext1 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[0], c.ctIdx)
        // Extract coordinates of the second point
        val ext2 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[1], c.ctIdx)
        // Extract the number of edges of the regular polygon
        val ext3 = extractFirstPossible<NumberExtraction<Int>>(doc, PK_Value, c.params[2], c.ctIdx)
        // Extract the intersection selection order (above/below)
        val ext4 = extractFirstPossible<NumberExtraction<Int>>(doc, PK_Value, c.params[3], c.ctIdx)

        val noEdge = ext3.result // Number of edges of the regular polygon
        val nth = ext4.result - 1 // Intersection order (0 or 1)

        // Get the list of point names for the regular polygon, or generate new names if not
        // provided
        val pointsName: List<String> =
            if (!inputName.isNullOrBlank()) {
                NamePattern.extractPointName(Polygon::class, inputName)
            } else {
                val excluded = ArrayList<String>()
                (0..4).map { generatePointName(doc, excluded) }
            }

        // Create the first two points of the regular polygon
        val p1 = PointImpl(doc, pointsName[0], ext1.result[0], ext1.result[1], .0)
        val p2 = PointImpl(doc, pointsName[1], ext2.result[0], ext2.result[1], .0)
        // Create the line segment connecting the first two points
        val line = LineSegmentImpl(doc, "${p1.name}${p2.name}", p1, p2)
        val size = line.length() // Edge length
        val angle = 2 * PI / noEdge // Angle between vertices
        val r = (size / 2) / sin(angle / 2) // Circumradius of the regular polygon
        // Create two circles centered at the first two points, radius r
        val c1 = CircleImpl(doc, null, p1, r)
        val c2 = CircleImpl(doc, null, p2, r)
        // Find the intersection points of the two circles (circumcenter)
        val c1_x_c2 = Intersections.of(c1, c2)!!
        val vC = c1_x_c2.get(nth) // Get the circumcenter by order

        val points: ArrayList<Point> = ArrayList(listOf(p1, p2))

        // Function to create a new point by rotating around the circumcenter
        val buildPoint: (i: Int) -> Point = { i ->
            val p = p2.coordinates().rotate(angle * i, vC)
            val name = pointsName[abs(i) + 1]
            PointImpl(doc, name, p.x, p.y, .0)
        }

        // Create the remaining vertices of the regular polygon
        for (i in 1..noEdge - 2) {
            points.add(buildPoint(if (nth == 0) i else -i))
        }

        // Assign free movement data to the first two points
        p1.transformData = FreePointTransformData(0)
        p1.transformer = TransformMapping.fromClazz(FreePointTransformer::class)
        p1.movementPath = MovementFreePath()

        p2.transformData = FreePointTransformData(1)
        p2.transformer = TransformMapping.fromClazz(FreePointTransformer::class)
        p2.movementPath = MovementFreePath()

        // Function to assign move order data to the remaining points
        val buildTransformer: (p: Point) -> Unit = { p ->
            p.transformData = MoveOrderTransformData(vC.toArray(), p.coordinates().toArray(), listOf(0, 1))
            p.transformer = TransformMapping.fromClazz(MoveOrderTransformer::class)
            p.movementPath = MovementFreePath()
        }

        val cr = ConstructionResultImpl<RegularPolygon>()
        // Add the first two points as dependencies
        cr.addDependencies(listOf(p1, p2), true)
        // The remaining points depend on the first two points
        points.subtract(setOf(p1, p2)).forEach { p ->
            buildTransformer(p)
            cr.addDependency(p, listOf(p1, p2), true)
        }

        // Create the regular polygon name by joining the vertex names
        val polygonName = points.map { it.name }.joinToString("")

        cr.setResult(RegularPolygonImpl(doc, polygonName, points))
        return cr
    }

    /**
     * The constructFromLine function is used to construct a regular polygon based on a given line segment and number of edges.
     *
     * Theory:
     * - Input includes: + Name of the line segment (lineName): determines the two endpoints p1, p2. + Number of edges of the regular polygon (edgeCount):
     * must be >= 3. + Intersection order (intersectionOrder): choose one of the two possible circumcenter positions.
     * - Idea:
     * 1. Calculate the length of the segment p1p2, which is the edge of the regular polygon.
     * 2. Calculate the angle between two adjacent vertices: angle = 2π / edgeCount.
     * 3. Calculate the circumradius: radius = (edgeLength / 2) / sin(angle / 2).
     * 4. Draw two circles centered at p1 and p2, with radius. These two circles intersect at two points,
     * which are the two possible positions for the circumcenter of the regular polygon.
     * 5. Select the circumcenter according to intersectionOrder (1 or 2).
     * 6. From the circumcenter, successively rotate point p1 around the center by angle to generate the remaining vertices.
     * The rotation direction is determined based on the order of p1, p2 and the center position.
     * 7. Assign names to the vertices, ensuring the number of names matches the number of edges.
     * 8. The result returned is a regular polygon with the determined vertices.
     *
     * This function ensures generality: allows generating any regular polygon based on one edge and the number of edges, while also controlling the rotation direction and circumcenter position.
     */
    private fun constructFromLine(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<RegularPolygon> {
        // Get the line segment name and find its two endpoints
        val lineName = extractFirstPossible<StringExtraction>(doc, PK_Name, c.params[0], c.ctIdx).result
        val linePoints = NamePattern.extractPointName(LineSegment::class, lineName)
        val p1 =
            doc.findElementByName(linePoints[0], Point::class, c.ctIdx)
                ?: throw InvalidElementNameException("Point ${linePoints[0]} not found")
        val p2 =
            doc.findElementByName(linePoints[1], Point::class, c.ctIdx)
                ?: throw InvalidElementNameException("Point ${linePoints[1]} not found")
        val lineSegment = LineSegmentImpl(doc, lineName, p1, p2)

        // Get the number of edges and intersection order (above/below)
        val edgeCount = extractFirstPossible<NumberExtraction<Int>>(doc, PK_Value, c.params[1], c.ctIdx).result
        val intersectionOrder = extractFirstPossible<NumberExtraction<Int>>(doc, PK_Value, c.params[2], c.ctIdx).result

        // Check if the number of edges is valid
        require(edgeCount >= 3) { "A regular polygon must have at least 3 edges" }

        // Get the names of the regular polygon's vertices, generate new ones if inputName is not provided
        val pointNames: List<String> =
            if (!inputName.isNullOrBlank()) {
                NamePattern.extractPointName(Polygon::class, inputName)
            } else {
                val excluded = arrayListOf(p1.name!!, p2.name!!)
                val n = edgeCount - 2
                listOf(p1.name!!, p2.name!!) + (1..n).map { generatePointName(doc, excluded) }
            }

        // Angle between vertices, circumradius
        val angle = 2 * PI / edgeCount
        val edgeLength = lineSegment.length()
        val radius = (edgeLength / 2) / sin(angle / 2)

        // Create two circles centered at p1, p2 with radius
        val circle1 = CircleImpl(doc, null, p1, radius)
        val circle2 = CircleImpl(doc, null, p2, radius)
        val intersections =
            Intersections.of(circle1, circle2)
                ?: throw IllegalStateException("Cannot find intersection of circles for regular polygon construction")

        // Get the circumcenter according to the selected order (intersectionOrder: 1 or 2)
        val nth = (intersectionOrder - 1).coerceIn(0, 1)
        val center = intersections[nth]

        // Generate the remaining vertices based on the position and order of p1 and p2
        val points = ArrayList<Point>()
        points.add(p1)
        points.add(p2)

        // Determine the rotation direction to generate the remaining vertices of the regular polygon.
        // Detailed explanation of the cross product:
        // - We have two endpoints p1 and p2 of the first edge.
        // - The circumcenter (center) is one of the two intersection points of the two circumcircles constructed from p1 and p2.
        // - To determine the rotation direction (clockwise or counterclockwise) when generating the next vertices,
        //   we need to know the relative position of the circumcenter with respect to the segment p1->p2.
        // - We construct two vectors in the Oxy plane:
        //      + edgeVector: vector from p1 to p2 (edgeVector = p2 - p1)
        //      + centerVector: vector from p1 to center (centerVector = center - p1)
        // - The cross product of two vectors in the Oxy plane is calculated as follows:
        //      cross = edgeVector.x * centerVector.y - edgeVector.y * centerVector.x
        //   Geometric meaning:
        //      + cross > 0: centerVector is to the left of edgeVector (i.e., the circumcenter is to the left of segment p1->p2 when looking from p1 to p2).
        //      + cross < 0: centerVector is to the right of edgeVector (i.e., the circumcenter is to the right of segment p1->p2).
        //   Right-hand rule:
        //      - Place your right hand so that your fingers point from p1 to p2 (along edgeVector), if your palm faces up towards center then cross > 0.
        //      - If your palm faces down towards center then cross < 0.
        //   Application:
        //      + If cross > 0: we will rotate the vertices in the positive direction (counterclockwise).
        //      + If cross < 0: we rotate in the negative direction (clockwise).
        // - The variable direction takes the value 1 (counterclockwise) or -1 (clockwise) to control the rotation direction.
        val edgeVector = p2.coordinates().subtract(p1.coordinates())
        val centerVector = center.subtract(p1.coordinates())
        val cross = edgeVector.x * centerVector.y - edgeVector.y * centerVector.x
        val direction = if (cross > 0) 1 else -1

        // Generate the remaining vertices
        var prev = p2
        for (i in 2 until edgeCount) {
            // Rotate the previous point around the center by direction * angle
            val prevCoord = prev.coordinates()
            val nextCoord = prevCoord.rotate(direction * angle, center)
            val nextPoint = PointImpl(doc, pointNames[i], nextCoord.x, nextCoord.y, .0)
            points.add(nextPoint)
            prev = nextPoint
        }

        // Ensure the number of vertex names matches the number of vertices
        require(points.size == edgeCount) { "Number of points does not match edge count" }

        // Create the regular polygon name
        val polygonName = points.joinToString(separator = "") { it.name.toString() }

        // Construction result
        val cr = ConstructionResultImpl<RegularPolygon>()
        cr.setResult(RegularPolygonImpl(doc, polygonName, points))
        // Add dependency for the vertices
        points.forEach { cr.addDependency(it, listOf(), true) }
        return cr
    }
}
