package viclass.editor.geo.impl.constructor.polygon

import kotlin.reflect.KClass
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PolygonImpl

/**
 *
 * <AUTHOR>
 */
@Singleton
class PolygonEC : ElementConstructor<Polygon> {
    override fun outputType(): KClass<Polygon> {
        return Polygon::class
    }

    private enum class CGS {
        ByPointsName
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this).cgs(
            ConstraintGroupBuilder.create().name(CGS.ByPointsName.name).constraintDefault().build(),
        ).elTypes(Polygon::class).build()
    }

    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<Polygon> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ByPointsName -> {
                Validations.validateNumConstraints(c, 0)
                constructFromName(doc, inputName!!, c)
            }
        }
    }

    private fun constructFromName(
        doc: GeoDoc, inputName: String, c: Construction
    ): ConstructionResult<Polygon> {
        val names = NamePattern.extractPointName(Polygon::class, inputName)
        val vertexes = names.map { doc.findElementByName(it, Point::class, c.ctIdx)!! }

        val cr = ConstructionResultImpl<Polygon>()
        cr.setResult(PolygonImpl(doc, inputName, vertexes))
        vertexes.map { cr.addDependency(it, listOf(), true) }

        return cr
    }
}
