package viclass.editor.geo.impl.doc

import common.libs.logger.Logging
import io.ktor.util.reflect.*
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.impl.koin.PARAMETER_EXTRACTORS
import kotlin.reflect.KClass
import kotlin.reflect.full.isSuperclassOf

/**
 * This class contains all parameter extractors available to the system
 * It provides API to find most suitable extractors for certain types and name
 *
 * The algorithm to find the most suitable extractors is going to be improved over time
 */
@Singleton(binds = [ParamExtractorManager::class])
class ParamExtractorManager(
    @Named(PARAMETER_EXTRACTORS) private val extractors: Set<ParameterExtractor>
) : Logging {
    operator fun get(id: String): ParameterExtractor {
        return extractors.first { it.id == id }
    }

    /**
     * Find the suitable extractors for a particular type and values
     * This includes
     * 1. finding all extractors within the set that has output types equal to the requested type OR is the super type of
     * the requested type, for example, extractor for a line might be used to extract a line segment parameter
     * 2. using the extractors with supported types to try extract the value from the document to see if it successful
     * @param doc
     * @param type
     * @return
     */
    fun findExtractors(doc: GeoDoc, type: KClass<*>, store: ParamStore?, ctIdx: Int): List<ParameterExtractor> {
        val candidates: MutableList<ParameterExtractor> = mutableListOf()
        for (ex in extractors) {
            if (ex.isSupport(doc, type, store)) {
                //TODO this is a simple implementation, to do it correctly
                // a candidate is an extractor that can extract the exact type OR
                // a type that is the subclass of type.
                // The candidates array should be sorted by the supported types and then
                // used for trial extraction.
                candidates.add(ex)
            }
        }

        //TODO For now we return the first successful extractor.
        // We should however return a list of suitable
        // extractor

        for (c in candidates) {
            if (Element::class.isSuperclassOf(type)) { // extracting an element
                if (store is ParamStoreValue) {
                    val er = c.extract<ElementExtraction<Element>>(doc, store, ctIdx, type as KClass<out Element>)
                    val cr = er.result
                    if (cr.result() != null) {
                        // extraction is successful, and we get an instance of type required
                        if (type.isSuperclassOf(cr.result()!!::class))
                        //TODO not return the first successful one
                            return listOf(c)
                    }
                } else if (store is ParamStoreArray) {
                    val er = c.extract<ElementListExtraction<Element>>(doc, store, ctIdx, type as KClass<out Element>)
                    val cr = er.result
                    if (cr.isNotEmpty()) { // if you have some result
                        var successful = true
                        for (r in cr) {
                            // if the constructed result is of required type
                            if (!type.isSuperclassOf(r.result()!!::class)) {
                                successful = false
                                break
                            }
                        }
                        // extraction is successful and we get an instance of type required
                        //TODO not return the first successful one
                        if (successful) return listOf(c)
                    }
                }
            } else if (Number::class.isSuperclassOf(type)) {
                if (store is ParamStoreValue) {
                    val er = c.extract<NumberExtraction<*>>(doc, store, ctIdx)
                    val value = er.result
                    // extraction is successful, and we get an instance of type required
                    if (value.instanceOf(Number::class))
                    //TODO not return the first successful one, should add to a list and return the list later on
                        return listOf(c)
                } else if (store is ParamStoreArray) {
                    val er = c.extract<NumberListExtraction<*>>(doc, store, ctIdx)
                    val lst = er.result
                    if (lst.isNotEmpty()) { // if you have some result
                        var successful = true
                        for (r in lst) {
                            // if the constructed result is of required type
                            if (!r.instanceOf(Number::class)) {
                                successful = false
                                break
                            }
                        }
                        // extraction is successful and we get an instance of type required
                        //TODO not return the first successful one
                        if (successful) return listOf(c)
                    }
                }
            } else if (String::class.isSuperclassOf(type)) {
                if (store is ParamStoreValue) {
                    val er = c.extract<StringExtraction>(doc, store, ctIdx)
                    val value = er.result
                    // extraction is successful, and we get an instance of type required
                    if (value.instanceOf(String::class))
                    //TODO not return the first successful one, should add to a list and return the list later on
                        return listOf(c)
                } else if (store is ParamStoreArray) {
                    val er = c.extract<StringListExtraction>(doc, store, ctIdx)
                    val lst = er.result
                    if (lst.isNotEmpty()) { // if you have some result
                        var successful = true
                        for (r in lst) {
                            // if the constructed result is of required type
                            if (!r.instanceOf(String::class)) {
                                successful = false
                                break
                            }
                        }
                        // extraction is successful and we get an instance of type required
                        //TODO not return the first successful one
                        if (successful) return listOf(c)
                    }
                }
            } else if (Boolean::class.isSuperclassOf(type)) {
                if (store is ParamStoreValue) {
                    c.extract<BooleanExtraction>(doc, store, ctIdx)
                    return listOf(c)
                } else if (store is ParamStoreArray) {
                    c.extract<BooleanListExtraction>(doc, store, ctIdx)
                    return listOf(c)
                }
            } else if (Extractable::class.isSuperclassOf(type)) {
                // currently only support single value for extraction
                //TODO support param store array as well
                if (store is ParamStoreValue) {
                    val er = c.extract<ExtractableWithConstructions<Extractable>>(doc, store, ctIdx)
                    if (er.instanceOf(ExtractableWithConstructions::class) && er.result.instanceOf(Extractable::class)) {
                        return listOf(c)
                    }
                }
            }
        }

        return listOf() // if no extraction is successful
    }
}
