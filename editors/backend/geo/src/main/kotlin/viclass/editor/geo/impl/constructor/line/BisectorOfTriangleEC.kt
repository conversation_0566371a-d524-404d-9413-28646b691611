package viclass.editor.geo.impl.constructor.line

import kotlin.reflect.KClass
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementExtraction
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementExistInDocumentException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.elements.*
import viclass.editor.geo.impl.extractors.LineExtractor
import viclass.editor.geo.impl.extractors.PointExtractor

@Singleton
class BisectorOfTriangleEC constructor(private val extractorManager: ParamExtractorManager) :
    ElementConstructor<LineVi> {

    private enum class CGS {
        BisectorLineSegment, BisectorLine
    }

    override fun template(): ConstructorTemplate {
        val cg2 = ConstraintGroupBuilder.create().name(CGS.BisectorLineSegment.name).hints("hint-BisectorOfTriangle")
            .constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aTriangle]!!,
                listOf("NameOfTriangle"),
                "tpl-BisectorOfTriangle"
            ).build()

        val cg3 =
            ConstraintGroupBuilder.create().name(CGS.BisectorLine.name).hints("hint-BisectorOfTriangle").constraint(
                0,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aTriangle]!!,
                listOf("NameOfTriangle"),
                "tpl-BisectorOfTriangle"
            ).constraint(
                1,
                ConstraintParamDefManager.instance()[ConstraintParamDefManager.aPoint]!!,
                listOf("NameOfPoint"),
                "tpl-ThroughPoint"
            ).build()

        return ConstructorTemplateBuilder.create(this).cgs(cg2, cg3)
            .elTypes(LineVi::class, LineSegment::class, Ray::class).build()
    }

    override fun construct(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.BisectorLineSegment -> {
                Validations.validateNumConstraints(c, 1)
                constructBisectorLineSegment(doc, inputName, c)
            }

            CGS.BisectorLine -> {
                Validations.validateNumConstraints(c, 2)
                constructBisectorLine(doc, inputName, c)
            }
        }
    }

    private fun constructBisectorLine(
        doc: GeoDoc, inputName: String?, c: Construction
    ): ConstructionResult<LineVi> {
        // 1. Extract triangle and the vertex from which the bisector originates.
        val triangleCr = extractFirstPossible<ElementExtraction<Triangle>>(
            doc, ParamKind.PK_Name, c.params[0], c.ctIdx
        )
        val triangle = triangleCr.result.result() ?: throw ElementNotExistInDocumentException(
            "Triangle not found for bisector construction."
        )

        val fromVertexCr = extractFirstPossible<ElementExtraction<Point>>(
            doc, ParamKind.PK_Name, c.params[1], c.ctIdx
        )
        val fromVertex = fromVertexCr.result.result() ?: throw ElementNotExistInDocumentException(
            "Vertex not found for bisector construction."
        )

        val bisectorName = inputName ?: fromVertex.name!!.lowercase()

        // Validate if the vertex belongs to the triangle.
        val triangleVertexNames = NamePattern.extractPointName(Triangle::class, triangle.name!!)
        if (fromVertex.name !in triangleVertexNames) {
            throw ConstructionException(
                "Vertex ${fromVertex.name} is not part of triangle ${triangle.name}."
            )
        }

        // 2. Find the incenter of the triangle.
        // Triangles.findBisectorsIntersection may create a new PointImpl in the doc if 'doc' is not
        // null.
        // This incenter point is used for its coordinates to determine the bisector's direction.
        val incenterPoint = Triangles.findBisectorsIntersection(doc, null, triangle)

        // 3. Determine the direction vector for the bisector (fromVertex -> incenterPoint).
        val bisectorDirectionVector = fromVertex.coordinates().vectorTo(incenterPoint.coordinates())

        // 4. Prepare the main construction result object and add initial dependencies.
        val finalCr = ConstructionResultImpl<LineVi>()
        finalCr.mergeAsDependency(triangleCr.result)
        finalCr.mergeAsDependency(fromVertexCr.result)

        // 5. Construct the specific type of line based on c.entity.elType.
        when (c.entity.elType) {
            LineSegment::class.simpleName -> {
                // Determine the opposite side of the triangle.
                val oppositeSideVertexNamesSet = triangleVertexNames.subtract(setOf(fromVertex.name!!))
                if (oppositeSideVertexNamesSet.size != 2) {
                    throw ConstructionException(
                        "Could not determine unique opposite side for vertex ${fromVertex.name} in triangle ${triangle.name}."
                    )
                }
                val oppositeSideNameString = oppositeSideVertexNamesSet.joinToString("")

                val lineExtractor = extractorManager.get(LineExtractor::class.simpleName!!)
                val oppositeSideCr = lineExtractor.extractConstructionResult(
                    doc, oppositeSideNameString, c.ctIdx, LineSegment::class
                )
                val oppositeSide = oppositeSideCr.result() ?: throw ElementNotExistInDocumentException(
                    "Opposite side '$oppositeSideNameString' not found or could not be constructed."
                )
                finalCr.mergeAsDependency(oppositeSideCr) // Add opposite side as a dependency.

                // Create a temporary infinite line for intersection calculation. Name is null as
                // it's not persisted.
                val tempInfiniteBisectorLine = LineImpl(doc, null, fromVertex, bisectorDirectionVector)

                val intersectionCoords =
                    Intersections.of(oppositeSide, tempInfiniteBisectorLine) ?: throw ConstructionException(
                        "Angle bisector does not intersect the opposite side segment."
                    )

                // Create the new point at the intersection.
                val intersectionPointElementName = generatePointName(doc)
                val intersectionPointElement = PointImpl(doc, intersectionPointElementName, intersectionCoords)
                // This new point is created by this constructor, so it's a 'newly' created
                // dependency.
                finalCr.addDependency(
                    intersectionPointElement, listOf(oppositeSide, fromVertex, triangle), true
                )

                val bisectorSegment = LineSegmentImpl(doc,  "${fromVertex.name}${intersectionPointElement.name}", fromVertex, intersectionPointElement)
                finalCr.setResult(bisectorSegment)
            }

            LineVi::class.simpleName -> {
                val bisectorInfiniteLine = LineImpl(doc, bisectorName, fromVertex, bisectorDirectionVector)
                finalCr.setResult(bisectorInfiniteLine)
            }

            Ray::class.simpleName -> {
                val bisectorRay = RayImpl(doc, bisectorName, fromVertex, bisectorDirectionVector)
                finalCr.setResult(bisectorRay)
            }

            else -> {
                throw ConstructionException(
                    "Unsupported element type for triangle bisector: ${c.entity.elType}"
                )
            }
        }
        return finalCr
    }

    private fun constructBisectorLineSegment(
        doc: GeoDoc, name: String?, c: Construction
    ): ConstructionResult<LineVi> {
        val extractionResult = extractFirstPossible<ElementExtraction<Triangle>>(
            doc, ParamKind.PK_Name, c.params[0], c.ctIdx
        )
        val triangle = extractionResult.result.result()!!

        var fromVertexName: String? = null
        var toLineName: String? = null
        var intersectionPointName: String? = null

        for (regex in NamePattern[LineSegment::class]!!) {
            if (regex.matches(name!!)) {
                val matchResult = regex.find(name)!!
                val p1 = matchResult.groups[1]?.value!!
                val p2 = matchResult.groups[2]?.value!!

                if (triangle.name!!.contains(p1) && !triangle.name!!.contains(p2)) {
                    fromVertexName = p1
                    toLineName = triangle.name!!.replace(p1, "")
                    intersectionPointName = p2
                    break
                }

                if (!triangle.name!!.contains(p1) && triangle.name!!.contains(p2)) {
                    fromVertexName = p2
                    toLineName = triangle.name!!.replace(p2, "")
                    intersectionPointName = p1
                    break
                }

                throw ConstructionException("Bisector line $name is not valid")
            }
        }

        if (fromVertexName == null) throw ConstructionException("Bisector line $name is not valid")
        if (doc.findElementByName(intersectionPointName!!, Point::class, c.ctIdx) != null) {
            throw ElementExistInDocumentException("$intersectionPointName is already exist")
        }

        val extractorLineSegment = extractorManager[LineExtractor::class.simpleName!!]
        val extractorPoint = extractorManager[PointExtractor::class.simpleName!!]

        val toLineCR: ConstructionResult<LineSegment> =
            extractorLineSegment.extractConstructionResult(doc, toLineName!!, c.ctIdx)
        val fromVertexCR: ConstructionResult<Point> =
            extractorPoint.extractConstructionResult(doc, fromVertexName, c.ctIdx)

        val toLine = toLineCR.result()!!
        val fromVertex = fromVertexCR.result()!!

        val centerPoint = Triangles.findBisectorsIntersection(doc, null, triangle)
        val bisectorVector = createVectorByEl(doc, centerPoint, fromVertex)
        val bisectorLine = LineImpl(doc, name, fromVertex, bisectorVector)

        // calculate intersection
        val intersection = Intersections.of(bisectorLine, toLine)!!
        val intersectionPoint = PointImpl(
            doc, intersectionPointName, intersection.x, intersection.y, intersection.z
        )

        val cr = ConstructionResultImpl<LineVi>()
        cr.setResult(LineSegmentImpl(doc, name, fromVertex, intersectionPoint))
        cr.addDependency(fromVertex, listOf(), true)
        cr.addDependency(intersectionPoint, listOf(), true)

        return cr
    }

    override fun outputType(): KClass<LineVi> {
        return LineVi::class
    }
}
