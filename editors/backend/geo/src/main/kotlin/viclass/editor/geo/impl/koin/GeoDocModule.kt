package viclass.editor.geo.impl.koin

import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import org.koin.core.component.KoinComponent
import viclass.editor.geo.doc.ConstraintParamDefManager

@Module
@ComponentScan("viclass.editor.geo.impl.doc")
class GeoDocModule : KoinComponent {

    @Singleton
    fun provideConstraintParamDefManager(): ConstraintParamDefManager = ConstraintParamDefManager.instance()
}
