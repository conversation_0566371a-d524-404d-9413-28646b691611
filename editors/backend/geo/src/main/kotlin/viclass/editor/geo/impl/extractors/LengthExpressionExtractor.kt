package viclass.editor.geo.impl.extractors

import com.fasterxml.jackson.databind.JsonNode
import common.libs.evaluator.EvalContext
import common.libs.evaluator.Evaluator
import net.objecthunter.exp4j.Expression
import net.objecthunter.exp4j.ExpressionBuilder
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ExtractableWithConstructions
import viclass.editor.geo.constructor.ExtractionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.exceptions.ExtractionFailedException
import viclass.editor.geo.extractable.ValueExpression
import viclass.editor.geo.server.logger
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class LengthExpressionExtractor(
    private val lineExtractor: LineExtractor,
    private val evaluator: Evaluator
) : ParameterExtractor {
    override var id: String = javaClass.simpleName

    private val lineRegex: Regex = Regex("([A-Z]\\d*'?)([A-Z]\\d*'?)")

    @Throws(ExtractionFailedException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : ExtractionResult<out Any>> extract(doc: GeoDoc, store: ParamStore, ctIdx: Int, clazz: KClass<out Element>?): T {
        if (store is ParamStoreValue) {
            try {
                val constructions: ArrayList<ConstructionResult<out Element>> = ArrayList()
                val strValue = store.value.trim()

                // try to evaluate as MathJSON expression
                if (strValue.possiblyMathJSON()) {
                    try {
                        val supportExtractorOps = setOf("Hinh", "Duong", "Doan", "Tia", "ChieuDai")
                        val extractor = fun (nodes: List<JsonNode>): Double?  {
                            if (nodes.size != 2) return null
                            if (nodes[0].isTextual && nodes[1].isTextual && supportExtractorOps.contains(nodes[0].asText())) {
                                var elemName = nodes[1].asText()
                                if (elemName.surroundedBy("'")) elemName = elemName.substring(1, elemName.length - 1)
                                val line: ConstructionResult<LineSegment> = this.lineExtractor.extractConstructionResult(doc, elemName, ctIdx)
                                constructions.add(line)
                                return line.result()!!.length()
                            }
                            return null
                        }
                        val result = evaluator.evaluate(strValue, EvalContext(
                            supportExtractorOps = supportExtractorOps,
                            extractor = extractor
                        ))
                        return ExtractableWithConstructions(ValueExpression(result, constructions)) as T
                    } catch (ex: Exception) {
                        logger.error("have exception while evaluating MathJSON expression... ", ex)
                    }
                }

                // not a MathJSON -> evaluate using the legacy expression
                val variables = lineRegex.findAll(strValue).map { it.groupValues[0] }.toList()
                val expression: Expression = ExpressionBuilder(strValue)
                    .variables(*variables.toTypedArray())
                    .build()
                variables.forEach {
                    val line: ConstructionResult<LineSegment> = this.lineExtractor.extractConstructionResult(doc, it, ctIdx)
                    constructions.add(line)
                    expression.setVariable(it, line.result()!!.length())
                }
                return ExtractableWithConstructions(ValueExpression(expression.evaluate(), constructions)) as T
            } catch (ex: Exception) {
                logger.error("have exception while extracting constructions... ", ex)
                throw ExtractionFailedException(ex)
            }
        }

        throw ExtractionFailedException("Param store type not supported")
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(ValueExpression::class)
    }
}
