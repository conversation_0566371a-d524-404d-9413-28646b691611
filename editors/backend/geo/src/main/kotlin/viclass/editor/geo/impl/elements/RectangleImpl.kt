package viclass.editor.geo.impl.elements

import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Rectangle
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
open class RectangleImpl(doc: GeoDoc, name: String?,
    point1: Point, point2: Point, point3: Point, point4: Point
) : Rectangle, ParallelogramImpl(doc, name, point1, point2, point3, point4) {
    override val clazz: KClass<out Rectangle> = Rectangle::class

    /**
     * Validates that the element is a Rectangle by ensuring it is Parallelogram and its diagonals are of equal length.
     *
     * This function extends the validation of a Parallelogram by specifically checking if the
     * lengths of its diagonals are equal, thus confirming it is a Rectangle. It calculates the
     * distances between opposite vertices (point1, point3) and (point2, point4) representing the
     * diagonals and compares these lengths within a predefined tolerance (DEFAULT_TOLERANCE) to
     * account for floating-point precision.
     *
     * If the absolute difference between the lengths of the diagonals exceeds the specified
     * tolerance, it signifies that the Parallelogram does not meet the criteria of a Rectangle,
     * and an InvalidElementException is thrown.
     *
     * @throws InvalidElementException if the Parallelogram is not a Rectangle due to unequal diagonal lengths.
     */
    override fun validate() {
        super<ParallelogramImpl>.validate()

        // Calculate the length of the diagonals.
        val diagonal1Length = Distances.of(point1, point3)
        val diagonal2Length = Distances.of(point2, point4)

        // Check if the lengths of the diagonals are equal within the allowed tolerance.
        if (Math.abs(diagonal1Length - diagonal2Length) > DEFAULT_TOLERANCE) {
            // If the difference exceeds the tolerance, it's not a Rectangle, throw an exception.
            throw InvalidElementException("$name is not a rectangle")
        }
    }

    override fun mergeFrom(other: Element) {
        if (other !is Rectangle) return
        super<ParallelogramImpl>.mergeFrom(other)
    }
}
