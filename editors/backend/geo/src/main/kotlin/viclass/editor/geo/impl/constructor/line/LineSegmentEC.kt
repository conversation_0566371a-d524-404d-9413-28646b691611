package viclass.editor.geo.impl.constructor.line

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.extractors.LineExtractor
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class LineSegmentEC constructor(
    val extractorManager: ParamExtractorManager
) : ElementConstructor<LineSegment> {

    override fun outputType(): KClass<LineSegment> {
        return LineSegment::class
    }

    private enum class CGS {
        ByPointsName
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.ByPointsName.name)
                    .constraintDefault()
                    .build(),
            )
            .elTypes(LineSegment::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<LineSegment> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ByPointsName -> {
                Validations.validateNumConstraints(c, 0)
                constructFromLineSegmentName(doc, inputName!!, c)
            }
        }
    }

    private fun constructFromLineSegmentName(
        doc: GeoDoc,
        inputName: String,
        c: Construction,
    ): ConstructionResult<LineSegment> {
        val extractor = extractorManager[LineExtractor::class.simpleName!!]
        return extractor.extractConstructionResult(doc, inputName, c.ctIdx, LineSegment::class)
    }
}
