package viclass.editor.geo.impl.elements

import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Quadrilateral
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
open class QuadrilateralImpl(
    override var doc: GeoDoc, override var name: String?,
    private var _point1: Point,
    private var _point2: Point,
    private var _point3: Point,
    private var _point4: Point
) : Quadrilateral, PolygonImpl(doc, name, listOf(_point1, _point2, _point3, _point4)) {
    override val clazz: KClass<out Quadrilateral> = Quadrilateral::class

    override val point1: Point
        get() = _point1
    override val point2: Point
        get() = _point2
    override val point3: Point
        get() = _point3
    override val point4: Point
        get() = _point4

    /**
     * Validates that the polygon defined by the 'points' list is a valid Quadrilateral.
     *
     * This function ensures the given points form a valid quadrilateral by performing two key checks:
     * 1. Validates the triangle inequality theorem for adjacent sides and a diagonal, ensuring that
     * the sum of any two adjacent side lengths is greater than the length of the diagonal.
     * 2. Checks for collinearity of the vertices to prevent degenerate quadrilaterals.
     *
     * The lengths of the four sides and the diagonal connecting points 2 and 4 are calculated.
     * The triangle inequality theorem is checked using a tolerance (DEFAULT_TOLERANCE) to account
     * for floating-point precision.
     *
     * Collinearity is checked by computing the cross product of the vector from point 1 to point 2
     * and the diagonal vector, and then taking the dot product with the vector from
     * point 4 to point 1. A non-zero result indicates non-collinearity.
     *
     * If either the triangle inequality or collinearity check fails, an InvalidElementException is thrown.
     *
     * @throws InvalidElementException if the given points do not form a valid quadrilateral due to
     * invalid side lengths or collinear vertices.
     */
    override fun validate() {
        // Calculate the lengths of the four sides of the quadrilateral.
        val side1 = Distances.of(point1, point2)
        val side2 = Distances.of(point2, point3)
        val side3 = Distances.of(point3, point4)
        val side4 = Distances.of(point4, point1)

        // Calculate the length of the diagonal connecting point 2 and point 4.
        val diagonalLength = Distances.of(point2, point4)

        // Check if the sum of any two adjacent sides is greater than the diagonal length
        // (triangle inequality theorem within tolerance).
        if (Math.abs(side1 + side4 - diagonalLength) < DEFAULT_TOLERANCE ||
            Math.abs(side2 + side3 - diagonalLength) < DEFAULT_TOLERANCE
        ) {
            // The sum of adjacent sides is not greater than the diagonal, indicating an invalid quadrilateral.
            throw InvalidElementException("$name is not a quadrilateral")
        }

        // Check for collinearity of vertices to ensure a non-degenerate quadrilateral.
        // Compute the cross product of vectors (point1->point2) and (point2->point4) (diagonal vector),
        // then take the dot product with the vector (point4->point1).
        // A non-zero result indicates non-collinearity.
        if (point1.coordinates().vectorTo(point2.coordinates())
                .cross(point2.coordinates().vectorTo(point4.coordinates()))
                .dot(point4.coordinates().vectorTo(point1.coordinates())) != 0.0
        ) {
            // Vertices are collinear, indicating an invalid quadrilateral.
            throw InvalidElementException("$name is not a quadrilateral")
        }
    }

    override fun vertices(): List<Point> {
        return listOf(point1, point2, point3, point4)
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Quadrilateral::class, name)) return false
        val a = NamePattern.extractPointName(Quadrilateral::class, this.name!!)
        val b = NamePattern.extractPointName(Quadrilateral::class, name)
        if (a.size != b.size || a.isEmpty()) return false
        fun match(x: List<String>, y: List<String>) =
            (x + x).windowed(x.size).any { it == y } || (x.reversed() + x.reversed()).windowed(x.size).any { it == y }
        return match(a, b)
    }

    override fun mergeFrom(other: Element) {
        if (other !is Quadrilateral) return
        super<PolygonImpl>.mergeFrom(other)

    }
}
