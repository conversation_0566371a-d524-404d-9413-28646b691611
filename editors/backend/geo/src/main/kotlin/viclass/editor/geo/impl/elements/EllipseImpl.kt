package viclass.editor.geo.impl.elements

import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt
import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.math.MFunc
import viclass.editor.geo.solving.Coefficients

class EllipseImpl(
    override val doc: GeoDoc,
    override var name: String?,
    private var _f1: Point,
    private var _f2: Point,
    private var _a: Double,
    private var _b: Double,
) : Ellipse {
    override val clazz = Ellipse::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true

    override val f1: Point
        get() = _f1
    override val f2: Point
        get() = _f2
    override val a: Double
        get() = _a
    override val b: Double
        get() = _b

    private lateinit var _vC: Vector3D
    private var _angle: Double = 0.0

    private lateinit var _coefs: Coefficients

    override val center: Vector3D
        get() = _vC
    override val rotate: Double
        get() = _angle

    /**
     * Các hệ số của phương trình bậc hai ẩn biểu diễn elip.
     *
     * Các hệ số này được tính toán trong hàm `validate()` và được sử dụng để xác định hình dạng và vị trí của elip
     * trong không gian.
     *
     * Các hệ số biểu diễn các thuật ngữ sau:
     * - AA: Hệ số của x^2
     * - BB: Hệ số của xy
     * - CC: Hệ số của y^2
     * - D: Hệ số của x
     * - E: Hệ số của y
     * - F: Hằng số
     */
    override val coefs: Coefficients
        get() {
            return _coefs
        }

    /**
     * Xác thực các thuộc tính của elip và tính toán các hệ số cho phương trình bậc hai ẩn.
     *
     * Hàm này kiểm tra tọa độ của các tiêu điểm để xác định tâm và góc quay của elip. Nếu các tiêu điểm trùng nhau,
     * elip suy biến (thành hình tròn hoặc điểm). Ngược lại, tâm là trung điểm của các tiêu điểm, và góc quay được tính
     * toán bằng cách sử dụng các vectơ từ tâm đến một điểm tham chiếu tùy ý và đến một tiêu điểm.
     *
     * Sau khi xác định tâm và góc quay, hàm tính toán các hệ số của phương trình bậc hai ẩn của elip. Các hệ số này
     * được sử dụng để mô tả hình dạng và vị trí của elip trong không gian.
     *
     * Các hệ số được tính như sau:
     * - AA, BB, CC: Hệ số của các thuật ngữ bậc hai (x^2, xy, y^2).
     * - D, E: Hệ số của các thuật ngữ tuyến tính (x, y).
     * - F: Hằng số.
     *
     * Các hệ số này được lưu trữ trong đối tượng Coefficients (_coefs).
     */
    override fun validate() {
        // Xác định tâm và góc dựa trên tọa độ các tiêu điểm.
        if (f1.coordinates() == f2.coordinates()) {
            // Các tiêu điểm trùng nhau; elip suy biến (hình tròn hoặc điểm).
            _vC = f1.coordinates() // Tâm tại các tiêu điểm trùng nhau.
            _angle = 0.0 // Không có quay.
        } else {
            // Elip tiêu chuẩn với các tiêu điểm khác nhau.
            _vC =
                Vector3D.of(
                    (f1.x + f2.x) / 2, // Trung điểm của các tiêu điểm là tâm elip.
                    (f1.y + f2.y) / 2,
                    (f1.z + f2.z) / 2
                )

            // Tính toán góc quay.
            val v0 = Vector3D.of(center.x + 10, center.y, center.z) // Vectơ tùy ý để tham chiếu.
            val vecC0 = center.vectorTo(v0) // Vectơ từ tâm đến điểm tham chiếu.
            val vecCE = center.vectorTo(f2.coordinates()) // Vectơ từ tâm đến một tiêu điểm.
            _angle = vecC0.angleTo(vecCE) // Góc giữa hai vectơ.
        }

        // Tính toán các hệ số của phương trình bậc hai ẩn của elip.
        val angle = -this._angle // Góc quay, phủ định.
        val cosAngle = cos(angle)
        val sinAngle = sin(angle)
        val centerX = center.x
        val centerY = center.y
        val aSquared = a * a // Bình phương trục bán kính lớn.
        val bSquared = b * b // Bình phương trục bán kính nhỏ.

        // Tính toán các hệ số của phương trình elip đã quay.
        val AA = cosAngle * cosAngle / aSquared + sinAngle * sinAngle / bSquared
        val BB = -2 * cosAngle * sinAngle / aSquared + 2 * cosAngle * sinAngle / bSquared
        val CC = sinAngle * sinAngle / aSquared + cosAngle * cosAngle / bSquared
        val D = -(2 * AA * centerX + BB * centerY)
        val E = -(BB * centerX + 2 * CC * centerY)
        val F = AA * centerX * centerX + BB * centerX * centerY + CC * centerY * centerY - 1

        // Lưu trữ các hệ số đã tính.
        _coefs = Coefficients(AA, BB, CC, D, E, F)
    }

    override fun parametricFunc(dim: Dimension): MFunc {
        TODO("Not yet implemented")
    }

    override fun vertices(): List<Point> {
        return listOf(f1, f2)
    }

    override fun area(): Double {
        return PI * a * b
    }

    override fun perimeter(): Double {
        return PI * (3 * (a + b) - sqrt((3 * a + b) * (a + 3 * b)))
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Ellipse::class, name)) return false
        return NamePattern.extractPointName(Ellipse::class, this.name!!).toSet() ==
            NamePattern.extractPointName(Ellipse::class, name).toSet()
    }

    override fun mergeFrom(other: Element) {
        if (other !is Ellipse) return
        super.mergeFrom(other)

        _a = other.a
        _b = other.b

        _vC = Vector3D.of((f1.x + f2.x) / 2, (f1.y + f2.y) / 2, (f1.z + f2.z) / 2)
        val v0 = Vector3D.of(center.x + 10, center.y, center.z)
        val vecC0 = center.vectorTo(v0)
        val vecCE = center.vectorTo(f2.coordinates())
        _angle = vecC0.angleTo(vecCE)
    }
}
