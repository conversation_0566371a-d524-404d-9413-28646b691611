package viclass.editor.geo.impl.koin

import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import org.koin.core.component.KoinComponent
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.entity.ConstructorTemplate

@Module
@ComponentScan("viclass.editor.geo.impl.constructor")
class ConstructorModule : KoinComponent {

    private fun pair(tpl: ConstructorTemplate): Pair<String, ConstructorTemplate> = tpl.id to tpl

    @Singleton
    @Named(CONSTRUCTOR_TEMPLATES)
    fun provideConstructorTemplates(): Map<String, ConstructorTemplate> {
        val ls: List<ElementConstructor<*>> = getKoin().getAll<ElementConstructor<*>>()
        return ls.associate { pair(it.template()) }
    }
}
