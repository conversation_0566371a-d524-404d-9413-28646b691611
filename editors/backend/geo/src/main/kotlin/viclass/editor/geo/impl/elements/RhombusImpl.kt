package viclass.editor.geo.impl.elements

import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Rhombus
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.Distances
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
open class RhombusImpl(
    doc: GeoDoc, name: String?,
    point1: Point, point2: Point, point3: Point, point4: Point
): Rhombus, ParallelogramImpl(doc, name, point1, point2, point3, point4) {
    override val clazz: KClass<out Rhombus> = Rhombus::class

    /**
     * Validates that the element is a Rhombus by ensuring it is a Parallelogram and all sides are of equal length.
     *
     * This function extends the validation of a Parallelogram by specifically checking if the
     * lengths of adjacent sides are equal, thus confirming it is a Rhombus. It calculates the
     * distances between consecutive points (point1, point2) and (point2, point3) and compares
     * these lengths within a predefined tolerance (DEFAULT_TOLERANCE) to account for floating-point
     * precision.
     *
     * If the absolute difference between the lengths of the adjacent sides exceeds the specified
     * tolerance, it signifies that the Parallelogram does not meet the criteria of a Rhombus, and an
     * InvalidElementException is thrown.
     *
     * @throws InvalidElementException if the Parallelogram is not a Rhombus due to unequal side lengths.
     */
    override fun validate() {
        // Invoke the base Parallelogram validation logic to ensure it's a valid Parallelogram first.
        super<ParallelogramImpl>.validate()

        // Calculate the length of the side formed by points.
        val side12 = Distances.of(point1, point2)
        val side23 = Distances.of(point2, point3)

        // Check if the lengths of the adjacent sides are equal within the allowed tolerance.
        if (Math.abs(side12 - side23) > DEFAULT_TOLERANCE) {
            // If the difference exceeds the tolerance, it's not a Rhombus, throw an exception.
            throw InvalidElementException("$name is not a rhombus")
        }
    }

    override fun mergeFrom(other: Element) {
        if (other !is RhombusImpl) return
        super<ParallelogramImpl>.mergeFrom(other)
    }
}
