package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.geometry.euclidean.threed.line.Lines3D
import org.apache.commons.geometry.euclidean.twod.Vector2D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.abs


/**
 *
 * <AUTHOR>
 */
object Points {
    fun calculateCenterPoint(doc: GeoDoc, name: String?, point1: Point, point2: Point): Point {
        val x = (point1.x + point2.x) / 2
        val y = (point1.y + point2.y) / 2
        val z = (point1.z + point2.z) / 2

        return PointImpl(doc, name, x, y, z)
    }

    fun calculateCenterPoint(point1: Point, point2: Point): Vector3D {
        val x = (point1.x + point2.x) / 2
        val y = (point1.y + point2.y) / 2
        val z = (point1.z + point2.z) / 2

        return Vector3D.of(x, y, z)
    }

    fun isBetweenTwoPoints(ref: Vector3D, v1: Vector3D, v2: Vector3D): Boolean {
        val distanceRefV1 = Distances.of(ref, v1)
        val distanceRefV2 = Distances.of(ref, v2)
        val distanceV1V2 = Distances.of(v1, v2)

        return abs(distanceRefV1 + distanceRefV2 - distanceV1V2) < DEFAULT_TOLERANCE
    }

    fun calculateCenterPoint(vertices: List<Point>): Vector3D {
        var x = 0.0
        var y = 0.0
        var z = 0.0
        for (p in vertices) {
            x += p.x
            y += p.y
            z += p.z
        }
        x /= vertices.size.toDouble()
        y /= vertices.size.toDouble()
        z /= vertices.size.toDouble()
        return Vector3D.of(x, y, z)
    }

    fun isNewPoint(doc: GeoDoc, p: Point): Boolean = p.valid && !p.usable
}

/**
 * Calculates the shortest distance from this point to the line defined by
 * the point `vPoint` and the direction vector `v`.
 *
 * @param vPoint a point on the line
 * @param v the direction vector of the line
 * @return the perpendicular distance from this point to the line
 */
fun Vector3D.distanceToLine(vPoint: Vector3D, v: Vector3D): Double {
    // Vector from vPoint to this point
    val ap = this - vPoint
    // Cross product gives area of parallelogram formed by ap and v
    val cross = ap.cross(v)
    // Distance is area divided by base (norm of v)
    return cross.norm() / v.norm()
}

fun Vector3D.distancePointToLineSegment(p1: Vector3D, p2: Vector3D): Double {
    // Create a line segment from two points p1 and p2
    val lineSegment = Lines3D.segmentFromPoints(p1, p2, DEFAULT_PRECISION)

    // Calculate the vector from p1 to p2 (direction of the segment)
    val lineDirection = p2.subtract(p1)

    // Calculate the vector from p1 to this point
    val pointToP1 = this.subtract(p1)

    // Calculate the projection ratio (equivalent to projecting the point onto the segment)
    val t = pointToP1.dot(lineDirection) / lineDirection.normSq()

    // If t is in [0, 1], the projection falls within the segment
    return if (t in 0.0..1.0) {
        // Calculate the projected point on the segment
        val projectedPoint = p1.add(lineDirection.multiply(t))
        // Distance from this point to the projected point
        this.distance(projectedPoint)
    } else {
        // If t is not in [0, 1], the closest distance is to one of the segment's endpoints
        val distToP1 = this.distance(p1)
        val distToP2 = this.distance(p2)
        // Return the minimum distance to the endpoints
        minOf(distToP1, distToP2)
    }
}

// Function to project a point from 3D → 2D (in a plane)
fun Vector3D.projectTo2D(
    origin: Vector3D,
    u: Vector3D.Unit,
    v: Vector3D.Unit
): Vector2D {
    val relative = this.subtract(origin)
    return Vector2D.of(relative.dot(u), relative.dot(v))
}