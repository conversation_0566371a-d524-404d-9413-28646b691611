package viclass.editor.geo.impl.constructor.symmetry

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.elements.*

fun projectPointOntoLine(point: DoubleArray, startPoint: DoubleArray, vector: DoubleArray): DoubleArray {
    // Calculate the dot product of the vector formed by the start point and the point to be
    // projected
    val dotProduct = (point[0] - startPoint[0]) * vector[0] + (point[1] - startPoint[1]) * vector[1]

    // Normalize the vector
    val magnitudeSquared = vector[0] * vector[0] + vector[1] * vector[1]
    val normalizedVector = doubleArrayOf(vector[0] / magnitudeSquared, vector[1] / magnitudeSquared)

    // Project the point onto the line
    val projection =
        doubleArrayOf(
            startPoint[0] + dotProduct * normalizedVector[0],
            startPoint[1] + dotProduct * normalizedVector[1]
        )

    return projection
}

fun symmetricPoint(originalPoint: DoubleArray, middlePoint: DoubleArray): DoubleArray {
    return doubleArrayOf(2 * middlePoint[0] - originalPoint[0], 2 * middlePoint[1] - originalPoint[1])
}

fun symmetricPointByLine(originalPoint: DoubleArray, startPoint: DoubleArray, vector: DoubleArray): DoubleArray {
    val middlePoint = projectPointOntoLine(originalPoint, startPoint, vector)
    return symmetricPoint(originalPoint, middlePoint)
}

fun createSymmetricPoint(
    doc: GeoDoc,
    coord: DoubleArray,
    name: String? // Input parameter for the point name
): ConstructionResultImpl<Point> {
    val cr = ConstructionResultImpl<Point>()
    val actualName = name ?: generatePointName(doc) // Generate name if input is null
    val newpoint = PointImpl(doc, actualName, coord[0], coord[1])
    cr.setResult(newpoint)
    return cr
}

fun createSymmetricLineSegment(
    doc: GeoDoc,
    coord1: DoubleArray,
    coord2: DoubleArray,
    name1: String?, // Input parameter for the first point's name
    name2: String? // Input parameter for the second point's name
): ConstructionResultImpl<LineVi> {
    val cr = ConstructionResultImpl<LineVi>()

    val actualName1 = name1 ?: generatePointName(doc)
    val actualName2 =
        name2 ?: generatePointName(doc, actualName1) // Generate name2 if input is null, potentially using actualName1

    val newpoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])
    val newpoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])
    val newLine = LineSegmentImpl(doc, "${newpoint1.name}${newpoint2.name}", newpoint1, newpoint2)

    cr.setResult(newLine)
    cr.addDependency(newpoint1, emptyList(), true)
    cr.addDependency(newpoint2, emptyList(), true)
    return cr
}

fun createSymmetricLineVi(
    doc: GeoDoc,
    line: LineVi,
    coord1: DoubleArray,
    coord2: DoubleArray?,
    name1: String?, // Input parameter for the first point's name
    name2: String? // Input parameter for the second point's name (if applicable)
): ConstructionResultImpl<LineVi> {
    val cr = ConstructionResultImpl<LineVi>()

    val actualName1 = name1 ?: generatePointName(doc)
    val newPoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])
    cr.addDependency(newPoint1, emptyList(), true)

    val newLine: LineVi =
        if (coord2 != null) {
            val actualName2 = name2 ?: generatePointName(doc, actualName1)
            val newPoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])
            cr.addDependency(newPoint2, emptyList(), true)

            val vector = createVectorByEl(doc, newPoint1, newPoint2).let { Vector3D.of(it.x, it.y, 0.0) }
            LineImpl(doc, "${newPoint1.name}${newPoint2.name}", newPoint1, vector, newPoint2)
        } else {
            LineImpl(doc, newPoint1.name, newPoint1, line.parallelVector)
        }

    cr.setResult(newLine)
    return cr
}

fun createSymmetricVector(
    doc: GeoDoc,
    coord1: DoubleArray,
    coord2: DoubleArray,
    name1: String?,
    name2: String?
): ConstructionResultImpl<LineVi> {
    val cr = ConstructionResultImpl<LineVi>()

    val actualName1 = name1 ?: generatePointName(doc)
    val actualName2 = name2 ?: generatePointName(doc, actualName1)

    val newPoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])
    val newPoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])

    val newVector = VectorImpl(doc, "${newPoint1.name}${newPoint2.name}", newPoint1, newPoint2)

    cr.setResult(newVector)
    cr.addDependency(newPoint1, emptyList(), true)
    cr.addDependency(newPoint2, emptyList(), true)
    return cr
}

fun createSymmetricRay(
    doc: GeoDoc,
    line: Ray,
    coord1: DoubleArray,
    coord2: DoubleArray?,
    name1: String?, // Input parameter for the first point's name
    name2: String? // Input parameter for the second point's name (if applicable)
): ConstructionResultImpl<LineVi> {
    val cr = ConstructionResultImpl<LineVi>()

    val actualName1 = name1 ?: generatePointName(doc)
    val newPoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])

    if (coord2 != null) {
        val actualName2 = name2 ?: generatePointName(doc, actualName1)
        val newPoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])
        val vector = createVectorByEl(doc, newPoint1, newPoint2).let { Vector3D.of(it.x, it.y, 0.0) }
        val newRay = RayImpl(doc, "${newPoint1.name}${newPoint2.name}", newPoint1, vector, newPoint2)
        cr.setResult(newRay)
        cr.addDependency(newPoint2, emptyList(), true)
    } else {
        val newRay = RayImpl(doc, newPoint1.name, newPoint1, line.parallelVector) // Use actualName1 via newPoint1.name
        cr.setResult(newRay)
    }

    cr.addDependency(newPoint1, emptyList(), true)

    return cr
}

fun createSymmetricCircle(
    doc: GeoDoc,
    radius: Double,
    centerCoord: DoubleArray,
    centerName: String? // Input parameter for the center point's name
): ConstructionResultImpl<Circle> {
    val cr = ConstructionResultImpl<Circle>()

    val actualCenterName = centerName ?: generatePointName(doc)
    val newCenterPoint = PointImpl(doc, actualCenterName, centerCoord[0], centerCoord[1])

    val newCircle =
        CircleImpl(doc, actualCenterName, newCenterPoint, radius) // Name of circle often same as center point

    cr.setResult(newCircle)
    cr.addDependency(newCenterPoint, emptyList(), true)
    return cr
}

fun createSymmetricEllipse(
    doc: GeoDoc,
    ellipsea: Double,
    ellipseb: Double,
    focus1Coord: DoubleArray,
    focus2Coord: DoubleArray,
    focus1Name: String?,
    focus2Name: String?
): ConstructionResultImpl<Ellipse> {
    val cr = ConstructionResultImpl<Ellipse>()

    val actualFocus1Name = focus1Name ?: generatePointName(doc)
    val newFocus1 = PointImpl(doc, actualFocus1Name, focus1Coord[0], focus1Coord[1])

    val actualFocus2Name = focus2Name ?: generatePointName(doc, actualFocus1Name)
    val newFocus2 = PointImpl(doc, actualFocus2Name, focus2Coord[0], focus2Coord[1])

    val newEllipse = EllipseImpl(doc, "${newFocus1}${newFocus2}", newFocus1, newFocus2, ellipsea, ellipseb)

    cr.setResult(newEllipse)
    cr.addDependency(newFocus1, emptyList(), true)
    cr.addDependency(newFocus2, emptyList(), true)
    return cr
}

fun createSymmetricCircularSector(
    doc: GeoDoc,
    coord1: DoubleArray, // Center point
    coord2: DoubleArray, // Start point of arc
    coord3: DoubleArray, // End point of arc
    name1: String?, // Name for center point
    name2: String?, // Name for start arc point
    name3: String? // Name for end arc point
): ConstructionResultImpl<CircularSector> {
    val cr = ConstructionResultImpl<CircularSector>()

    val actualName1 = name1 ?: generatePointName(doc)
    val newpoint1 = PointImpl(doc, actualName1, coord1[0], coord1[1])

    val actualName2 = name2 ?: generatePointName(doc, actualName1)
    val newpoint2 = PointImpl(doc, actualName2, coord2[0], coord2[1])

    val actualName3 = name3 ?: generatePointName(doc, actualName1, actualName2)
    val newpoint3 = PointImpl(doc, actualName3, coord3[0], coord3[1])

    val newCircularSector =
        CircularSectorImpl(
            doc,
            "${newpoint1.name}${newpoint2.name}${newpoint3.name}", // Name of the sector itself
            newpoint1, // Center
            newpoint2, // Start
            newpoint3 // End
        )

    cr.setResult(newCircularSector)
    cr.addDependency(newpoint1, emptyList(), true)
    cr.addDependency(newpoint2, emptyList(), true)
    cr.addDependency(newpoint3, emptyList(), true)

    return cr
}
