package viclass.editor.geo.impl.extractors

import common.libs.evaluator.Evaluator
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.dbentity.paramstore.ParamStore
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.extractors.PointWithCoordsExtractor.Companion.isPointWithCoordsValue
import kotlin.reflect.KClass

@Singleton
class PointExtractor(private val evaluator: Evaluator) : ParameterExtractor {
    override var id: String = javaClass.simpleName

    @Throws(ElementNotExistInDocumentException::class)
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> extractConstructionResult(
        doc: GeoDoc,
        pk: String,
        ctIdx: Int,
        clazz: KClass<out T>?
    ): ConstructionResult<T> {
        val pointName = evaluator.extractElementName(pk, listOf("Hinh", "Diem"))
        val p = doc.findElementByName(pointName, Point::class, ctIdx)
            ?: throw ElementNotExistInDocumentException("There is no point $pointName")

        val c = ConstructionResultImpl<Point>()
        c.setResult(p)

        return c as ConstructionResult<T>
    }

    override fun supportedTypes(): List<KClass<*>> {
        return listOf(Point::class)
    }

    override fun isSupport(doc: GeoDoc, type: KClass<*>, store: ParamStore?): Boolean {
        return super.isSupport(doc, type, store) && !isPointWithCoordsValue(doc, store)
    }
}
