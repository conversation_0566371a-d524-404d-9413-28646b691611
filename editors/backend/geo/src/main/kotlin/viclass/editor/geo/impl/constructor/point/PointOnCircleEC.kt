package viclass.editor.geo.impl.constructor.point

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementCirclePath
import viclass.editor.geo.dbentity.transformdata.PointOnArcWithAngleTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aCircle
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.transformer.PointOnArcWithAngleTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class PointOnCircleEC : ElementConstructor<Point> {
    private enum class CGS {
        OnCircleWithDegree, OnCircleWithRadian
    }

    override fun outputType(): KClass<Point> {
        return Point::class
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.OnCircleWithDegree.name)
                    .hints("PointOnCircleWithDegree")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aCircle]!!,
                        listOf("NameOfCircle"),
                        "tpl-OnCircle"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Degree"),
                        "tpl-AngleDegree"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.OnCircleWithRadian.name)
                    .hints("PointOnCircleWithRadian")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aCircle]!!,
                        listOf("NameOfCircle"),
                        "tpl-OnCircle"
                    )
                    .constraintDepends(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Radian"),
                        "tpl-AngleRadian"
                    )
                    .build(),
            )
            .elTypes(Point::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.OnCircleWithDegree, CGS.OnCircleWithRadian -> {
                Validations.validateNumConstraints(c, 2)
                constructOnCircleWithAngle(doc, inputName, c)
            }
        }
    }

    private fun constructOnCircleWithAngle(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Point> {
        val result = ConstructionResultImpl<Point>()

        var circle: Circle? = null
        var angle: Double? = null

        c.params.forEach {
            when (it.paramDef.id) {
                aCircle -> {
                    val extractedCircleResult =
                        extractFirstPossible<ElementExtraction<Circle>>(doc, ParamKind.PK_Name, it, c.ctIdx)
                    circle = extractedCircleResult.result.result()!!
                    result.mergeAsDependency(extractedCircleResult.result)
                }

                aValue -> {
                    val extractedRangeResult =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, it, c.ctIdx)
                    angle = extractedRangeResult.result
                }
            }
        }

        if (circle == null || angle == null) {
            throw ConstructionException("missing circle or angle")
        }

        if (c.cgName == CGS.OnCircleWithDegree.name) angle = radian(angle)

        val name = inputName ?: generatePointName(doc)

        val point = Circles.calculatePointOnCircleWithRadian(doc, name, circle, angle)

        point.transformData = PointOnArcWithAngleTransformData(
            1,
            ParamKind.PK_Value,
            circle.centerPoint.coordinates().toArray(),
            degree = c.cgName == CGS.OnCircleWithDegree.name
        )
        point.transformer = TransformMapping.fromClazz(PointOnArcWithAngleTransformer::class)
        point.movementPath = MovementCirclePath(circle.centerPoint.coordinates().toArray(), circle.radius)

        result.setResult(point)

        return result
    }
}
