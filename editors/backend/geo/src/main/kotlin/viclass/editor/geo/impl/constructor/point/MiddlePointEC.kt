package viclass.editor.geo.impl.constructor.point

import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.reflect.KClass

@Singleton
class MiddlePointEC : ElementConstructor<Point> {
    override fun outputType(): KClass<Point> {
        return Point::class
    }

    enum class CGS {
        UseTwoPoints, LineSegment
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.UseTwoPoints.name)
                    .hints("MiddlePointOfPoints")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfPoint", "NameOfPoint"),
                        "tpl-MiddlePointOfPoints"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.LineSegment.name)
                    .hints("MidpointOfLineSegment")
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-MiddlePointOfLineSegment",
                        "tpl-MidpointOf"
                    )
                    .build()
            )
            .elTypes(Point::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.UseTwoPoints -> {
                Validations.validateNumConstraints(c, 1)
                constructWithTwoPoints(doc, inputName, c)
            }
            CGS.LineSegment -> {
                Validations.validateNumConstraints(c, 1)
                constructWithLineSegment(doc, inputName, c)
            }
        }
    }

    private fun constructWithLineSegment(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val c1 = c.params[0]
        val er = extractFirstPossible<ElementExtraction<LineSegment>>(doc, PK_Name, c1, c.ctIdx)

        val cr = er.result
        val line : LineSegment? = cr.result()
        val points = line!!.vertices()

        val name = inputName ?: generatePointName(doc)

        val result = calculatePoint(doc, name, points[0], points[1])
        result.mergeAsDependency(cr)

        return result
    }

    @Throws(RuntimeException::class)
    private fun constructWithTwoPoints(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Point> {
        val c1 = c.params[0]
        val er = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c1, c.ctIdx)

        val cr = er.result

        if (cr.size < 2 || cr[0].result() == null || cr[1].result() == null)
            throw ConstructionException("Cannot extract the correct number of points from constraint")

        val p1 = cr[0].result()!!
        val p2 = cr[1].result()!!

        val name = inputName ?: generatePointName(doc)

        val result = calculatePoint(doc, name, p1, p2)

        // here we don't have to merge the construction result of the two points
        // because we know that our result only depend directly on these two points
        // and no other thing.
        result.addDependency(p1, listOf(), true)
        result.addDependency(p2, listOf(), true)
        return result
    }

    private fun calculatePoint(doc: GeoDoc, name: String?, point: Point, point1: Point): ConstructionResultImpl<Point> {
        val c = ConstructionResultImpl<Point>()
        val x = (point.x + point1.x) / 2
        val y = (point.y + point1.y) / 2
        val z = (point.z + point1.z) / 2

        c.setResult(PointImpl(doc, name, x, y, z))
        return c
    }
}
