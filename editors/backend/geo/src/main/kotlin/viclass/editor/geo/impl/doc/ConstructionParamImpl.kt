package viclass.editor.geo.impl.doc

import viclass.editor.geo.constructor.ConstructionParams
import viclass.editor.geo.constructor.ParameterExtractor
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.entity.ConstraintParamDef

class ConstructionParamImpl(override val specs: ParamSpecs, override val paramDef: ConstraintParamDef) :
        ConstructionParams {
    override var paramExtractors : Map<String, ParameterExtractor> = mutableMapOf()
    override var candidateExtractors: Map<String, List<ParameterExtractor>> = mutableMapOf()

    override fun useExtractor(paramKind: String, extractor: ParameterExtractor) {
        this.setExtractorForParam(paramKind, extractor)
        this.specs.extractorForParam(paramKind, extractor.id)
    }

    private fun setExtractorForParam(paramName : String, extractor : ParameterExtractor) {
        (paramExtractors as MutableMap)[paramName] = extractor
    }
}
