package viclass.editor.geo.server

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import viclass.editor.geo.impl.controller.ConstructionController
import viclass.editor.geo.impl.controller.DocumentController
import viclass.editor.geo.impl.controller.TemplateController

/**
 *
 * <AUTHOR>
 */
fun Route.constructorTemplateRouting(c: TemplateController) {

    get("/template/reload") {
        try {
            c.reloadMappingTextCache()
            call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            logger.error("reload mapping text cache exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/template/fetch") {
        try {
            c.fetch(call)
        } catch (t: Throwable) {
            logger.error("fetch template exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/mapping/getStringById") {
        try {
            c.getStringById(call)
        } catch (t: Throwable) {
            logger.error("get string exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }
}

/**
 * Routing for applying construction
 */
fun Route.constructionRouting(c: ConstructionController) {
    post("/document/{docId}/construct") {
        try {
            c.applyConstruction(call)
        } catch (t: Throwable) {
            logger.error("apply construction exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/document/{docId}/reconstruct") {
        try {
            c.reconstruct(call)
        } catch (t: Throwable) {
            logger.error("reconstruct exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/document/{docId}/element/move") {
        try {
            c.moveElement(call)
        } catch (t: Throwable) {
            logger.error("move element exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }
}

fun Route.documentRouting(c: DocumentController) {
    post("/document/create") {
        try {
            c.create(call)
        } catch (t: Throwable) {
            logger.error("create document exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    get("/document/fetch") {
        try {
            c.fetch(call)
        } catch (t: Throwable) {
            logger.error("fetch document exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/documents/delete") {
        try {
            c.deleteDocuments(call)
        } catch (t: Throwable) {
            logger.error("delete documents exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/documents/duplicate") {
        try {
            c.duplicateDocuments(call)
        } catch (t: Throwable) {
            logger.error("duplicate documents exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/document/{docId}/delete") {
        try {
            c.deleteDocument(call)
        } catch (t: Throwable) {
            logger.error("delete document exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/document/{docId}/rel/delete") {
        try {
            c.deleteRenderElements(call)
        } catch (t: Throwable) {
            logger.error("delete render elements exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/document/{docId}/el/vertex/rename") {
        try {
            c.renamePointElement(call)
        } catch (t: Throwable) {
            logger.error("rename element exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/document/{docId}/els/state/update") {
        try {
            c.updateElementsState(call)
        } catch (t: Throwable) {
            logger.error("update elements state exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

    post("/cmd") {
        try {
            c.processCmd(call)
        } catch (t: Throwable) {
            logger.error("process cmd exception... ", t)
            call.respond(HttpStatusCode.InternalServerError)
        }
    }

}


