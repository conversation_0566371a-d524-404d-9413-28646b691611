package viclass.editor.geo.impl.constructor.triangle

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementFreePath
import viclass.editor.geo.dbentity.transformdata.FreePointTransformData
import viclass.editor.geo.dbentity.transformdata.MoveOrderTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.EquilateralTriangle
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.constructor.generatePointName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.EquilateralTriangleImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.FreePointTransformer
import viclass.editor.geo.impl.transformer.MoveOrderTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.math.sqrt
import kotlin.reflect.KClass

/**
 * EquilateralTriangleEC is responsible for constructing an equilateral triangle
 * from two points (defining one side), and constructs the apex based on optional orientation.
 * This design mirrors IsoscelesRightTriangleEC.
 *
 * <AUTHOR>
 */
@Singleton
class EquilateralTriangleEC : ElementConstructor<EquilateralTriangle>, Logging {
    override fun outputType(): KClass<EquilateralTriangle> = EquilateralTriangle::class

    private enum class CGS { FromTwoPosition }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromTwoPosition.name)
                    .numDim(3)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value", "Value", "Value"),
                        "tpl-3DPoint"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
            )
            .elTypes(EquilateralTriangle::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<EquilateralTriangle> =
        when (CGS.valueOf(c.cgName)) {
            CGS.FromTwoPosition -> {
                Validations.validateNumConstraints(c, 2)
                constructFromTwoPosition(doc, inputName, c)
            }
        }

    /**
     * Construct an equilateral triangle from two positions (defining one side, with the second position used as a tip or root).
     * This is directly analogous to the IsoscelesRightTriangleEC construction.
     */
    private fun constructFromTwoPosition(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<EquilateralTriangle> {
        val ext1 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[0], c.ctIdx)
        val ext2 = extractFirstPossible<NumberListExtraction<Double>>(doc, PK_Value, c.params[1], c.ctIdx)
        val ext3 = if (c.params.size > 2)
            extractFirstPossible<NumberExtraction<Int>>(doc, PK_Value, c.params[2], c.ctIdx)
        else null
        val nth: Int = ext3?.result ?: 1
        // If the order is 2, rotate -60 degrees; if 1, rotate +60 degrees
        val sig: Int = if (nth == 2) -1 else 1

        val pointCoords1 = ext1.result
        val pointCoords2 = ext2.result

        val pointsName: List<String> = if (!inputName.isNullOrBlank()) {
            NamePattern.extractPointName(Polygon::class, inputName)
        } else {
            (0..2).map { generatePointName(doc, ArrayList<String>()) }
        }
        if (pointsName.size < 3) throw ConstructionException("Cần ít nhất 3 tên điểm để tạo tam giác.")

        val p_a = PointImpl(doc, pointsName[0], pointCoords1[0], pointCoords1[1], pointCoords1.getOrElse(2) { 0.0 })
        val p_b = PointImpl(doc, pointsName[1], pointCoords2[0], pointCoords2[1], pointCoords2.getOrElse(2) { 0.0 })

        val v_a = p_a.coordinates()
        val v_b = p_b.coordinates()

        val dx = v_b.x - v_a.x
        val dy = v_b.y - v_a.y
        val l = sqrt(dx * dx + dy * dy)
        // To create an equilateral triangle, the remaining vertex is rotated around p_b by 60 or -60 degrees with p_a as the center
        val angle = Math.PI / 3 * sig // 60 degrees
        // Calculate the coordinates of the third vertex by rotating v_a around v_b by 60 degrees
        val x3 = v_a.x + (dx * Math.cos(angle) - dy * Math.sin(angle))
        val y3 = v_a.y + (dx * Math.sin(angle) + dy * Math.cos(angle))
        val z3 = v_a.z + (v_b.z - v_a.z) // Maintain the plane (if 2D, this is 0)

        val p_c = PointImpl(doc, pointsName[2], x3, y3, z3)

        // Configure transform/move similar to IsoscelesRightTriangleEC: the first two points are free, the third point is dependent
        listOf(p_a to 0, p_b to 1).forEach { (p, idx) ->
            p.transformData = FreePointTransformData(idx)
            p.transformer = TransformMapping.fromClazz(FreePointTransformer::class)
            p.movementPath = MovementFreePath()
        }

        // The third point moves dependently on the first two points
        p_c.transformData =
            MoveOrderTransformData(p_b.coordinates().toArray(), p_c.coordinates().toArray(), listOf(0, 1))
        p_c.transformer = TransformMapping.fromClazz(MoveOrderTransformer::class)
        p_c.movementPath = MovementFreePath()

        val triangle = EquilateralTriangleImpl(doc, pointsName.joinToString(""), p_a, p_b, p_c)

        return ConstructionResultImpl<EquilateralTriangle>().apply {
            setResult(triangle)
            addDependencies(listOf(p_a, p_b), true)
            addDependency(p_c, listOf(p_a, p_b), true)
        }
    }
}
