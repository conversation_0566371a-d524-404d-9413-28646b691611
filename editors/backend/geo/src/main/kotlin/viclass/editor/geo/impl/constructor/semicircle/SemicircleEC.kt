package viclass.editor.geo.impl.constructor.semicircle

import kotlin.reflect.KClass
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Semicircle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.generateLowercaseName
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.SemicircleImpl

/**
 *
 * <AUTHOR>
 */
@Singleton
class SemicircleEC : ElementConstructor<Semicircle> {

    override fun outputType(): KClass<Semicircle> {
        return Semicircle::class
    }

    private enum class CGS {
        ByPointsName
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create().name(CGS.ByPointsName.name).constraintDefault().build(),
            )
            .elTypes(Semicircle::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Semicircle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.ByPointsName -> {
                Validations.validateNumConstraints(c, 0)
                constructFromName(doc, inputName!!, c)
            }
        }
    }

    private fun constructFromName(doc: GeoDoc, inputName: String, c: Construction): ConstructionResult<Semicircle> {
        val names = NamePattern.extractPointName(Semicircle::class, inputName)
        val vertexes =
            names.map {
                doc.findElementByName(it, Point::class, c.ctIdx)
                    ?: throw ElementNotExistInDocumentException("not found vertex")
            }

        // Validate that vertexes[1] is the center of vertexes[0] and vertexes[2]
        val centerX = (vertexes[0].x + vertexes[2].x) / 2
        val centerY = (vertexes[0].y + vertexes[2].y) / 2
        if (vertexes[1].x != centerX || vertexes[1].y != centerY) {
            throw IllegalArgumentException("vertexes[1] is not the center of vertexes[0] and vertexes[2]")
        }

        val cr = ConstructionResultImpl<Semicircle>()
        cr.setResult(SemicircleImpl(doc, generateLowercaseName(doc,arrayListOf()), vertexes[1], vertexes[0], vertexes[2]))
        cr.addDependencies(vertexes, true)
        cr.addDependency(vertexes[1], vertexes, true)

        return cr
    }
}
