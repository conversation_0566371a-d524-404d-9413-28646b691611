package viclass.editor.geo.impl.extractors

import com.fasterxml.jackson.databind.JsonNode
import common.libs.evaluator.Evaluator
import viclass.editor.geo.exceptions.ExtractionFailedException

@Throws(ExtractionFailedException::class)
fun extractElementName(root: List<JsonNode>, allowOperators: List<String>): String? {
    if (root.isEmpty() || !root[0].isTextual)
        throw ExtractionFailedException("MathJSON can not be empty")
    val opr = root[0].asText()
    if (!allowOperators.contains(opr))
        throw ExtractionFailedException("Operator $opr not supported")

    if (root.size > 2 || !root[1].isTextual) {
        throw throw ExtractionFailedException("Invalid element name")
    }

    val name = root[1].asText()
    if (name.isEmpty()) throw ExtractionFailedException("Element name can not be empty")
    if (name.startsWith("'") && name.endsWith("'")) {
        return name.substring(1, name.length - 1)
    }

    return name
}

fun String.surroundedBy(start: String, end: String = start): Boolean = this.startsWith(start) && this.endsWith(end)

fun String.possiblyMathJSON(): Boolean = this.surroundedBy("[", "]") || this.surroundedBy("\"")

fun String.possiblyBoolean(): Boolean =
    this.equals("true", ignoreCase = true) || this.equals("false", ignoreCase = true)

fun Evaluator.extractElementName(pk: String, allowOperators: List<String>): String? {
    val formatPk = pk.trim()
    if (formatPk.isEmpty()) return null

    // only do extraction if this is possibly a MathJSON with root operator (surrounded by [])
    if (formatPk.possiblyMathJSON())
        return extractRootOperator(formatPk) { extractElementName(it, allowOperators) }

    return formatPk
}
