syntax = "proto3";

package viclass.proto.geometry.msg;

enum GeoKind {
  Unknown = 0;
  Geo2D = 1;
  Geo3D = 2;
}

message BoundaryProto {
  PositionProto start = 1;
  PositionProto end = 2;
}

message PositionProto {
  double x = 1;
  double y = 2;
  double z = 3;
}

message CoordsProto {
  repeated double coords = 1;
}

message StringWrapper {
  string value = 1;
}

message In32Wrapper {
  int32 value = 1;
}

message RenderVertexProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  CoordsProto coords = 9;
  optional string movement_path = 10;
}
message PreviewVertexProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  CoordsProto coords = 8;
  bool unselectable = 9;
}

enum LineTypeProto {
  UNKNOWN = 0;
  LINE = 1;
  RAY = 2;
  SEGMENT = 3;
  VECTOR = 4;
}

message RenderLineProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  int32 start_point_idx = 9;
  optional int32 end_point_idx = 10;
  repeated double vector = 11;
  LineTypeProto line_type = 12;
  string el_type = 13;
  repeated int32 vertex_rel_idxes = 14;
}
message PreviewLineProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  CoordsProto start_point = 8;
  optional CoordsProto end_point = 9;
  LineTypeProto line_type = 10;
  string el_type = 11;
  CoordsProto vector = 12;
  bool unselectable = 13;
}

message RenderPolygonProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  repeated int32 faces = 9;
  repeated int32 vertex_rel_idxes = 10;
  repeated int32 line_rel_idxes = 11;
  string el_type = 12;
}
message PreviewPolygonProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  repeated CoordsProto faces = 8;
  bool unselectable = 9;
  string el_type = 12;
}

message RenderCircleShapeProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  double radius = 9;
  int32 center_point_idx = 10; // index of the vertex that is the center
  repeated int32 vertex_rel_idxes = 11;
  repeated int32 line_rel_idxes = 12;
  int32 arc_idx = 13;
}
message PreviewCircleShapeProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  double radius = 8;
  CoordsProto center_point = 9; // the coord that is the center
  bool unselectable = 10;
}

message RenderSectorShapeProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  int32 center_point_idx = 9;
  int32 start_point_idx = 10;
  int32 end_point_idx = 11;
  double radius = 12;
  repeated int32 vertex_rel_idxes = 13;
  repeated int32 line_rel_idxes = 14;
  int32 arc_idx = 15;
  string el_type = 16;
}
message PreviewSectorShapeProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  CoordsProto center_point = 8;
  CoordsProto start_point = 9;
  CoordsProto end_point = 10;
  double radius = 11;
  bool unselectable = 12;
  string el_type = 13;
}

message RenderSectorProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  int32 center_point_idx = 9;
  In32Wrapper start_point_idx = 10;
  In32Wrapper end_point_idx = 11;
  double radius = 12;
  repeated int32 vertex_rel_idxes = 13;
  string el_type = 14;
}
message PreviewSectorProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  int32 center_point_idx = 8;
  In32Wrapper start_point_idx = 9;
  In32Wrapper end_point_idx = 10;
  double radius = 11;
  repeated int32 vertex_rel_idxes = 12;
  bool unselectable = 13;
  string el_type = 14;
}

message RenderCircleProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  int32 center_point_idx = 9;
  In32Wrapper end_point_idx = 11;
  double radius = 12;
  repeated int32 vertex_rel_idxes = 13;
  string el_type = 14;
}
message PreviewCircleProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  int32 center_point_idx = 8;
  In32Wrapper end_point_idx = 10;
  double radius = 11;
  repeated int32 vertex_rel_idxes = 12;
  bool unselectable = 13;
  string el_type = 14;
}

message RenderEllipseProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  int32 f1_idx = 9;
  int32 f2_idx = 10;
  double a = 11;
  double b = 12;
  double rotate = 13;
  repeated int32 vertex_rel_idxes = 14;
}
message PreviewEllipseProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  CoordsProto f1 = 8;
  CoordsProto f2 = 9;
  double a = 10;
  double b = 11;
  double rotate = 12;
  repeated int32 vertex_rel_idxes = 13;
  bool unselectable = 14;
}

message RenderEllipseShapeProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  int32 f1_idx = 9;
  int32 f2_idx = 10;
  double a = 11;
  double b = 12;
  double rotate = 13;
  repeated int32 vertex_rel_idxes = 14;
  int32 arc_idx = 15;
}
message PreviewEllipseShapeProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  CoordsProto f1 = 8;
  CoordsProto f2 = 9;
  double a = 10;
  double b = 11;
  double rotate = 12;
  bool unselectable = 13;
}

message RenderAngleProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  optional bool deleted = 5;
  bool valid = 6;
  double area = 7;
  double perimeter = 8;
  int32 angle_point_idxes = 9;
  CoordsProto v_start = 10;
  CoordsProto v_end = 11;
  repeated int32 vertex_rel_idxes = 12;
  repeated int32 line_rel_idxes = 13;
  double degree = 14;
}
message PreviewAngleProto {
  int32 rel_index = 1;
  string name = 2;
  ElRenderPropsProto render_prop = 3;
  bool usable = 4;
  bool valid = 5;
  double area = 6;
  double perimeter = 7;
  CoordsProto angle_point = 8;
  CoordsProto start_point = 9;
  CoordsProto end_point = 10;
  CoordsProto v_start = 11;
  CoordsProto v_end = 12;
  bool unselectable = 13;
}

message ElRenderPropsProto {
  optional string color = 1;
  optional string stroke_style = 2;
  optional uint32 line_weight = 3;
  optional bool hidden = 4;
  optional uint32 opacity = 5;
  optional string label = 6;
  optional bool swap_label_position = 7;
  optional bool show_background = 8;
  optional string name = 9;
  optional bool show_label = 10;
  optional string label_type = 11;
  optional float space_from_arc_to_corner = 12;
  optional string show_angle_types = 13;
  optional float size = 14;
  optional bool enable_equal_segment_sign = 15;
  optional string equal_segment_sign = 16;
  optional bool show_arc_label = 17;
  optional string arc_label_type = 18;
  optional string arc_label_content = 19;
  optional uint32 angle_arc = 20;
  optional bool is_show_angle_size = 21;
  optional string line_color = 22;
  optional string point_color = 23;
  optional string point_label_type = 24;
  optional string point_label_free_content = 25;
  optional bool show_point_label = 26;
}

message DocDefaultElRenderPropsProto {
  optional string color = 1;
  optional string stroke_style = 2;
  optional uint32 line_weight = 3;
  optional uint32 opacity = 4;
  optional bool show_background = 5;
  optional float space_from_arc_to_corner = 6;
  optional string line_color = 7;
  optional string point_color = 8;
}

message DocRenderPropProto {
  optional int32 screen_unit = 1;
  optional double canvas_width = 2;
  optional double canvas_height = 3;
  optional double scale = 4;
  repeated double translation = 5;
  repeated double rotation = 6;
  optional bool valid = 7;
  optional bool shadow = 8;
  optional string shadow_style = 9;
  optional bool axis = 10;
  optional bool grid = 11;
  optional bool detail_grid = 12;
  optional bool border = 13;
  optional string border_style = 14;
  optional string border_color = 15;
  optional bool snap_mode = 16;
  optional bool snap_to_existing_points = 17;
  optional bool snap_to_grid = 18;
  optional bool naming_mode = 19;
}
