package viclass.editor.geo.test

import io.ktor.server.testing.*
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.doc.GeoDocCommander
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Triangle
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.point.PointFromCoordsEC
import viclass.editor.geo.impl.constructor.triangle.TriangleBaseEC
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.models.request.ElConstructionRequest


/**
 *
 * <AUTHOR>
 */
class TestTriangleConstruction {
    private lateinit var cmder: GeoDocCommander
    private lateinit var doc : GeoDoc

    @Before
    fun setup() = testApplication {
        initialize()
        cmder = testKoinApplication.koin.get<GeoDocCommander>() // create the commander
        doc = cmder.newDoc(3, 10.0, 10.0, 20)
    }

    @After
    fun tearDown() {

    }

    @Test
    fun testTriangle() = runBlocking {
        val ceA = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "A", PointFromCoordsEC.CGS.G2d.name)
        val pA = ParamStoreArray(listOf("-1.0", "-2.0", "0.0"))
        val specA = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, pA)))
        ceA.paramSpecs = listOf(specA) // set the param specs for the ce

        val ceB = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "B", PointFromCoordsEC.CGS.G2d.name)
        val pB = ParamStoreArray(listOf("-1.0", "1.0", "0.0"))
        val specB = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, pB)))
        ceB.paramSpecs = listOf(specB) // set the param specs for the ce

        val ceC = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "C", PointFromCoordsEC.CGS.G2d.name)
        val pC = ParamStoreArray(listOf("1.0", "-2.0", "0.0"))
        val specC = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, pC)))
        ceC.paramSpecs = listOf(specC) // set the param specs for the ce

        val ceD = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "D", PointFromCoordsEC.CGS.G2d.name)
        val pD = ParamStoreArray(listOf("3.0", "1.0", "0.0"))
        val specD = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, pD)))
        ceD.paramSpecs = listOf(specD) // set the param specs for the ce

        val ceTriangle1 = ElConstructionRequest("Triangle/TriangleBaseEC", "ABC", TriangleBaseEC.CGS.Points.name)
        val pTriangle1 = ParamStoreArray(listOf("B", "A", "C"))
        val specTriangle1 = ParamSpecs(ConstraintParamDefManager.aPoint, 0, false,"", mapOf(Pair(ParamKind.PK_Name, pTriangle1)))
        ceTriangle1.paramSpecs = listOf(specTriangle1)

        val ceTriangle2 = ElConstructionRequest("Triangle/TriangleBaseEC", "BCD", TriangleBaseEC.CGS.ByPointsName.name)

        val ceTriangle3 = ElConstructionRequest("Triangle/TriangleBaseEC", "ACD", TriangleBaseEC.CGS.LineSegments.name)
        val pTriangle3 = ParamStoreArray(listOf("AC", "AD"))
        val specTriangle3 = ParamSpecs(ConstraintParamDefManager.aLineSegment, 0, false,"", mapOf(Pair(ParamKind.PK_Name, pTriangle3)))
        ceTriangle3.paramSpecs = listOf(specTriangle3)

        val ceTriangle4 = ElConstructionRequest("Triangle/TriangleBaseEC", "ABD", TriangleBaseEC.CGS.LineSegmentAndPoint.name)
        val pTriangle4_1 = ParamStoreValue("AB")
        val pTriangle4_2 = ParamStoreValue("D")
        val specTriangle4_1 = ParamSpecs(ConstraintParamDefManager.aLineSegment, 0, false,"", mapOf(Pair(ParamKind.PK_Name, pTriangle4_1)))
        val specTriangle4_2 = ParamSpecs(ConstraintParamDefManager.aPoint, 1, false,"", mapOf(Pair(ParamKind.PK_Name, pTriangle4_2)))
        ceTriangle4.paramSpecs = listOf(specTriangle4_1, specTriangle4_2)

        cmder.applyConstruction(doc, ceA).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceB).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceC).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceD).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceTriangle1).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceTriangle2).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceTriangle3).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceTriangle4).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }

        val A = doc.findElementByName("A", Point::class, null)
        val B = doc.findElementByName("B", Point::class, null)
        val C = doc.findElementByName("C", Point::class, null)
        val D = doc.findElementByName("D", Point::class, null)

        val triangle1 = doc.findElementByName("BCA", Triangle::class, null)
        val triangle2 = doc.findElementByName("DBC", Triangle::class, null)
        val triangle3 = doc.findElementByName("ADC", Triangle::class, null)
        val triangle4 = doc.findElementByName("BDA", Triangle::class, null)


        checkNotNull(triangle1)
        assert(triangle1.name.equals("ABC"))
        assert(triangle1.vertices().containsAll(listOf(A, B, C)))

        checkNotNull(triangle2)
        assert(triangle2.name.equals("BCD"))
        assert(triangle2.vertices().containsAll(listOf(B, C, D)))

        checkNotNull(triangle3)
        assert(triangle3.name.equals("ACD"))
        assert(triangle3.vertices().containsAll(listOf(A, C, D)))

        checkNotNull(triangle4)
        assert(triangle4.name.equals("ABD"))
        assert(triangle4.vertices().containsAll(listOf(A, B, D)))
    }
}
