package viclass.editor.geo.test

import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import org.powermock.api.mockito.PowerMockito
import viclass.editor.geo.impl.db.GeoDocumentDBS
import viclass.editor.geo.impl.db.TextMappingDS

@Module
class TestDatabaseModule {
    @Singleton
    fun provideGeoDocDBS(): GeoDocumentDBS = PowerMockito.mock(GeoDocumentDBS::class.java)

    @Singleton
    fun provideDBS(): TextMappingDS = PowerMockito.mock(TextMappingDS::class.java)
}
