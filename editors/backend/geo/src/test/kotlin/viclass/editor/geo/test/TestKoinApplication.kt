package viclass.editor.geo.test

import org.koin.dsl.module
import org.koin.ksp.generated.module
import org.koin.logger.slf4jLogger
import viclass.editor.geo.impl.cache.MappingTextCache
import viclass.editor.geo.impl.koin.*
import org.koin.dsl.koinApplication as _koinApplication

val testKoinApplication = _koinApplication {
    slf4jLogger()
    modules(
        TestDatabaseModule().module,
        ExtractorModule().module,
        ConstructorModule().module,
        GeoDocModule().module,
        ControllerModule().module,
        module {
            single<MappingTextCache> { MappingTextCache(get()) }
        }
    )
}