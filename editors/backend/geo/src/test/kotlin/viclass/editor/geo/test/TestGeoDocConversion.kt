package viclass.editor.geo.test

import io.ktor.server.testing.*
import io.ktor.util.reflect.*
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.doc.GeoDocCommander
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.point.MiddlePointEC
import viclass.editor.geo.impl.constructor.point.PointFromCoordsEC
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.extractors.LineExtractor
import viclass.editor.geo.impl.extractors.NumberConstantExtractor
import viclass.editor.geo.models.request.ElConstructionRequest
import viclass.editor.geo.render.RenderDocState
import kotlin.test.assertNotNull

class TestGeoDocConversion {

    private lateinit var cmder: GeoDocCommander
    private lateinit var doc: GeoDoc
    private lateinit var renderProp: RenderProp
    private lateinit var renderDocState: RenderDocState

    private val red = "#F00"

    @Before
    fun setup() = testApplication {
        initialize()
        cmder = testKoinApplication.koin.get<GeoDocCommander>() // create the commander
        doc = cmder.newDoc(2, 10.0, 10.0, 20)
        renderProp = RenderProp()
        renderDocState = doc.renderDoc.state
        renderProp.color = red   // create a custom render prop
    }

    @After
    fun tearDown() {

    }

    @Test
    fun testConvertGeoDocWithPointAndLineSegmentToEntityAndBack() = runBlocking {
        val ce01 = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "B", PointFromCoordsEC.CGS.G2d.name)
        val p01 = ParamStoreArray(listOf("1.0", "1.5", "0.5"))
        val spec01 = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, p01)))
        ce01.paramSpecs = listOf(spec01) // set the param specs for the ce

        val ce02 = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "A", PointFromCoordsEC.CGS.G2d.name)
        val p02 = ParamStoreArray(listOf("0.0", "2.5", "0.5"))
        val spec02 = ParamSpecs(ConstraintParamDefManager.aValue, 0, false, "", mapOf(Pair(ParamKind.PK_Value, p02)))
        ce02.paramSpecs = listOf(spec02) // set the param specs for the ce

        cmder.applyConstruction(doc, ce01).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
            cmder.renderConstructionResult(cr, null)
        }
        cmder.applyConstruction(doc, ce02).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
            cmder.renderConstructionResult(cr, renderProp)
        }

        assertEquals(NumberConstantExtractor::class.simpleName, spec01.extractorIds[ParamKind.PK_Value])
        assertEquals(NumberConstantExtractor::class.simpleName, spec02.extractorIds[ParamKind.PK_Value])

        val ce03 = ElConstructionRequest("Point/${MiddlePointEC::class.simpleName}", "M", MiddlePointEC.CGS.LineSegment.name)
        val p03 = ParamStoreValue("AB")
        val spec03 =
            ParamSpecs(ConstraintParamDefManager.aLineSegment, 0, false, "", mapOf(Pair(ParamKind.PK_Name, p03)))
        ce03.paramSpecs = listOf(spec03)

        cmder.applyConstruction(doc, ce03).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
            cmder.renderConstructionResult(cr, null)
        }

        val document = cmder.convertDocToEntity(doc)

        assertEquals(3, document.elConstructions.size)
        assertEquals(4, document.elData.size)

        assertEquals(renderDocState.canvasWidth, document.docRenderProp.canvasWidth)
        assertEquals(renderDocState.canvasHeight, document.docRenderProp.canvasHeight)
        assertEquals(renderDocState.screenUnit, document.docRenderProp.screenUnit)
        assertEquals(renderDocState.rotation, document.docRenderProp.rotation)
        assertEquals(renderDocState.translation, document.docRenderProp.translation)

        val elc0 = document.elConstructions[0]
        val elc1 = document.elConstructions[1]
        val elc2 = document.elConstructions[2]
        assertNotNull(elc0.paramSpecs)
        assertEquals(NumberConstantExtractor::class.simpleName, elc0.paramSpecs!![0].extractorIds[ParamKind.PK_Value])
        assertEquals(NumberConstantExtractor::class.simpleName, elc1.paramSpecs!![0].extractorIds[ParamKind.PK_Value])
        assertEquals(LineExtractor::class.simpleName, elc2.paramSpecs!![0].extractorIds[ParamKind.PK_Name])

        //TODO add more verification for the entity

        val newDoc = cmder.convertEntityToDoc(document)

        assertEquals(3, newDoc.constructions.size)
        assertEquals("A", newDoc.elements[1].name)
        assertTrue(newDoc.elements[1].instanceOf(PointImpl::class))
        assertEquals("B", newDoc.elements[0].name)
        assertTrue(newDoc.elements[0].instanceOf(PointImpl::class))
        val lineAB = newDoc.findElementByName("AB", LineSegment::class, null)
        assertNotNull(lineAB)
        val deps = newDoc.dependencies(lineAB)
        assertTrue(deps.size == 2 && deps.contains(newDoc.elements[1]) && deps.contains(newDoc.elements[1]))
        val pointM = newDoc.findElementByName("M", Point::class, null)
        assertNotNull(pointM)
        val depsM = newDoc.dependencies(pointM)
        assertTrue(depsM.size == 1 && depsM.contains(lineAB))

        val renderDoc = newDoc.renderDoc

        assertEquals(renderDocState.canvasWidth, renderDoc.state.canvasWidth)
        assertEquals(renderDocState.canvasHeight, renderDoc.state.canvasHeight)
        assertEquals(renderDocState.screenUnit, renderDoc.state.screenUnit)
        assertEquals(renderDocState.rotation, renderDoc.state.rotation)
        assertEquals(renderDocState.translation, renderDoc.state.translation)
        assertEquals(4, renderDoc.elements.size)
        val pointARender = renderDoc.elements[1]
        assertEquals(red, pointARender.renderProp.color)
        assertEquals("A", pointARender.name)
        assertEquals(1, pointARender.relIndex)

    }

}
