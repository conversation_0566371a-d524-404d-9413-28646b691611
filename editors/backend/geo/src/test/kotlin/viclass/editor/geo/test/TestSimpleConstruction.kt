package viclass.editor.geo.test

import io.ktor.server.testing.*
import io.ktor.util.reflect.*
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.jupiter.api.Assertions.*
import viclass.editor.geo.NamePattern
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.doc.GeoDocCommander
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.point.MiddlePointEC
import viclass.editor.geo.impl.constructor.point.PointFromCoordsEC
import viclass.editor.geo.impl.extractors.LineExtractor
import viclass.editor.geo.impl.extractors.NumberConstantExtractor
import viclass.editor.geo.models.request.ElConstructionRequest
import kotlin.reflect.full.isSubclassOf

class TestSimpleConstruction {

    private lateinit var cmder: GeoDocCommander
    private lateinit var doc : GeoDoc

    @Before
    fun setup() = testApplication {
        initialize()
        cmder = testKoinApplication.koin.get<GeoDocCommander>() // create the commander
        doc = cmder.newDoc(2, 10.0, 10.0, 20)
    }

    @After
    fun tearDown() {

    }

    @Test
    fun testCreatingPointFromCoords() = runBlocking {
        val ce01 = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "A", PointFromCoordsEC.CGS.G2d.name)
        val p01 = ParamStoreArray(listOf("1.0", "1.5", "0.5"))
        val spec01 = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, p01)))
        ce01.paramSpecs = listOf(spec01) // set the param specs for the ce

        cmder.applyConstruction(doc, ce01).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
            assertTrue(cr.result()!!::class.isSubclassOf(Point::class))
            val point1 : Point = cr.result()!! as Point
            assertEquals(1.0, point1.x)
            assertEquals(1.5, point1.y)
            assertEquals(0.0, point1.z)  // because in 2D all z coordinate will be normalize to 0
        }

        assertEquals(NumberConstantExtractor::class.simpleName, spec01.extractorIds[ParamKind.PK_Value])
    }

    @Test
    fun testCreatingMiddlePoint() = runBlocking {
        val ce01 = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "B", PointFromCoordsEC.CGS.G2d.name)
        val p01 = ParamStoreArray(listOf("1.0", "1.5", "0.5"))
        val spec01 = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, p01)))
        ce01.paramSpecs = listOf(spec01) // set the param specs for the ce

        val ce02 = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "A", PointFromCoordsEC.CGS.G2d.name)
        val p02 = ParamStoreArray(listOf("0.0", "2.5", "0.5"))
        val spec02 = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, p02)))
        ce02.paramSpecs = listOf(spec02) // set the param specs for the ce

        val (_, cr01) = cmder.applyConstruction(doc, ce01).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        val (_, cr02) = cmder.applyConstruction(doc, ce02).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }

        assertEquals(NumberConstantExtractor::class.simpleName, spec01.extractorIds[ParamKind.PK_Value])
        assertEquals(NumberConstantExtractor::class.simpleName, spec02.extractorIds[ParamKind.PK_Value])

        val ce03 = ElConstructionRequest("Point/${MiddlePointEC::class.simpleName}", "M", MiddlePointEC.CGS.LineSegment.name)
        val p03 = ParamStoreValue("AB")
        val spec03 = ParamSpecs(ConstraintParamDefManager.aLineSegment, 0, false,"", mapOf(Pair(ParamKind.PK_Name,p03)))
        ce03.paramSpecs = listOf(spec03)

        val (_, cr03) = cmder.applyConstruction(doc, ce03).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }

        val pointM = doc.findElementByName("M", Point::class, null)

        assertEquals(LineExtractor::class.simpleName, spec03.extractorIds[ParamKind.PK_Name])

        assertNotNull(pointM)
        assertEquals(cr03.result(), pointM)
        val pointMDeps = doc.dependencies(pointM!!)
        assertEquals(1, pointMDeps.size)
        val lineAB = pointMDeps[0]
        assertEquals("AB", pointMDeps[0].name)
        assertNotNull(doc.findElementByName("AB", LineSegment::class, null))
        assertNull(doc.findElementByName("BA", LineSegment::class, null))
        assertTrue(pointMDeps[0].instanceOf(LineSegment::class))
        val lineABDeps = doc.dependencies(pointMDeps[0])
        assertEquals(2, lineABDeps.size)
        assertTrue(lineABDeps.contains(cr01.result()))
        assertTrue(lineABDeps.contains(cr02.result()))

        val constructionIndex = doc.getConstructionIndex(pointM) ?: throw ElementNotExistInDocumentException("Cannot find construction. Element doesn't exist inside the document")
        val elFromConst = doc.elementsFromConstruction(constructionIndex)
        assertEquals(2, constructionIndex)
        assertTrue(elFromConst[1] == pointM)
        assertTrue(elFromConst[0] == lineAB)
        assertEquals(2, elFromConst.size)
    }

    /**
     * Test name validation
     */
    @Test
    fun testNameChecking() = runBlocking {
        assertTrue(NamePattern.isNameValid(Point::class, "A"))
        assertTrue(NamePattern.isNameValid(Point::class, "A1"))
        assertTrue(NamePattern.isNameValid(Point::class, "Z"))
        assertTrue(NamePattern.isNameValid(Point::class, "Z'"))
        assertTrue(NamePattern.isNameValid(LineVi::class, "Ax'"))
        assertTrue(NamePattern.isNameValid(LineVi::class, "A1x'"))
        assertTrue(NamePattern.isNameValid(LineVi::class, "x"))
        assertTrue(NamePattern.isNameValid(LineVi::class, "x'"))
        assertTrue(NamePattern.isNameValid(LineVi::class, "x1"))
        assertTrue(NamePattern.isNameValid(LineVi::class, "AB"))
        assertTrue(NamePattern.isNameValid(LineVi::class, "A1B1"))
        assertTrue(NamePattern.isNameValid(LineVi::class, "A1'B1"))

        assertFalse(NamePattern.isNameValid(LineVi::class, "ABC"))
        assertFalse(NamePattern.isNameValid(LineVi::class, "xx"))
    }
}
