package viclass.editor.geo.test

import io.ktor.server.testing.*
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.doc.GeoDocCommander
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.DEFAULT_TOLERANCE
import viclass.editor.geo.impl.constructor.point.IntersectionPointEC
import viclass.editor.geo.impl.constructor.point.PointFromCoordsEC
import viclass.editor.geo.models.request.ElConstructionRequest
import kotlin.math.abs


/**
 *
 * <AUTHOR>
 */
class TestLineIntersectionPointEC {
    private lateinit var cmder: GeoDocCommander
    private lateinit var doc : GeoDoc

    @Before
    fun setup() = testApplication {
        initialize()
        cmder = testKoinApplication.koin.get<GeoDocCommander>() // create the commander
        doc = cmder.newDoc(3, 10.0, 10.0, 20)
    }

    @After
    fun tearDown() {

    }

    @Test
    fun testLineIntersection() = runBlocking {
        val ceA = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "A", PointFromCoordsEC.CGS.G2d.name)
        val pA = ParamStoreArray(listOf("-1.0", "-1.0", "-1.0"))
        val specA = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, pA)))
        ceA.paramSpecs = listOf(specA) // set the param specs for the ce

        val ceB = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "B", PointFromCoordsEC.CGS.G2d.name)
        val pB = ParamStoreArray(listOf("1.0", "1.0", "1.0"))
        val specB = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, pB)))
        ceB.paramSpecs = listOf(specB) // set the param specs for the ce

        val ceC = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "C", PointFromCoordsEC.CGS.G2d.name)
        val pC = ParamStoreArray(listOf("-1.0", "1.0", "-1.0"))
        val specC = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, pC)))
        ceC.paramSpecs = listOf(specC) // set the param specs for the ce

        val ceD = ElConstructionRequest("Point/${PointFromCoordsEC::class.simpleName}", "D", PointFromCoordsEC.CGS.G2d.name)
        val pD = ParamStoreArray(listOf("1.0", "-1.0", "1.0"))
        val specD = ParamSpecs(ConstraintParamDefManager.aValue, 0, false,"",mapOf(Pair(ParamKind.PK_Value, pD)))
        ceD.paramSpecs = listOf(specD) // set the param specs for the ce

        val ceK = ElConstructionRequest("Point/${IntersectionPointEC::class.simpleName}", "K", IntersectionPointEC.CGS.LineLine.name)
        val pK1 = ParamStoreValue("AB")
        val pK2 = ParamStoreValue("CD")
        val specK1 = ParamSpecs(ConstraintParamDefManager.aLine, 0, false,"", mapOf(Pair(ParamKind.PK_Name, pK1)))
        val specK2 = ParamSpecs(ConstraintParamDefManager.aLine, 1, false,"", mapOf(Pair(ParamKind.PK_Name, pK2)))
        ceK.paramSpecs = listOf(specK1, specK2)

        cmder.applyConstruction(doc, ceA).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceB).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceC).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceD).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        cmder.applyConstruction(doc, ceK).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }

        val pointK = doc.findElementByName("K", Point::class, null)

        checkNotNull(pointK)

        assert(
            abs(pointK.x-.0) < DEFAULT_TOLERANCE && pointK.x == pointK.y && pointK.x == pointK.z)
    }
}
