package viclass.editor.geo.test

import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.server.testing.*
import io.ktor.util.reflect.*
import org.junit.Test
import org.junit.jupiter.api.Assertions.assertTrue
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.renderdata.RenderVertex
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.point.PointFromCoordsEC
import viclass.editor.geo.impl.controller.ConstructionController
import viclass.editor.geo.impl.controller.DocumentController
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.models.request.ApplyConstructionRequest
import viclass.editor.geo.models.request.ConstructionRequest
import viclass.editor.geo.models.request.CreateGeoDocRequest
import viclass.editor.geo.models.request.ElConstructionRequest
import viclass.editor.geo.models.response.ApplyConstructionResponse
import viclass.editor.geo.models.response.FetchDocResponse
import viclass.editor.geo.server.constructionRouting
import viclass.editor.geo.server.documentRouting
import kotlin.test.assertEquals

class TestConstructionController constructor() {

    @Test
    fun testApplyConstruction() = testApplication {

        initialize()

        routing {
            constructionRouting(application.testComp.koin.get<ConstructionController>())
            documentRouting(application.testComp.koin.get<DocumentController>())
        }

        val client = testClient()

        val response = client.post("document/create") {
            contentType(ContentType.Application.Json)
            setBody(CreateGeoDocRequest(2, 10.0, 10.0, 30))
        }

        val docResponse = response.body<FetchDocResponse>()

        val ce01 = ElConstructionRequest(
            "Point/${PointFromCoordsEC::class.simpleName}",
            "A",
            PointFromCoordsEC.CGS.G2d.name
        )
        val p01 = ParamStoreArray(listOf("1.0", "1.5", "0.5"))
        val spec01 = ParamSpecs(ConstraintParamDefManager.aValue, 0, false, "", mapOf(Pair(ParamKind.PK_Value, p01)))
        ce01.paramSpecs = listOf(spec01) // set the param specs for the ce

        val applyResponse = client.post("document/${docResponse.docId}/construct") {
            contentType(ContentType.Application.Json)
            setBody(ApplyConstructionRequest(listOf(ConstructionRequest(ce01, RenderProp()))))
        }

        val applyResponseBody = applyResponse.body<ApplyConstructionResponse>()

        assertEquals(1, applyResponseBody.render.size)
        assertTrue(applyResponseBody.render.first().instanceOf(RenderVertex::class))
        val v = applyResponseBody.render.first() as RenderVertex
        assertTrue(v.coords[0] == 1.0 && v.coords[1] == 1.5 && v.coords[2] == 0.0)
    }

}
