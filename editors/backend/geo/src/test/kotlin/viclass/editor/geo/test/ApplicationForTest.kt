package viclass.editor.geo.test

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.util.DefaultIndenter
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import io.ktor.client.*
import io.ktor.serialization.jackson.*
import io.ktor.server.application.*
import io.ktor.server.config.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.testing.*
import io.ktor.util.*
import org.koin.core.KoinApplication
import org.koin.core.context.startKoin
import kotlin.reflect.KClass

var s:KoinApplication? = null

fun ApplicationTestBuilder.testClient() =
    createClient {
        install(io.ktor.client.plugins.contentnegotiation.ContentNegotiation) {
            jackson {
                setDefaultPrettyPrinter(DefaultPrettyPrinter().apply {
                    indentArraysWith(DefaultPrettyPrinter.FixedSpaceIndenter.instance)
                    indentObjectsWith(DefaultIndenter("  ", "\n"))
                })
                registerModule(JavaTimeModule())  // support java.time.* types
                registerModule(KotlinModule.Builder().build().registerKClass()) // register KClassSerializer, KClassDeserializer
            }
        }
    }

fun ApplicationTestBuilder.initialize() {
    this.testClient()
    environment {
        config = ApplicationConfig("application.test.conf")
        developmentMode = false
    }

    if (s == null) s = startKoin(testKoinApplication)

    application {
        attributes.put(
            AttributeKey("testComp"),
            s!!
        )
        configureSerialization()
    }


}

val Application.testComp : KoinApplication
    get() = this.attributes[AttributeKey("testComp")]

val Application.client : HttpClient
    get() = this.attributes[AttributeKey("client")]

private fun Application.configureSerialization() {
    install(ContentNegotiation) {
        jackson {
            setDefaultPrettyPrinter(DefaultPrettyPrinter().apply {
                indentArraysWith(DefaultPrettyPrinter.FixedSpaceIndenter.instance)
                indentObjectsWith(DefaultIndenter("  ", "\n"))
            })
            registerModule(JavaTimeModule())  // support java.time.* types
            registerModule(KotlinModule.Builder().build().registerKClass()) // register KClassSerializer, KClassDeserializer
        }
    }
}

/**
 * Define KClass kotlin module for jackson
 */
fun KotlinModule.registerKClass(): SimpleModule {
    class KClassSerializer : StdSerializer<KClass<*>>(KClass::class.java) {
        override fun serialize(value: KClass<*>, gen: JsonGenerator, provider: SerializerProvider) {
            gen.writeString(value.qualifiedName)
        }
    }

    class KClassDeserializer : StdDeserializer<KClass<*>>(KClass::class.java) {
        override fun deserialize(p: JsonParser, ctxt: DeserializationContext?): KClass<*> {
            val qualifiedName = p.text
            return Class.forName(qualifiedName).kotlin
        }
    }

    return this
            .addSerializer(KClass::class.java, KClassSerializer())
            .addDeserializer(KClass::class.java, KClassDeserializer())
}