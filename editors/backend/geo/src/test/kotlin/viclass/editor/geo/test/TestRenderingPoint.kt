package viclass.editor.geo.test

import io.ktor.server.testing.*
import io.ktor.util.reflect.*
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.jupiter.api.Assertions
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.paramstore.ParamSpecs
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.renderdata.RenderVertex
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.doc.GeoDocCommander
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.point.MiddlePointEC
import viclass.editor.geo.impl.constructor.point.PointFromCoordsEC
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.models.request.ElConstructionRequest
import kotlin.reflect.full.isSubclassOf

class TestRenderingPoint {

    private lateinit var cmder: GeoDocCommander
    private lateinit var doc: GeoDoc
    private lateinit var renderProp: RenderProp

    private val red = "#F00"

    @Before
    fun setup() = testApplication {
        initialize()
        cmder = testKoinApplication.koin.get<GeoDocCommander>() // create the commander
        doc = cmder.newDoc(2, 10.0, 10.0, 20)
        renderProp = RenderProp()
        renderProp.color = red   // create a custom render prop
    }

    @After
    fun tearDown() {

    }

    @Test
    fun testBasicRenderingWithOneElementInConstructionResult() = runBlocking {
        val ce01 = ElConstructionRequest(
            "Point/${PointFromCoordsEC::class.simpleName}",
            "A",
            PointFromCoordsEC.CGS.G2d.name
        )
        val p01 = ParamStoreArray(listOf("1.0", "1.5", "0.5"))
        val spec01 = ParamSpecs(ConstraintParamDefManager.aValue, 0, false, "", mapOf(Pair(ParamKind.PK_Value, p01)))
        ce01.paramSpecs = listOf(spec01) // set the param specs for the ce

        val (cst01, cr01) = cmder.applyConstruction(doc, ce01).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
        }
        Assertions.assertTrue(cr01.result()!!::class.isSubclassOf(Point::class))

        val point1: Point = cr01.result()!! as Point

        val rendered = cmder.renderConstructionResult(cr01, renderProp) as List<RenderVertex>
        Assertions.assertTrue(doc.renderDoc.elements.size == 1)
        Assertions.assertTrue(doc.renderDoc.elements[0].instanceOf(RenderVertex::class))
        Assertions.assertEquals(doc.getIndex(point1), doc.renderDoc.elements[0].relIndex)
        Assertions.assertEquals(rendered[0], doc.renderDoc.elements[0])
        Assertions.assertTrue(rendered[0].coords[0] == 1.0 && rendered[0].coords[1] == 1.5)
        Assertions.assertEquals("A", rendered[0].name)


    }

    @Test
    fun testCreatingMiddlePoint() = runBlocking {
        val ce01 = ElConstructionRequest(
            "Point/${PointFromCoordsEC::class.simpleName}",
            "B",
            PointFromCoordsEC.CGS.G2d.name
        )
        val p01 = ParamStoreArray(listOf("1.0", "1.5", "0.5"))
        val spec01 = ParamSpecs(ConstraintParamDefManager.aValue, 0, false, "", mapOf(Pair(ParamKind.PK_Value, p01)))
        ce01.paramSpecs = listOf(spec01) // set the param specs for the ce

        val ce02 = ElConstructionRequest(
            "Point/${PointFromCoordsEC::class.simpleName}",
            "A",
            PointFromCoordsEC.CGS.G2d.name
        )
        val p02 = ParamStoreArray(listOf("0.0", "2.5", "0.5"))
        val spec02 = ParamSpecs(ConstraintParamDefManager.aValue, 0, false, "", mapOf(Pair(ParamKind.PK_Value, p02)))
        ce02.paramSpecs = listOf(spec02) // set the param specs for the ce

        cmder.applyConstruction(doc, ce01).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
            cmder.renderConstructionResult(cr, null)
        }
        cmder.applyConstruction(doc, ce02).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
            cmder.renderConstructionResult(cr, null)
        }

        val ce03 = ElConstructionRequest(
            "Point/${MiddlePointEC::class.simpleName}",
            "M",
            MiddlePointEC.CGS.LineSegment.name
        )
        val p03 = ParamStoreValue("AB")
        val spec03 =
            ParamSpecs(ConstraintParamDefManager.aLineSegment, 0, false, "", mapOf(Pair(ParamKind.PK_Name, p03)))
        ce03.paramSpecs = listOf(spec03)

        cmder.applyConstruction(doc, ce03).also { (cst, cr) ->
            doc.mergeConstructionResult(cr, cst)
            cmder.renderConstructionResult(cr, renderProp)
        }

        val pointM = doc.findElementByName("M", Point::class, null)!!
        val lineAB = doc.findElementByName("AB", LineSegment::class, null)!!

        val renderEls = doc.renderDoc.elements

        Assertions.assertEquals(4, renderEls.size)
        renderEls[3].renderProp.hidden?.let { Assertions.assertFalse(it) }
        renderEls[2].renderProp.hidden?.let { Assertions.assertFalse(it) }
        renderEls[1].renderProp.hidden?.let { Assertions.assertFalse(it) }
        renderEls[0].renderProp.hidden?.let { Assertions.assertFalse(it) }
        Assertions.assertEquals(3, renderEls[3].relIndex)
        Assertions.assertEquals(doc.getIndex(lineAB), renderEls[2].relIndex)
        Assertions.assertEquals(doc.getIndex(pointM), renderEls[3].relIndex)
        Assertions.assertEquals(red, renderEls[3].renderProp.color)

    }

}
