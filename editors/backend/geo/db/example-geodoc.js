/*
Initialize an example Geo Document
*/

var col = db.getCollection("GeoDocument");

col.insertMany([
    {
        numDims: 2,
        elConstructions: [
            {
                ctId: "PointFromCoords",
                name: "A",
                elType: "Point",
                constraints: [
                    {
                        ctId: "coords2d",
                        params: {
                            "X": 1.0,
                            "Y": 1.0
                        }
                    }
                ]
            },

            {
                ctId: "PointFromCoords",
                name: "B",
                elType: "Point",
                constraints: [
                    {
                        ctId: "coords2d",
                        params: {
                            "X": 3.0,
                            "Y": 5.0
                        }
                    }
                ]
            },

            {
                ctId: "PointFromCoords",
                name: "C",
                elType: "Point",
                constraints: [
                    {
                        ctId: "coords2d",
                        params: {
                            "X": 5.0,
                            "Y": 1.0
                        }
                    }
                ]
            },

            {
                ctId: "LineFromTwoPoints",
                name: "AB",
                elType: "LineSegment"
            },

            {
                ctId: "PerpendicularLine",
                name: "CH",
                elType: "LineSegment",
                constraints: [
                    {
                        ctId: "fromPoint",
                        params: {
                            "fromPoint": "C"
                        }
                    },
                    {
                        ctId: "perpendicular",
                        params: {
                            "line": "AB"
                        }
                    }
                ]
            }
        ],

        cached: [
        ]
    }
]);