/*
Initialize the ConstraintTemplate collection
*/

var col = db.getCollection("ParamTemplate");


col.insertMany([

    {
        _id: "aValue",
        defaultTpl: "Value {value}",
        pTypes: {
            "value": "Double"
        }
    },

    {
        _id: "aPoint",
        description: "Specify a point as a constraint",
        defaultTpl: "Point {name}",
        pTypes: {
            "name": "Point"
        }
    },

    {
        _id: "aLine",
        description: "Specify a line as a constraint",
        defaultTpl: "Line {name}",
        pTypes: {
            "name": "Line"
        }
    },

    {
        _id: "aLineSegment",
        description: "Specify a line segment as a constraint",
        defaultTpl: "Line segment {name}",
        pTypes: {
            "name": "LineSegment"
        }
    },

    {
        _id: "anElement",
        description: "Specify an element as constraint",
        defaultTpl: "Element {name}",
        pTypes: {
            "name": "Element"
        }
    },

    {
        _id: "aPolygon",
        description: "Specify a polygon as constraint",
        defaultTpl: "Polygon {name}",
        pTypes: {
            "name": "Polygon"
        }
    },

    {
        _id: "lengthAssignment",
        description: "Specify an expression for length calculation",
        defaultTemplate: "Length of {name} = {expression}",
        pTypes: {
            "name": "NameForLength",
            "expression": "LengthExpression"
        }
    }

]);