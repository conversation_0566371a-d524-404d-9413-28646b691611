/*
Initialize the ConstraintTemplate collection
*/

var col = db.getCollection("ConstructorTemplate");


// points
col.insertMany([

    {
        _id: "PointFromCoords",
        cgs: [
            {
                name: "_3d",
                numDim: 3,
                params: [{
                    id: "aValue",
                    tpl: ["3D Coordinates X = {value}, Y = {value}, Z = {value}"]
                }]
            },
            {
                name: "_2d",
                numDim: 2,
                params : [{
                    id: "aValue",
                    tpl: ["2D Coordinates X = {value}, Y = {value}"]
                }]
            }
        ],
        elTypes: ["Point"],
        constructorClass: "point.PointFromCoords"
    },

    {
        _id: "MiddlePoint",
        constructorClass: "point.MiddlePoint",
        hints: ["middle point of"],
        cgs: [
            {
                name: "UseTwoPoints",
                params: [{
                    id:"aPoint",
                    tpl: ["Points [name] and [name]"]
                }]
            },
            {
                name: "LineSegment",
                params : [{
                    id: "aLineSegment",
                    tpl: ["Line segment {name}"]
                }]
            }
        ],
        elTypes: ["Point"]

    },

    {
        _id: "CenterPoint",
        constructorClass: "point.CenterPoint",
        hints: ["center point of a shape", "center point of a list of other points"],
        cgs: [
            {
                name: "useAnElement",
                params: [{
                    id:"anElement",
                    tpl: ["Shape {name}"]
                }]
            },
            {
                name: "usePointList",
                params: [{
                    id:"aPoint",
                    tpl: ["Points [name]"]
                }]
            }
        ],
        elTypes: ["Point"]
    },

    {
        _id: "OnLineWithLength",
        constructorClass: "point.OnLineWithLength",
        hints: ["point on a line with distance to other point"],
        cgs: [
            {
                name: "onLineSegment",
                params: [{
                    id: "aLine",
                    tpl: ["On line {name}"]
                },
                {
                    id: "lengthAssignment",
                    tpl: ["Length {name} = {expression}", "{name} = {expression}"]
                }]
            }
        ],
        elTypes: ["Point"]
    }

]);

// line
col.insertMany([

    {
        _id: "LineFromTwoPoints",
        constructorClass: "line.LineFromTwoPoints",
        hints: ["Line between two points"],
        cgs: [
            {
                name: "useTwoPoints",
                params : [{
                    id: "aPoint",
                    tpl: ["From point {name} to point {name}"]
                }]
            },
            {   // create a line without constraint (using known point set extracted from name)
                name: "usePointSet"
            }
        ],
        elTypes: ["Line"]
    },

    {
        _id: "TriangleAltitude",
        constructorClass: "line.TriangleAltitude",
        hints: ["Altitude"],
        cgs: [
            {
                name: "fromPoint",
                params: [{
                    id: "aPoint",
                    tpl: ["From vertex {name}", "From {name}"]
                }, {
                    id: "aTriangle",
                    tpl: ["In triangle {name}"]
                }]
            },
            {
                name: "toEdge",
                params: [{
                    id: "aTriangle",
                    tpl: ["In triangle {name}"]
                }, {
                    id: "aLineSegment",
                    tpl: ["To edge {name}"]
                }]
            },
            {
                name: "usePointSet",
                params: [{
                    id: "aTriangle",
                    tpl: ["In triangle {name}"]
                }]
            }
        ],
        elTypes: ["LineSegment"],

    },

    {
        _id: "PerpendicularLine",
        constructorClass: "line.Perpendicular",
        cgs: [
            {
                name: "fromPointAndLine",
                params: [{
                    id: "aPoint",
                    tpl: ["From point {name}"]
                }, {
                    id: "aLine",
                    tpl: ["Perpendicular with {name}"]
                }]
            }
        ],
        elTypes: ["Line"]

    },

    {
        _id: "ParallelLine",
        constructorClass: "line.Parallel",
        cgs: [
            {
                name: "fromPointAndLine",
                params: [{
                    id: "aPoint",
                    tpl: ["From point {name}", "Through point {name}"]
                }, {
                    id: "aLine",
                    tpl: ["Parallel with {name}"]
                }]
            },
        ],
        elTypes: ["Line"]
    },

    {
        _id: "RandomLine",
        constructorClass: "line.RandomLine",
        cgs: [],
        elTypes: ["Line"]
    }

]);
