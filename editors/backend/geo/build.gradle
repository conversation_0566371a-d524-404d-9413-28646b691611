plugins {
    id "hnct.build"
    id "kotlin"
    id "application"
    id "kotlinx-serialization"
    id "com.google.protobuf"
    id "com.google.devtools.ksp"
}

version = "1.0.1"

application {
    mainClass = 'viclass.editor.geo.Launcher'
}

run {
    classpath += files("conf")
}

tasks.withType(AbstractCopyTask) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

compileApiKotlin.destinationDirectory = compileApiJava.destinationDirectory
compileKotlin.destinationDirectory = compileJava.destinationDirectory
compileTestKotlin.destinationDirectory = compileTestJava.destinationDirectory

dependencies {

    sourceSets {
        implementation api
        testImplementation main, api
    }

    internal {
        apiImplementation([id: ':common.libs', src: ['logger']], "viclass:common.libs-logger:1.0.0", true)
        apiImplementation([id: ':common.libs', src: ['lang']], "viclass:common.libs-lang:1.0.0", true)
        implementation([id: ':common.libs', src: ['codec']], "viclass:common.libs-codec:1.0.0", true)
        implementation([id: ':common.libs', src: ['evaluator']], "viclass:common.libs-evaluator:1.0.0", true)

        implementation([id: ':feature.common', src: ['main']], "viclass:feature.common:1.0.0", true)
    }

    apiImplementation "org.reflections:reflections:0.10.2"

    // Koin
    ksp "io.insert-koin:koin-ksp-compiler:$koinKspVs"
    implementation "io.insert-koin:koin-annotations:$koinKspVs"
    implementation "io.insert-koin:koin-core:$koinVs"
    implementation "io.insert-koin:koin-ktor:$koinVs"
    implementation "io.insert-koin:koin-logger-slf4j:$koinVs"
    implementation "io.ktor:ktor-server-core-jvm"
    // implementation "org.koin:koin-java:2.0.1"

    apiImplementation("org.apache.commons:commons-math3:3.6.1")

    implementation("org.mongodb:bson:$mongoRxDriverVs")
    apiImplementation "org.apache.commons:commons-geometry-euclidean:1.0"

    apiImplementation "org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm:0.3.5"
    // https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-annotations
    apiImplementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.3'

    implementation "com.fasterxml.jackson.core:jackson-databind:$jacksonVs"

    implementation 'net.objecthunter:exp4j:0.4.8'

    implementation group: 'org.jblas', name: 'jblas', version: '1.2.4'

    implementation "org.mongodb:mongodb-driver-reactivestreams:$mongoRxDriverVs"

    implementation("org.jetbrains.kotlinx:kotlinx-serialization-core:1.5.1")

    implementation "com.google.guava:guava:$guavaVs"

    implementation 'com.typesafe:config:1.4.2'
    implementation "io.reactivex.rxjava3:rxkotlin:$rxKotlinVs"

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-rx3:$coroutineVs"
//	implementation "org.jetbrains.kotlinx:kotlinx-coroutines-jdk15:$coroutineVs"

    // -------- KTOR -------------
    implementation "io.ktor:ktor-server-core:$ktorVs"
    implementation "io.ktor:ktor-server-content-negotiation:$ktorVs"
    implementation "io.ktor:ktor-serialization-jackson:$ktorVs"
    implementation "io.ktor:ktor-server-netty:$ktorVs"
    implementation "io.ktor:ktor-server-cors:$ktorVs"
    implementation "io.ktor:ktor-server-status-pages:$ktorVs"
    implementation "io.ktor:ktor-server-jvm:$ktorVs"
    // -----------------------------

    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.3'
    implementation "com.google.protobuf:protobuf-java:$protobufVs"

    implementation "org.jetbrains.kotlinx:kotlinx-collections-immutable-jvm:0.3.5"


    testImplementation "org.junit.jupiter:junit-jupiter-api:${junitVS}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${junitVS}"
    testImplementation "org.powermock:powermock-api-mockito2:${powerMockVS}"
    testImplementation "org.powermock:powermock-module-junit4:${powerMockVS}"
    testImplementation "org.jetbrains.kotlin:kotlin-test:$kotlinVs"

    testImplementation "io.ktor:ktor-client-content-negotiation:$ktorVs"
    testImplementation "io.ktor:ktor-server-test-host-jvm:$ktorVs"

    // https://mvnrepository.com/artifact/org.locationtech.jts/jts-core
    implementation 'org.locationtech.jts:jts-core:1.19.0'
}

tasks.withType(JavaCompile) {
    doFirst {
        options.compilerArgs = [
                '--module-path', classpath.asPath,
        ]
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVs}"
    }

    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:1.65.1${platform}"
        }
    }
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                java {
//                    option '--experimental_allow_proto3_optional'
                }
            }
            task.plugins {
                grpc {}
            }
        }
    }
}
