II/ Algebra : Đ<PERSON><PERSON> số

Function = Hàm Số
Range: khoảng giá trị
Domain: khoảng giá trị của x
Coordinates: toạ độ
x-intercept: giao điểm của đồ thị và trục x
y-intercept: giao điểm của đồ thị và trục y
Plot: vẽ
Sketch: vẽ phác
Simultaneous: đồng thời
exponent : số mũ
fraction: phân số
numberator: tử số
Denominator: mẫu số
Surd: số vô tỉ của căn
Gradient: hệ số a trong y=ax+b
Coeficient: hệ số
Asymptote : đường tiểm cận.
Tangent: Tiếp tuyến
Quadratic function: Hàm bậc 2
Discriminant: là denta = b^2 - 4ac
Cubic function: Hàm bậc 3
Hyperbola:Hình Hi pe bol( em không biết Tiếng Việt viết thế nào chỉ biết vẽ tn thôi)
Ellipse: Hình e-líp
Factorise the equation: nôm na là đưa về dạng thừa số.
Stretch: Kéo dãn hay co dãn vào( hữu ích khi nói về transformation of graph)
Sequence: dãy số
Serie: tổng của dãy số
Arithmetic sequence: Cấp số cộng
Differentiation: Vi phân
Derivative: Đạo hàm
Integration: Tích phân
Gradient: hệ số a trong y=ax+b <-- tiếng Việt dùng từ "hệ số góc"
Arithmetic sequence: Cấp số cộng , còn cấp số Nhân là Geometric sequence
4 basic calculations: plus (+) minus (-) duplicate (x) divide (:)
