syntax = "proto3";

package viclass.proto.freedrawing.cmd;

import "freedrawing_msg.proto";

enum CmdTypeProto {
    DUMMY = 0;
    INSERT_SHAPE_OBJ = 2;
    INSERT_TEXT_OBJ = 5;

    START_PREVIEW = 6;
    UPDATE_BOUNDARY_PREVIEW = 7;
    UPDATE_POSITION_PREVIEW = 8;
    UPDATE_TOOL_STATE_PREVIEW = 9;
    UPDATE_CONTENT_PREVIEW = 15;
    END_PREVIEW = 10;

    INSERT_PARTIAL_PENCIL_OBJ = 11;
    INSERT_PARTIAL_ERASER_OBJ = 12;

    REMOVE_OBJ = 13;
    REMOVE_LAYER = 14;
}

message InsertDocCmdProto {
    optional string global_id = 1;
}

message InsertLayerCmdProto {
    int32 zIndex = 1;
    freedrawing.msg.PositionProto position = 2;
}

message InsertShapeObjCmdProto {
    int32 layer_id = 1;
    freedrawing.msg.ShapeObjectProto shape_obj = 2;
}

message InsertTextObjCmdProto {
    int32 layer_id = 1;
    freedrawing.msg.TextObjectProto text_obj = 2;
}

message InsertPartialPencilObjCmdProto {
    int32 layer_id = 1;
    freedrawing.msg.PencilObjectProto pencil_obj = 2;
}

message InsertPartialEraserObjCmdProto {
    int32 layer_id = 1;
    freedrawing.msg.EraserObjectProto eraser_obj = 2;
}

message StartPreviewCmdProto {
    string tool_type = 1;
    int32 layer_id = 2;
    oneof object {
        freedrawing.msg.ShapeObjectProto shape = 3;
        freedrawing.msg.PencilObjectProto pencil = 4;
        freedrawing.msg.EraserObjectProto eraser = 5;
        freedrawing.msg.TextObjectProto text = 6;
    }
}

message UpdatePreviewCmdProto {
    string tool_type = 1;
    bool end_point = 2;
    int32 layer_id = 3;
    bool typing = 10;
    oneof update {
        freedrawing.msg.BoundaryProto boundary = 4;
        freedrawing.msg.PositionProto point = 5;
        freedrawing.msg.CommonToolStateProto common_tool_state = 6;
        freedrawing.msg.EraserToolStateProto eraser_tool_state = 7;
        freedrawing.msg.TextToolStateProto text_tool_state = 8;
        string content = 9;
    }
}

message EndPreviewCmdProto {}

message RemoveFreedrawingObjCmdProto {
    int32 layer_id = 1;
    int32 obj_id = 2;
}

message RemoveLayerCmdProto {}
