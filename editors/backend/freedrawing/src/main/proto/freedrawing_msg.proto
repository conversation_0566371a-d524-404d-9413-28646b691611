syntax = "proto3";

package viclass.proto.freedrawing.msg;

message CommonToolStateProto {
  optional string stroke_color = 1;
  optional string fill_color = 2;
  int32 line_width = 3;
}

message EraserToolStateProto {
  int32 size = 1;
}

message TextToolStateProto {
  optional string stroke_color = 1;
  optional string fill_color = 2;
  string font_color = 3;
  int32 font_size = 4;
  string font_style = 5;
  string font_weight = 6;
  string font = 7;
  string text_decoration = 8;
}

message BoundaryProto {
  PositionProto start = 1;
  PositionProto end = 2;
}

message PositionProto {
  double x = 1;
  double y = 2;
}

message ShapeObjectProto {
  int32 local_id = 1;
  string tool_type = 2;
  BoundaryProto boundary = 3;
  CommonToolStateProto tool_state = 4;
}

message TextObjectProto {
  int32 local_id = 1;
  BoundaryProto boundary = 2;
  TextToolStateProto tool_state = 3;
  string content = 4;
}

message PencilObjectProto {
  int32 local_id = 1;
  repeated PositionProto points = 2;
  CommonToolStateProto tool_state = 3;
}

message EraserObjectProto {
  int32 local_id = 1;
  repeated PositionProto points = 2;
  EraserToolStateProto tool_state = 3;
}

message ObjectProto {
  oneof object {
    ShapeObjectProto shape = 1;
    PencilObjectProto pencil = 2;
    EraserObjectProto eraser = 3;
    TextObjectProto text = 4;
  }
}

message LayerProto {
  int32 local_id = 1;
  repeated ObjectProto objects = 2;
}

message DocumentProto {
  string global_id = 1;
  repeated LayerProto layers = 2;
  uint32 version = 3;
}
