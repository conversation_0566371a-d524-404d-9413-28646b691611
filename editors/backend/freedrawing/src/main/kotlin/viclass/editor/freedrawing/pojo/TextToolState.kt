package viclass.editor.freedrawing.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonProperty

data class TextToolState @BsonCreator constructor(
    @BsonProperty("font")
    val font: String,

    @BsonProperty("fontSize")
    val fontSize: Int,

    @BsonProperty("fontStyle")
    val fontStyle: String,

    @BsonProperty("fontWeight")
    val fontWeight: String,

    @BsonProperty("fontColor")
    val fontColor: String,

    @BsonProperty("textDecoration")
    val textDecoration: String,

    @BsonProperty("stroke")
    val stroke: String? = null,

    @BsonProperty("fill")
    val fill: String? = null,
)
