package viclass.editor.freedrawing.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "TextObject", key = "objectType")
data class TextObject @BsonCreator constructor(
    @BsonProperty("localId")
    override val localId: Int,

    @BsonProperty("content")
    val content: String,

    @BsonProperty("state")
    val state: TextToolState,

    @BsonProperty("boundary")
    val boundary: List<Double>
): FreedrawingObject
