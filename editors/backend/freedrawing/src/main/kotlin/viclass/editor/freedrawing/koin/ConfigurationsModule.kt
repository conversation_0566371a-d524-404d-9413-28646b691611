package viclass.editor.freedrawing.koin

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.koin.dsl.module
import viclass.editor.freedrawing.config.ConfigBean
import java.io.File

val configurationsModule = module {
    single<ConfigBean> {
        val loadConfigFailedMessage = "Failed to load configurations"
        val configPath = "conf/config.json"

        jacksonObjectMapper().readValue(File(configPath), ConfigBean::class.java)
            ?: throw RuntimeException(loadConfigFailedMessage)
    }
}
