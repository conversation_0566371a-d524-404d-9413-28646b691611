package viclass.editor.freedrawing.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "PencilObject", key = "objectType")
data class PencilObject @BsonCreator constructor(
    @BsonProperty("localId")
    override val localId: Int,

    @BsonProperty("points")
    val points: List<Double> = emptyList(),

    @BsonProperty("state")
    val state: CommonToolState = CommonToolState(1),
): FreedrawingObject
