package viclass.editor.freedrawing.controllers

import CmdProto.CmdMeta.CmdMetaProto
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import kotlinx.coroutines.rx3.await
import org.bson.types.ObjectId
import viclass.editor.freedrawing.db.FreedrawingDBS
import viclass.editor.freedrawing.pojo.FreedrawingDocument
import viclass.editor.freedrawing.pojo.FreedrawingLayer
import viclass.editor.freedrawing.utils.*
import viclass.proto.feature_common.FeatureCommonCmd
import viclass.proto.freedrawing.cmd.FreedrawingCmd

class FreedrawingController constructor(
    private val dbS: FreedrawingDBS,
    private val jsonFactory: JsonNodeFactory
) : Logging {

    suspend fun deleteDocuments(call: ApplicationCall) {
        val ids: List<String> = call.receive()
        logger.info("deleting documents {}", ids)
        try {
            dbS.deleteDocuments(ids).await()
            call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            logger.error("Failed delete documents {} ... ", ids, t)
            return call.respond(HttpStatusCode.InternalServerError)
        }
    }

    suspend fun getDocument(call: ApplicationCall) {

        val id = call.parameters["globalId"] ?: return call.respond(
            HttpStatusCode.BadRequest,
            jsonFactory.objectNode().put("message", "Missing or malformed globalId"),
        )

        logger.info("Getting document {}", id)

        try {
            val document = dbS.loadDocument(id).await()
            val proto = convertDocumentToProto(document)
            val arr = proto.toByteArray()
            call.respondBytes(arr)
        } catch (t: Throwable) {
            logger.error("failed to get document {}: ", id, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to get document $id: ${t.message}"),
            )
        }
    }

    suspend fun processCmd(call: ApplicationCall) {
        val data = call.receive<ByteArray>()

        val metaLength = data[0].toInt()
        val cmdMetaArrBuf = data.copyOfRange(1, metaLength + 1)
        val cmdDataArrBuf = data.copyOfRange(metaLength + 1, data.size)

        val cmdMeta = try {
            CmdMetaProto.parseFrom(cmdMetaArrBuf)
        } catch (t: Throwable) {
            logger.error("failed to parse CmdMeta: ", t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to parse CmdMeta: ${t.message}")
            )
        }

        if (cmdMeta.channelCode != 1) {
            return call.respond(
                HttpStatusCode.BadRequest,
                jsonFactory.objectNode().put("message", "invalid channel code")
            )
        }

        val globalId = call.parameters["globalId"] ?: return call.respond(
            HttpStatusCode.BadRequest,
            jsonFactory.objectNode().put("message", "missing global id"),
        )

        logger.info("Command execution: {}", cmdMeta.cmdType)

        when (cmdMeta.cmdType) {
            FeatureCommonCmd.FCCmdTypeProto.INSERT_DOC_VALUE -> {
                // do nothing
                call.respond(HttpStatusCode.OK)
            }

            FeatureCommonCmd.FCCmdTypeProto.INSERT_LAYER_VALUE -> {
                processInsertLayerCmd(call, cmdMeta, cmdDataArrBuf, globalId)
            }

            FeatureCommonCmd.FCCmdTypeProto.REMOVE_DOC_VALUE -> {
                processRemoveDocCmd(call, cmdMeta, cmdDataArrBuf, globalId)
            }

            FreedrawingCmd.CmdTypeProto.INSERT_SHAPE_OBJ_VALUE -> {
                processInsertShapeObjCmd(call, cmdMeta, cmdDataArrBuf, globalId)
            }

            FreedrawingCmd.CmdTypeProto.INSERT_TEXT_OBJ_VALUE -> {
                processInsertTextObjCmd(call, cmdMeta, cmdDataArrBuf, globalId)
            }

            FreedrawingCmd.CmdTypeProto.INSERT_PARTIAL_PENCIL_OBJ_VALUE -> {
                processInsertPartialPencilObjCmd(call, cmdMeta, cmdDataArrBuf, globalId)
            }

            FreedrawingCmd.CmdTypeProto.INSERT_PARTIAL_ERASER_OBJ_VALUE -> {
                processInsertPartialEraserObjCmd(call, cmdMeta, cmdDataArrBuf, globalId)
            }

            FreedrawingCmd.CmdTypeProto.REMOVE_OBJ_VALUE -> {
                processRemoveObjectCmd(call, cmdMeta, cmdDataArrBuf, globalId)
            }

            FreedrawingCmd.CmdTypeProto.REMOVE_LAYER_VALUE -> {
                processRemoveLayerCmd(call, cmdMeta, cmdDataArrBuf, globalId)
            }

            else -> {
                logger.error("Not accept cmd type {} from cmd meta {}", cmdMeta.cmdType, cmdMeta)
                return call.respond(
                    HttpStatusCode.BadRequest,
                    jsonFactory.objectNode().put("message", "not accept cmd type ${cmdMeta.cmdType}")
                )
            }
        }
    }

    private suspend fun processRemoveLayerCmd(
        call: ApplicationCall,
        cmdMeta: CmdMetaProto,
        cmdDataArrBuf: ByteArray,
        globalId: String
    ) {
        try {
            dbS.removeLayer(cmdMeta.targetId, globalId).await()
            call.respond(
                HttpStatusCode.OK,
                jsonFactory.objectNode().put("message", "success")
            )
        } catch (t: Throwable) {
            logger.error("doc {} remove layer {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to remove layer: ${t.message}")
            )
        }
    }

    private suspend fun processRemoveDocCmd(
        call: ApplicationCall,
        cmdMeta: CmdMetaProto,
        cmdDataArrBuf: ByteArray,
        globalId: String
    ) {
        try {
            // do something
            call.respond(
                HttpStatusCode.OK,
                jsonFactory.objectNode().put("message", "success")
            )
        } catch (t: Throwable) {
            call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to remove doc: ${t.message}")
            )
        }
    }

    private suspend fun processRemoveObjectCmd(
        call: ApplicationCall,
        cmdMeta: CmdMetaProto,
        cmdDataArrBuf: ByteArray,
        globalId: String
    ) {
        val removeCmd = try {
            FreedrawingCmd.RemoveFreedrawingObjCmdProto.parseFrom(cmdDataArrBuf)
        } catch (t: Throwable) {
            logger.error("doc {} remove doc {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to parse RemoveFreedrawingObjCmdProto: ${t.message}")
            )
        }

        try {
            dbS.removeObject(removeCmd.objId, removeCmd.layerId, globalId).await()
            call.respond(
                HttpStatusCode.OK,
                jsonFactory.objectNode().put("message", "success")
            )
        } catch (t: Throwable) {
            logger.error("doc {} remove {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to remove object: ${t.message}")
            )
        }
    }

    private suspend fun processInsertPartialEraserObjCmd(
        call: ApplicationCall,
        cmdMeta: CmdMetaProto,
        cmdDataArrBuf: ByteArray,
        globalId: String
    ) {
        val insertCmd = try {
            FreedrawingCmd.InsertPartialEraserObjCmdProto.parseFrom(cmdDataArrBuf)
        } catch (t: Throwable) {
            logger.error("doc {} insert eraser {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to parse InsertPartialEraserObjCmdProto: ${t.message}")
            )
        }

        val eraser = try {
            buildPartialEraserObjFromCmd(cmdMeta, insertCmd)
        } catch (t: Throwable) {
            logger.error("doc {} insert eraser {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.BadRequest,
                jsonFactory.objectNode().put("message", "failed to create EraserObject: ${t.message}")
            )
        }

        try {
            dbS.insertEraserObject(eraser, insertCmd.layerId, globalId, cmdMeta.sequence).await()
            call.respond(
                HttpStatusCode.OK,
                jsonFactory.objectNode().put("message", "success")
            )
        } catch (t: Throwable) {
            logger.error("doc {} insert eraser {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to insert EraserObject: ${t.message}")
            )
        }
    }

    private suspend fun processInsertPartialPencilObjCmd(
        call: ApplicationCall,
        cmdMeta: CmdMetaProto,
        cmdDataArrBuf: ByteArray,
        globalId: String
    ) {
        val insertCmd = try {
            FreedrawingCmd.InsertPartialPencilObjCmdProto.parseFrom(cmdDataArrBuf)
        } catch (t: Throwable) {
            logger.error("doc {} insert pen {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to parse InsertPartialPencilObjCmdProto: ${t.message}")
            )
        }

        val pencil = try {
            buildPartialPencilObjFromCmd(cmdMeta, insertCmd)
        } catch (t: Throwable) {
            logger.error("doc {} insert pen {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.BadRequest,
                jsonFactory.objectNode().put("message", "failed to create PencilObject: ${t.message}")
            )
        }

        try {
            dbS.insertPencilObject(pencil, insertCmd.layerId, globalId, cmdMeta.sequence).await()
            call.respond(
                HttpStatusCode.OK,
                jsonFactory.objectNode().put("message", "success")
            )
        } catch (t: Throwable) {
            logger.error("doc {} insert pen {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to insert PencilObject: ${t.message}")
            )
        }
    }

    private suspend fun processInsertTextObjCmd(
        call: ApplicationCall,
        cmdMeta: CmdMetaProto,
        cmdDataArrBuf: ByteArray,
        globalId: String
    ) {
        val insertCmd = try {
            FreedrawingCmd.InsertTextObjCmdProto.parseFrom(cmdDataArrBuf)
        } catch (t: Throwable) {
            logger.error("doc {} insert text {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to parse InsertTextObjCmdProto: ${t.message}")
            )
        }

        val textObject = try {
            buildTextObjFromCmd(cmdMeta, insertCmd)
        } catch (t: Throwable) {
            logger.error("doc {} insert text {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.BadRequest,
                jsonFactory.objectNode().put("message", "failed to create TextObject: ${t.message}")
            )
        }

        try {
            dbS.insertTextObject(textObject, insertCmd.layerId, globalId, cmdMeta.sequence).await()
            call.respond(
                HttpStatusCode.OK,
                jsonFactory.objectNode().put("message", "success")
            )
        } catch (t: Throwable) {
            logger.error("doc {} insert text {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to insert TextObject: ${t.message}")
            )
        }
    }

    private suspend fun processInsertShapeObjCmd(
        call: ApplicationCall,
        cmdMeta: CmdMetaProto,
        cmdDataArrBuf: ByteArray,
        globalId: String
    ) {
        val insertCmd = try {
            FreedrawingCmd.InsertShapeObjCmdProto.parseFrom(cmdDataArrBuf)
        } catch (t: Throwable) {
            logger.error("doc {} insert sharp {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to parse InsertShapeObjCmdProto: ${t.message}")
            )
        }

        val shape = try {
            buildShapeObjFromCmd(cmdMeta, insertCmd)
        } catch (t: Throwable) {
            logger.error("doc {} insert sharp {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.BadRequest,
                jsonFactory.objectNode().put("message", "failed to create ShapeObject: ${t.message}")
            )
        }

        try {
            dbS.insertShapeObject(shape, insertCmd.layerId, globalId, cmdMeta.sequence).await()
            call.respond(
                HttpStatusCode.OK,
                jsonFactory.objectNode().put("message", "success")
            )
        } catch (t: Throwable) {
            logger.error("doc {} insert sharp {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to insert ShapeObject: ${t.message}")
            )
        }
    }

    private suspend fun processInsertLayerCmd(
        call: ApplicationCall,
        cmdMeta: CmdMetaProto,
        cmdDataArrBuf: ByteArray,
        globalId: String
    ) {
        try {
            logger.info("Inserting layer")
            dbS.insertLayer(FreedrawingLayer(cmdMeta.targetId), globalId, cmdMeta.sequence).await()

            call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            logger.error("doc {} insert layer {} exception ... ", globalId, cmdMeta.targetId, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to insert layer: ${t.message}")
            )
        }
    }

    suspend fun duplicateDocuments(call: ApplicationCall) {
        val docIds = call.receive<List<String>>()

        val original = try {
            dbS.loadDocuments(docIds).await()
        } catch (t: Throwable) {
            logger.error("load document {} exception ... ", docIds, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "Failed to load documents: ${t.message}")
            )
        }

        // duplicate docs
        val duplicated = original.map { FreedrawingDocument(0, it.layers, ObjectId().toHexString()) }

        val result = try {
            dbS.insertDocuments(duplicated).map { it.insertedIds }.await()
                .map { docIds[it.key] to it.value.asObjectId().value.toHexString() }
                .associate { it }
        } catch (t: Throwable) {
            logger.error("exception when duplicate documents {} ...", docIds, t)
            return call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "Failed to insert documents: ${t.message}")
            )
        }

        logger.info("duplicated document {}", result)
        call.respond(HttpStatusCode.OK, mapOf("mapping" to result))
    }

    suspend fun createDoc(call: ApplicationCall) {

        logger.info("Request received to create new document")

        try {
            val document = FreedrawingDocument(0)
            dbS.insertDocument(document).await()
            call.respond(document.id)
        } catch (t: Throwable) {
            logger.error("exception when create document ...", t)
            call.respond(
                HttpStatusCode.InternalServerError,
                jsonFactory.objectNode().put("message", "failed to insert document: ${t.message}")
            )
        }
    }
}
