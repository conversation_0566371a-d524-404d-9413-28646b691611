package viclass.editor.freedrawing.utils

import CmdProto.CmdMeta.CmdMetaProto
import viclass.editor.freedrawing.pojo.*
import viclass.proto.freedrawing.cmd.FreedrawingCmd
import viclass.proto.freedrawing.msg.FreedrawingMsg

fun buildShapeObjFromCmd(
    cmdMeta: CmdMetaProto,
    insertCmd: FreedrawingCmd.InsertShapeObjCmdProto
): ShapeObject {
    val shapeType = ShapeType.values().find { s -> s.id == insertCmd.shapeObj.toolType }
        ?: throw Exception("invalid shape type")

    return ShapeObject(
        insertCmd.shapeObj.localId,
        shapeType,
        convertProtoToToolState(insertCmd.shapeObj.toolState),
        convertProtoToBoundary(insertCmd.shapeObj.boundary),
    )
}

fun buildPartialPencilObjFromCmd(
    cmdMeta: CmdMetaProto,
    insertCmd: FreedrawingCmd.InsertPartialPencilObjCmdProto
): PencilObject {
    return PencilObject(
        cmdMeta.targetId,
        insertCmd.pencilObj.pointsList.flatMap { convertProtoToPosition(it) },
        convertProtoToToolState(insertCmd.pencilObj.toolState)
    )
}

fun buildPartialEraserObjFromCmd(
    cmdMeta: CmdMetaProto,
    insertCmd: FreedrawingCmd.InsertPartialEraserObjCmdProto
): EraserObject {
    return EraserObject(
        cmdMeta.targetId,
        insertCmd.eraserObj.pointsList.flatMap { convertProtoToPosition(it) },
        convertProtoToToolState(insertCmd.eraserObj.toolState)
    )
}

fun buildTextObjFromCmd(
    cmdMeta: CmdMetaProto,
    insertCmd: FreedrawingCmd.InsertTextObjCmdProto
): TextObject {
    return TextObject(
        cmdMeta.targetId,
        insertCmd.textObj.content,
        convertProtoToToolState(insertCmd.textObj.toolState),
        convertProtoToBoundary(insertCmd.textObj.boundary)
    )
}

fun convertDocumentToProto(doc: FreedrawingDocument): FreedrawingMsg.DocumentProto {
    val layers = doc.layers.map { convertLayerToProto(it) }

    return FreedrawingMsg.DocumentProto.newBuilder()
        .setGlobalId(doc.id)
        .addAllLayers(layers)
        .setVersion(doc.version)
        .build()
}

fun convertLayerToProto(layer: FreedrawingLayer): FreedrawingMsg.LayerProto {
    val objects = layer.objects.map {
        convertObjectToProto(it)
    }

    return FreedrawingMsg.LayerProto.newBuilder()
        .setLocalId(layer.localId)
        .addAllObjects(objects)
        .build()
}

fun convertObjectToProto(obj: FreedrawingObject): FreedrawingMsg.ObjectProto {
    val builder = when (obj) {
        is ShapeObject -> {
            val proto = convertShapeToProto(obj)
            FreedrawingMsg.ObjectProto.newBuilder().setShape(proto)
        }
        is TextObject -> {
            val proto = convertTextToProto(obj)
            FreedrawingMsg.ObjectProto.newBuilder().setText(proto)
        }
        is PencilObject -> {
            val proto = convertPencilToProto(obj)
            FreedrawingMsg.ObjectProto.newBuilder().setPencil(proto)
        }
        is EraserObject -> {
            val proto = convertEraserToProto(obj)
            FreedrawingMsg.ObjectProto.newBuilder().setEraser(proto)
        }
        else -> throw Exception("not support object type ${obj.javaClass}")
    }

    return builder.build()
}

fun convertEraserToProto(obj: EraserObject): FreedrawingMsg.EraserObjectProto {
    val points = obj.points.chunked(2) {
        FreedrawingMsg.PositionProto.newBuilder()
            .setX(it.component1())
            .setY(it.component2())
            .build()
    }

    return FreedrawingMsg.EraserObjectProto.newBuilder()
        .setLocalId(obj.localId)
        .setToolState(convertToolStateToProto(obj.state))
        .addAllPoints(points)
        .build()
}

fun convertPencilToProto(obj: PencilObject): FreedrawingMsg.PencilObjectProto {
    val points = obj.points.chunked(2) {
        FreedrawingMsg.PositionProto.newBuilder()
            .setX(it.component1())
            .setY(it.component2())
            .build()
    }

    return FreedrawingMsg.PencilObjectProto.newBuilder()
        .setLocalId(obj.localId)
        .setToolState(convertToolStateToProto(obj.state))
        .addAllPoints(points)
        .build()
}

fun convertTextToProto(obj: TextObject): FreedrawingMsg.TextObjectProto {


    return FreedrawingMsg.TextObjectProto.newBuilder()
        .setLocalId(obj.localId)
        .setContent(obj.content)
        .setBoundary(convertBoundaryToProto(obj.boundary))
        .setToolState(convertToolStateToProto(obj.state))
        .build()
}

fun convertShapeToProto(obj: ShapeObject): FreedrawingMsg.ShapeObjectProto {
    return FreedrawingMsg.ShapeObjectProto.newBuilder()
        .setBoundary(convertBoundaryToProto(obj.boundary))
        .setToolState(convertToolStateToProto(obj.state))
        .setLocalId(obj.localId)
        .setToolType(obj.shapeType.id)
        .build()
}

fun convertBoundaryToProto(boundary: List<Double>): FreedrawingMsg.BoundaryProto {
    val positions = boundary.chunked(2) {
        FreedrawingMsg.PositionProto.newBuilder()
            .setX(it.component1())
            .setY(it.component2())
            .build()
    }

    return FreedrawingMsg.BoundaryProto.newBuilder()
        .setStart(positions.component1())
        .setEnd(positions.component2())
        .build()
}

fun convertToolStateToProto(state: TextToolState): FreedrawingMsg.TextToolStateProto {
    val builder = FreedrawingMsg.TextToolStateProto.newBuilder()
        .setFont(state.font)
        .setFontSize(state.fontSize)
        .setFontColor(state.fontColor)
        .setFontStyle(state.fontStyle)
        .setFontWeight(state.fontWeight)
        .setTextDecoration(state.textDecoration)

    if (state.fill != null) {
        builder.fillColor = state.fill
    }

    if (state.stroke != null) {
        builder.strokeColor = state.stroke
    }

    return builder.build()
}

fun convertToolStateToProto(state: CommonToolState): FreedrawingMsg.CommonToolStateProto {
    val builder = FreedrawingMsg.CommonToolStateProto.newBuilder()
        .setLineWidth(state.lineWidth)

    if (state.fill != null) {
        builder.fillColor = state.fill
    }

    if (state.stroke != null) {
        builder.strokeColor = state.stroke
    }

    return builder.build()
}

fun convertToolStateToProto(state: EraserToolState): FreedrawingMsg.EraserToolStateProto {
    return FreedrawingMsg.EraserToolStateProto.newBuilder().setSize(state.size).build()
}

fun convertProtoToBoundary(proto: FreedrawingMsg.BoundaryProto): List<Double> {
    val start = convertProtoToPosition(proto.start)
    val end = convertProtoToPosition(proto.end)
    return listOf(*start.toTypedArray(), *end.toTypedArray())
}

fun convertProtoToPosition(proto: FreedrawingMsg.PositionProto): List<Double> {
    return listOf(proto.x, proto.y)
}

fun convertProtoToToolState(proto: FreedrawingMsg.CommonToolStateProto): CommonToolState {
    return CommonToolState(
        proto.lineWidth,
        if (proto.hasStrokeColor()) proto.strokeColor else null,
        if (proto.hasFillColor()) proto.fillColor else null
    )
}

fun convertProtoToToolState(proto: FreedrawingMsg.EraserToolStateProto): EraserToolState {
    return EraserToolState(proto.size)
}

fun convertProtoToToolState(proto: FreedrawingMsg.TextToolStateProto): TextToolState {
    return TextToolState(
        proto.font, proto.fontSize, proto.fontStyle, proto.fontWeight, proto.fontColor, proto.textDecoration,
        if (proto.hasStrokeColor()) proto.strokeColor else null,
        if (proto.hasFillColor()) proto.fillColor else null
    )
}
