package viclass.editor.freedrawing

import io.ktor.server.application.*
import io.ktor.server.routing.*
import org.koin.ktor.ext.get
import viclass.editor.freedrawing.controllers.FreedrawingController

fun Application.freedrawingRoutes(
) {
    val freedrawingCtrl: FreedrawingController = get()

    routing {
        post("/documents/delete") {
            freedrawingCtrl.deleteDocuments(call)
        }

        get("/document/fetch") {
            freedrawingCtrl.getDocument(call)
        }

        post("/cmd") {
            freedrawingCtrl.processCmd(call)
        }

        post("/documents/duplicate") {
            freedrawingCtrl.duplicateDocuments(call)
        }

        post("/document/create") {
            freedrawingCtrl.createDoc(call)
        }
    }
}