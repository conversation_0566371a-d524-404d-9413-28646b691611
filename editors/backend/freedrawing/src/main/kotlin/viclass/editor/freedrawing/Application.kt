package viclass.editor.freedrawing

import io.ktor.http.*
import io.ktor.serialization.jackson.*
import io.ktor.server.application.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.plugins.cors.routing.*
import io.ktor.server.routing.*
import org.koin.core.context.GlobalContext.startKoin
import org.koin.ktor.ext.get
import viclass.editor.freedrawing.controllers.FreedrawingController
import viclass.editor.freedrawing.koin.koinApplication

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

fun Application.module(testing: Boolean = false) {

    install(ContentNegotiation) {
        jackson()
    }

    startKoin(koinApplication)

    install(CORS) {
        allowHost("*", listOf("http", "https"))
        allowMethod(HttpMethod.Get)
        allowMethod(HttpMethod.Post)
        allowMethod(HttpMethod.Options)
        allowCredentials = true
        allowHeader(HttpHeaders.ContentType)
    }

    freedrawingRoutes()
}
