package viclass.editor.freedrawing.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId

data class FreedrawingDocument @BsonCreator constructor(
    @BsonProperty("version")
    var version: Int,

    @BsonProperty("layers")
    val layers: List<FreedrawingLayer> = emptyList(),

    @BsonId
    @BsonRepresentation(BsonType.OBJECT_ID)
    var id: String = ObjectId().toHexString(),
)
