package viclass.editor.freedrawing.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "ShapeObject", key = "objectType")
data class ShapeObject @BsonCreator constructor(
    @BsonProperty("localId")
    override val localId: Int,

    @BsonProperty("shapeType")
    val shapeType: ShapeType,

    @BsonProperty("state")
    val state: CommonToolState,

    @BsonProperty("boundary")
    val boundary: List<Double>,
): FreedrawingObject
