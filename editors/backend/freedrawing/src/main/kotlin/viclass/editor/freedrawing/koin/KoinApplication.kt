package viclass.editor.freedrawing.koin

import com.fasterxml.jackson.databind.node.JsonNodeFactory
import org.koin.dsl.module
import org.koin.ksp.generated.module
import viclass.editor.freedrawing.controllers.FreedrawingController
import org.koin.dsl.koinApplication as _koinApplication

val koinApplication = _koinApplication {
    modules(
        configurationsModule,
        DatabaseModule().module,
        module {
            single<JsonNodeFactory> { JsonNodeFactory.instance }
            single<FreedrawingController> { FreedrawingController(get(), get()) }
        }
    )
}