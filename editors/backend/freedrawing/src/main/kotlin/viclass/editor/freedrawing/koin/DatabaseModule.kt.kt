package viclass.editor.freedrawing.koin

import com.google.common.reflect.ClassPath
import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import common.libs.codec.EnumCodecProvider
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import viclass.editor.freedrawing.config.ConfigBean
import viclass.editor.freedrawing.config.DatabaseConfig
import viclass.editor.freedrawing.db.FreedrawingDBS
import viclass.editor.freedrawing.pojo.FreedrawingDocument
import java.util.stream.Collectors

fun loadAllBsonDiscriminatorClasses(packages: List<String>): List<Class<*>> {
    return try {
        ClassPath.from(Thread.currentThread().contextClassLoader).allClasses.stream()
            .filter { c: ClassPath.ClassInfo -> packages.contains(c.packageName) }
            .map { c: ClassPath.ClassInfo -> c.load() }.filter { it.isAnnotationPresent(BsonDiscriminator::class.java) }
            .collect(Collectors.toList())
    } catch (t: Throwable) {
        emptyList()
    }
}

@Module
class DatabaseModule {
    @Singleton
    fun provideCodecRegistry(): CodecRegistry {
        return CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(), CodecRegistries.fromProviders(
                EnumCodecProvider(),
                PojoCodecProvider.builder().automatic(true).register("viclass.editor.freedrawing.pojo")
                    .register(*loadAllBsonDiscriminatorClasses(listOf("viclass.editor.freedrawing.pojo")).toTypedArray())
                    .build(),
            )
        )
    }

    @Singleton
    fun provideMongoDatabase(dbConf: DatabaseConfig, codecRegistry: CodecRegistry): MongoDatabase {
        return MongoClients.create(dbConf.connectionString)
            .getDatabase(dbConf.dbName).withCodecRegistry(codecRegistry)
    }

    @Singleton
    fun provideFreedrawingDBS(database: MongoDatabase): FreedrawingDBS {
        val col: MongoCollection<FreedrawingDocument> = database.getCollection("frd-documents", FreedrawingDocument::class.java)
        return FreedrawingDBS(col)
    }

    @Singleton
    fun provideDatabaseConfig(config: ConfigBean): DatabaseConfig {
        return config.dbConf
    }
}