package viclass.editor.freedrawing.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "EraserObject", key = "objectType")
data class EraserObject @BsonCreator constructor(
    @BsonProperty("localId")
    override val localId: Int,

    @BsonProperty("points")
    val points: List<Double> = emptyList(),

    @BsonProperty("state")
    val state: EraserToolState = EraserToolState(5),
): FreedrawingObject
