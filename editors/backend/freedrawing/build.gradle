plugins {
    id "hnct.build"
    id "kotlin"
    id "application"
    id "kotlinx-serialization"
    id "com.google.protobuf"
    id "com.google.devtools.ksp"
}

version = "1.0.0"

application {
    mainClass = 'io.ktor.server.netty.EngineMain'
}

run {
    classpath += files("conf")
}

tasks.withType(AbstractCopyTask) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {
    internal {
        implementation([id: ':common.libs', src: ['logger']], "viclass:common.libs-logger:1.0.0", true)
        implementation([id: ':common.libs', src: ['codec']], "viclass:common.libs-codec:1.0.0", true)

        implementation([id: ':feature.common', src: ['main']], "viclass:feature.common:1.0.0", true)
    }

    // Koin
    ksp "io.insert-koin:koin-ksp-compiler:$koinKspVs"
    implementation "io.insert-koin:koin-annotations:$koinKspVs"
    implementation "io.insert-koin:koin-core:$koinVs"
    implementation "io.insert-koin:koin-ktor:$koinVs"
    implementation "io.insert-koin:koin-logger-slf4j:$koinVs"
    implementation "io.ktor:ktor-server-core-jvm"
    // implementation "org.koin:koin-java:2.0.1"

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-rx3:$coroutineVs"
    // implementation "org.jetbrains.kotlinx:kotlinx-coroutines-jdk15:$coroutineVs"
    implementation "io.reactivex.rxjava3:rxkotlin:$rxKotlinVs"

    implementation "jayeson:jayeson.lib.utility:2.1.0"


    // -------- KTOR -------------
    implementation "io.ktor:ktor-server-core:$ktorVs"

    implementation "io.ktor:ktor-server-content-negotiation:$ktorVs"
    implementation "io.ktor:ktor-serialization-jackson:$ktorVs"

    implementation "io.ktor:ktor-server-netty:$ktorVs"
    implementation "io.ktor:ktor-server-cors:$ktorVs"
    // -----------------------------

    implementation "com.google.protobuf:protobuf-java:$protobufVs"

    implementation 'com.google.guava:guava:31.0.1-jre'

    implementation "org.mongodb:mongodb-driver-reactivestreams:$mongoRxDriverVs"

    testImplementation 'junit:junit:4.13.2'
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVs}"
    }

    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:1.41.0${platform}"
        }
    }
    generateProtoTasks {
        all().each { task->
            task.builtins {
                java {
//                    option '--experimental_allow_proto3_optional'
                }
            }
            task.plugins {
                grpc {}
            }
        }
    }
}
