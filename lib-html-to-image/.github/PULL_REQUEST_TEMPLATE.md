<!--- Provide a general summary of your changes in the Title above -->

### Description

<!--- Describe your changes in detail -->

### Motivation and Context

<!--- Why is this change required? What problem does it solve? -->
<!--- If it fixes an open issue, please link to the issue here. -->
<!--- GIF or snapshot should be provided if includes UI/interactive modification. -->
<!--- How to fix the problem, and list final API implementation and usage sample if that is an new feature. -->

### Types of changes

<!--- What types of changes does your code introduce? Put an `x` in all the boxes that apply: -->

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to change)
- [ ] Enhancement (changes that improvement of current feature or performance)
- [ ] Refactoring (changes that neither fixes a bug nor adds a feature)
- [ ] Test Case (changes that add missing tests or correct existing tests)
- [ ] Code style optimization (changes that do not affect the meaning of the code)
- [ ] Docs (changes that only update documentation)
- [ ] Chore (changes that don't modify src or test files)

### Self Check before Merge

<!--- Go over all the following points, and put an `x` in all the boxes that apply. -->
<!--- If you're unsure about any of these, don't hesitate to ask. We're here to help! -->

- [ ] My code follows the code style of this project.
- [ ] My change requires a change to the documentation.
- [ ] I have updated the documentation accordingly.
- [ ] I have read the [**CONTRIBUTING**](https://github.com/bubkoo/html-to-image/blob/master/CONTRIBUTING.md) document.
- [ ] I have added tests to cover my changes.
- [ ] All new and existing tests passed.
