name: 🤝　Update Contributors
on:
  schedule:
    - cron: '0 1 * * *'
  push:
    branches:
      - master
      - alpha
      - beta
jobs:
  contributors:
    runs-on: ubuntu-latest
    steps:
      - uses: bubkoo/use-app-token@v1
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}
          env_name: bot_token
      - uses: bubkoo/contributors-list@v1
        with:
          GITHUB_TOKEN: ${{ env.bot_token }}
          excludeUsers: semantic-release-bot ImgBotApp
