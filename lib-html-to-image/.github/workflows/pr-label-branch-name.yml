name: 🏷️　Label(Branch Name)
on:
  pull_request_target:
    types: [opened]
jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - uses: bubkoo/use-app-token@v1
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}
          variable_name: bot_token
      - uses: TimonVS/pr-labeler-action@v3
        with:
          configuration-path: .github/workflows/config/pr-label-branch-name.yml
        env:
          GITHUB_TOKEN: ${{ env.bot_token }}
