name: ⛔️　Lock Threads
on:
  schedule:
    - cron: 0 0 * * *
jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - uses: bubkoo/use-app-token@v1
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}
          variable_name: bot_token
      - uses: dessant/lock-threads@v2
        with:
          github-token: ${{ env.bot_token }}
          issue-lock-inactive-days: 365
          issue-lock-comment: >
            This thread has been automatically locked because it has not had recent
            activity. Please open a new issue for related bugs and link to relevant
            comments in this thread.
          process-only: issues
