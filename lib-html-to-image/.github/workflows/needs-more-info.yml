name: 🚨　Needs More Info
on:
  pull_request_target:
    types: [opened]
  issues:
    types: [opened]
jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - uses: bubkoo/use-app-token@v1
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}
          variable_name: bot_token
      - uses: bubkoo/needs-more-info@v1
        with:
          GITHUB_TOKEN: ${{ env.bot_token }}
          CONFIG_FILE: .github/workflows/config/needs-more-info.yml

